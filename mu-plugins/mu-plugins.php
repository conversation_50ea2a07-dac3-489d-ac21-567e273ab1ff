<?php

/**
 * Plugin Name: Must Use Plugins
 * Description: Must-use plugins (a.k.a. mu-plugins) are plugins installed in a special directory inside the content folder and which are automatically enabled on all sites in the installation.
 * Version: 1.0.0
 * Author: Brandfast
 * License: MIT
 */

// Simple mu-plugins autoloader
if (is_blog_installed()) {
    // Load individual mu-plugin files
    $mu_plugins_dir = __DIR__;

    // Load specific mu-plugins
    $mu_plugin_files = [
        'mailhog.php',
        'spinupwp-debug-log-path.php',
    ];

    foreach ($mu_plugin_files as $file) {
        $file_path = $mu_plugins_dir . '/' . $file;
        if (file_exists($file_path)) {
            require_once $file_path;
        }
    }

    // Load mu-plugins from subdirectories
    $subdir_plugins = [
        'clean-image-filenames/clean-image-filenames.php',
        'headache/headache.php',
        'vistrom-wordplate-modifications/vistrom-wordplate-modifications.php',
    ];

    foreach ($subdir_plugins as $plugin_file) {
        $file_path = $mu_plugins_dir . '/' . $plugin_file;
        if (file_exists($file_path)) {
            require_once $file_path;
        }
    }
}
