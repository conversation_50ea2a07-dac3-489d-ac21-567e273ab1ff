<?php

/**
 * Core wordpress file list
 *
 * @package   Duplicator
 * @copyright (c) 2022, Snap Creek LLC
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 */

defined('ABSPATH') || defined('DUPXABSPATH') || exit;

/*
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 */

// @phpstan-ignore-next-line
self::$corePathList = array(
    'wp-login.php'         => "f",
    'wp-cron.php'          => "f",
    'wp-mail.php'          => "f",
    'wp-blog-header.php'   => "f",
    'license.txt'          => "f",
    'wp-activate.php'      => "f",
    'wp-settings.php'      => "f",
    'wp-config-sample.php' => "f",
    'readme.html'          => "f",
    'wp-comments-post.php' => "f",
    'wp-signup.php'        => "f",
    'wp-load.php'          => "f",
    'index.php'            => "f",
    'xmlrpc.php'           => "f",
    'wp-trackback.php'     => "f",
    'wp-links-opml.php'    => "f",
    'wp-admin'             => array(
        'import.php'                => "f",
        'admin.php'                 => "f",
        'site-editor.php'           => "f",
        'ms-options.php'            => "f",
        'update.php'                => "f",
        'custom-background.php'     => "f",
        'privacy-policy-guide.php'  => "f",
        'user-new.php'              => "f",
        'privacy.php'               => "f",
        'link-manager.php'          => "f",
        'upgrade.php'               => "f",
        'tools.php'                 => "f",
        'post-new.php'              => "f",
        'update-core.php'           => "f",
        'options-head.php'          => "f",
        'js'                        => array(
            'tags-suggest.min.js'            => "f",
            'auth-app.js'                    => "f",
            'media-gallery.min.js'           => "f",
            'press-this.js'                  => "f",
            'image-edit.js'                  => "f",
            'editor.min.js'                  => "f",
            'inline-edit-post.js'            => "f",
            'custom-background.min.js'       => "f",
            'user-profile.min.js'            => "f",
            'dashboard.js'                   => "f",
            'theme.js'                       => "f",
            'media-gallery.js'               => "f",
            'site-health.min.js'             => "f",
            'set-post-thumbnail.min.js'      => "f",
            'application-passwords.min.js'   => "f",
            'customize-nav-menus.js'         => "f",
            'common.js'                      => "f",
            'media-upload.min.js'            => "f",
            'password-strength-meter.min.js' => "f",
            'wp-fullscreen-stub.js'          => "f",
            'link.js'                        => "f",
            'updates.js'                     => "f",
            'customize-widgets.min.js'       => "f",
            'revisions.min.js'               => "f",
            'color-picker.min.js'            => "f",
            'inline-edit-tax.min.js'         => "f",
            'bookmarklet.min.js'             => "f",
            'widgets'                        => array(
                'media-audio-widget.min.js'   => "f",
                'custom-html-widgets.min.js'  => "f",
                'text-widgets.min.js'         => "f",
                'media-video-widget.min.js'   => "f",
                'media-widgets.js'            => "f",
                'media-video-widget.js'       => "f",
                'custom-html-widgets.js'      => "f",
                'media-widgets.min.js'        => "f",
                'media-image-widget.min.js'   => "f",
                'text-widgets.js'             => "f",
                'media-gallery-widget.js'     => "f",
                'media-image-widget.js'       => "f",
                'media-gallery-widget.min.js' => "f",
                'media-audio-widget.js'       => "f",
            ),
            'customize-widgets.js'           => "f",
            'language-chooser.min.js'        => "f",
            'updates.min.js'                 => "f",
            'iris.min.js'                    => "f",
            'color-picker.js'                => "f",
            'editor.js'                      => "f",
            'wp-fullscreen.js'               => "f",
            'media.min.js'                   => "f",
            'wp-fullscreen.min.js'           => "f",
            'post.min.js'                    => "f",
            'nav-menu.js'                    => "f",
            'svg-painter.min.js'             => "f",
            'theme-plugin-editor.min.js'     => "f",
            'user-suggest.min.js'            => "f",
            'tags-box.js'                    => "f",
            'media.js'                       => "f",
            'media-upload.js'                => "f",
            'site-icon.min.js'               => "f",
            'inline-edit-tax.js'             => "f",
            'custom-header.js'               => "f",
            'editor-expand.min.js'           => "f",
            'widgets.js'                     => "f",
            'edit-comments.js'               => "f",
            'gallery.js'                     => "f",
            'accordion.js'                   => "f",
            'tags-suggest.js'                => "f",
            'svg-painter.js'                 => "f",
            'password-toggle.js'             => "f",
            'postbox.min.js'                 => "f",
            'tags.js'                        => "f",
            'widgets.min.js'                 => "f",
            'post.js'                        => "f",
            'xfn.min.js'                     => "f",
            'nav-menu.min.js'                => "f",
            'tags-box.min.js'                => "f",
            'comment.min.js'                 => "f",
            'language-chooser.js'            => "f",
            'custom-background.js'           => "f",
            'customize-nav-menus.min.js'     => "f",
            'farbtastic.js'                  => "f",
            'plugin-install.min.js'          => "f",
            'tags.min.js'                    => "f",
            'privacy-tools.js'               => "f",
            'word-count.js'                  => "f",
            'inline-edit-post.min.js'        => "f",
            'auth-app.min.js'                => "f",
            'edit-comments.min.js'           => "f",
            'code-editor.min.js'             => "f",
            'user-suggest.js'                => "f",
            'theme-plugin-editor.js'         => "f",
            'site-icon.js'                   => "f",
            'common.min.js'                  => "f",
            'user-profile.js'                => "f",
            'wp-fullscreen-stub.min.js'      => "f",
            'postbox.js'                     => "f",
            'password-toggle.min.js'         => "f",
            'press-this.min.js'              => "f",
            'xfn.js'                         => "f",
            'link.min.js'                    => "f",
            'code-editor.js'                 => "f",
            'comment.js'                     => "f",
            'customize-controls.min.js'      => "f",
            'plugin-install.js'              => "f",
            'privacy-tools.min.js'           => "f",
            'application-passwords.js'       => "f",
            'site-health.js'                 => "f",
            'set-post-thumbnail.js'          => "f",
            'dashboard.min.js'               => "f",
            'bookmarklet.js'                 => "f",
            'gallery.min.js'                 => "f",
            'revisions.js'                   => "f",
            'theme.min.js'                   => "f",
            'word-count.min.js'              => "f",
            'accordion.min.js'               => "f",
            'customize-controls.js'          => "f",
            'password-strength-meter.js'     => "f",
            'editor-expand.js'               => "f",
            'image-edit.min.js'              => "f",
        ),
        'link.php'                  => "f",
        'ms-sites.php'              => "f",
        'upgrade-functions.php'     => "f",
        'plugin-install.php'        => "f",
        'widgets-form-blocks.php'   => "f",
        'media-new.php'             => "f",
        'options-permalink.php'     => "f",
        'load-scripts.php'          => "f",
        'theme-install.php'         => "f",
        'plugin-editor.php'         => "f",
        'post.php'                  => "f",
        'term.php'                  => "f",
        'my-sites.php'              => "f",
        'customize.php'             => "f",
        'edit-form-blocks.php'      => "f",
        'upload.php'                => "f",
        'images'                    => array(
            'freedom-2.svg'                => "f",
            'browser-rtl.png'              => "f",
            'about-texture.png'            => "f",
            'spinner.gif'                  => "f",
            'media-button.png'             => "f",
            'browser.png'                  => "f",
            'align-none-2x.png'            => "f",
            'contribute-code.svg'          => "f",
            'dashboard-background.svg'     => "f",
            'menu.png'                     => "f",
            'align-left-2x.png'            => "f",
            'align-right-2x.png'           => "f",
            'generic.png'                  => "f",
            'imgedit-icons.png'            => "f",
            'icons32.png'                  => "f",
            'sort.gif'                     => "f",
            'media-button-video.gif'       => "f",
            'align-center-2x.png'          => "f",
            'list.png'                     => "f",
            'about-color-palette.svg'      => "f",
            'media-button-image.gif'       => "f",
            'post-formats.png'             => "f",
            'menu-2x.png'                  => "f",
            'arrows-2x.png'                => "f",
            'contribute-main.svg'          => "f",
            'resize-2x.gif'                => "f",
            'about-header-contribute.svg'  => "f",
            'wordpress-logo.svg'           => "f",
            'marker.png'                   => "f",
            'media-button-2x.png'          => "f",
            'list-2x.png'                  => "f",
            'no.png'                       => "f",
            'about-header-background.svg'  => "f",
            'se.png'                       => "f",
            'icons32-2x.png'               => "f",
            'icons32-vs.png'               => "f",
            'post-formats32-vs.png'        => "f",
            'wheel.png'                    => "f",
            'align-none.png'               => "f",
            'freedom-3.svg'                => "f",
            'mask.png'                     => "f",
            'resize.gif'                   => "f",
            'post-formats-vs.png'          => "f",
            'wordpress-logo-white.svg'     => "f",
            'menu-vs.png'                  => "f",
            'wpspin_light-2x.gif'          => "f",
            'icons32-vs-2x.png'            => "f",
            'stars.png'                    => "f",
            'sort-2x.gif'                  => "f",
            'about-badge.svg'              => "f",
            'wpspin_light.gif'             => "f",
            'about-header-privacy.svg'     => "f",
            'spinner-2x.gif'               => "f",
            'w-logo-blue.png'              => "f",
            'arrows.png'                   => "f",
            'xit-2x.gif'                   => "f",
            'privacy.svg'                  => "f",
            'bubble_bg-2x.gif'             => "f",
            'resize-rtl-2x.gif'            => "f",
            'align-center.png'             => "f",
            'contribute-no-code.svg'       => "f",
            'post-formats32.png'           => "f",
            'media-button-music.gif'       => "f",
            'wordpress-logo.png'           => "f",
            'about-color-palette-vert.svg' => "f",
            'stars-2x.png'                 => "f",
            'about-release-badge.svg'      => "f",
            'resize-rtl.gif'               => "f",
            'loading.gif'                  => "f",
            'about-header-brushes.svg'     => "f",
            'bubble_bg.gif'                => "f",
            'comment-grey-bubble.png'      => "f",
            'freedom-4.svg'                => "f",
            'align-right.png'              => "f",
            'about-header-freedoms.svg'    => "f",
            'align-left.png'               => "f",
            'media-button-other.gif'       => "f",
            'imgedit-icons-2x.png'         => "f",
            'privacy.png'                  => "f",
            'comment-grey-bubble-2x.png'   => "f",
            'freedoms.png'                 => "f",
            'date-button.gif'              => "f",
            'freedom-1.svg'                => "f",
            'xit.gif'                      => "f",
            'date-button-2x.gif'           => "f",
            'about-header-about.svg'       => "f",
            'w-logo-white.png'             => "f",
            'about-header-credits.svg'     => "f",
            'yes.png'                      => "f",
            'menu-vs-2x.png'               => "f",
        ),
        'css'                       => array(
            'nav-menus.css'                   => "f",
            'press-this-editor-rtl.css'       => "f",
            'about-rtl.css'                   => "f",
            'widgets.min.css'                 => "f",
            'code-editor.css'                 => "f",
            'farbtastic.min.css'              => "f",
            'ie.css'                          => "f",
            'login.min.css'                   => "f",
            'forms-rtl.min.css'               => "f",
            'login.css'                       => "f",
            'press-this-editor-rtl.min.css'   => "f",
            'widgets-rtl.min.css'             => "f",
            'customize-nav-menus.min.css'     => "f",
            'site-health-rtl.css'             => "f",
            'nav-menus-rtl.css'               => "f",
            'customize-nav-menus.css'         => "f",
            'press-this-editor.css'           => "f",
            'customize-widgets-rtl.min.css'   => "f",
            'edit.min.css'                    => "f",
            'list-tables-rtl.min.css'         => "f",
            'site-health.css'                 => "f",
            'site-icon-rtl.css'               => "f",
            'press-this.css'                  => "f",
            'media-rtl.css'                   => "f",
            'about.css'                       => "f",
            'themes-rtl.min.css'              => "f",
            'site-icon-rtl.min.css'           => "f",
            'install.css'                     => "f",
            'site-health.min.css'             => "f",
            'media.min.css'                   => "f",
            'edit-rtl.min.css'                => "f",
            'site-icon.css'                   => "f",
            'list-tables.css'                 => "f",
            'farbtastic.css'                  => "f",
            'admin-menu-rtl.min.css'          => "f",
            'nav-menus.min.css'               => "f",
            'ie-rtl.min.css'                  => "f",
            'media.css'                       => "f",
            'install-rtl.css'                 => "f",
            'ie-rtl.css'                      => "f",
            'l10n-rtl.css'                    => "f",
            'common-rtl.min.css'              => "f",
            'revisions.min.css'               => "f",
            'color-picker-rtl.css'            => "f",
            'list-tables-rtl.css'             => "f",
            'revisions.css'                   => "f",
            'customize-controls-rtl.min.css'  => "f",
            'admin-menu.css'                  => "f",
            'press-this.min.css'              => "f",
            'press-this-rtl.css'              => "f",
            'site-health-rtl.min.css'         => "f",
            'media-rtl.min.css'               => "f",
            'login-rtl.min.css'               => "f",
            'customize-nav-menus-rtl.css'     => "f",
            'about.min.css'                   => "f",
            'color-picker-rtl.min.css'        => "f",
            'admin-menu.min.css'              => "f",
            'themes.css'                      => "f",
            'color-picker.css'                => "f",
            'admin-menu-rtl.css'              => "f",
            'install-rtl.min.css'             => "f",
            'site-icon.min.css'               => "f",
            'color-picker.min.css'            => "f",
            'wp-admin.min.css'                => "f",
            'dashboard-rtl.min.css'           => "f",
            'customize-widgets.min.css'       => "f",
            'press-this-editor.min.css'       => "f",
            'customize-controls-rtl.css'      => "f",
            'deprecated-media.css'            => "f",
            'forms.css'                       => "f",
            'widgets.css'                     => "f",
            'about-rtl.min.css'               => "f",
            'nav-menus-rtl.min.css'           => "f",
            'common.css'                      => "f",
            'wp-admin-rtl.min.css'            => "f",
            'l10n.min.css'                    => "f",
            'ie.min.css'                      => "f",
            'l10n.css'                        => "f",
            'farbtastic-rtl.min.css'          => "f",
            'code-editor-rtl.css'             => "f",
            'install.min.css'                 => "f",
            'customize-controls.min.css'      => "f",
            'list-tables.min.css'             => "f",
            'code-editor-rtl.min.css'         => "f",
            'themes.min.css'                  => "f",
            'press-this-rtl.min.css'          => "f",
            'customize-nav-menus-rtl.min.css' => "f",
            'colors'                          => array(
                '_variables.scss' => "f",
                '_admin.scss'     => "f",
                'light'           => array(
                    'colors.css'         => "f",
                    'colors.min.css'     => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.css'     => "f",
                    'colors-rtl.min.css' => "f",
                ),
                'ocean'           => array(
                    'colors.css'         => "f",
                    'colors.min.css'     => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.css'     => "f",
                    'colors-rtl.min.css' => "f",
                ),
                'ectoplasm'       => array(
                    'colors.css'         => "f",
                    'colors.min.css'     => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.css'     => "f",
                    'colors-rtl.min.css' => "f",
                ),
                'coffee'          => array(
                    'colors.css'         => "f",
                    'colors.min.css'     => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.css'     => "f",
                    'colors-rtl.min.css' => "f",
                ),
                'sunrise'         => array(
                    'colors.css'         => "f",
                    'colors.min.css'     => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.css'     => "f",
                    'colors-rtl.min.css' => "f",
                ),
                '_mixins.scss'    => "f",
                'blue'            => array(
                    'colors.css'         => "f",
                    'colors.min.css'     => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.css'     => "f",
                    'colors-rtl.min.css' => "f",
                ),
                'midnight'        => array(
                    'colors.css'         => "f",
                    'colors.min.css'     => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.css'     => "f",
                    'colors-rtl.min.css' => "f",
                ),
                'modern'          => array(
                    'colors.css'         => "f",
                    'colors.min.css'     => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.css'     => "f",
                    'colors-rtl.min.css' => "f",
                ),
            ),
            'revisions-rtl.min.css'           => "f",
            'dashboard.min.css'               => "f",
            'deprecated-media.min.css'        => "f",
            'revisions-rtl.css'               => "f",
            'dashboard-rtl.css'               => "f",
            'customize-controls.css'          => "f",
            'login-rtl.css'                   => "f",
            'farbtastic-rtl.css'              => "f",
            'l10n-rtl.min.css'                => "f",
            'customize-widgets.css'           => "f",
            'themes-rtl.css'                  => "f",
            'customize-widgets-rtl.css'       => "f",
            'common.min.css'                  => "f",
            'forms.min.css'                   => "f",
            'deprecated-media-rtl.css'        => "f",
            'edit-rtl.css'                    => "f",
            'wp-admin-rtl.css'                => "f",
            'wp-admin.css'                    => "f",
            'common-rtl.css'                  => "f",
            'widgets-rtl.css'                 => "f",
            'code-editor.min.css'             => "f",
            'forms-rtl.css'                   => "f",
            'edit.css'                        => "f",
            'deprecated-media-rtl.min.css'    => "f",
            'dashboard.css'                   => "f",
        ),
        'erase-personal-data.php'   => "f",
        'media.php'                 => "f",
        'nav-menus.php'             => "f",
        'credits.php'               => "f",
        'admin-ajax.php'            => "f",
        'install.php'               => "f",
        'ms-delete-site.php'        => "f",
        'revision.php'              => "f",
        'users.php'                 => "f",
        'plugins.php'               => "f",
        'install-helper.php'        => "f",
        'widgets.php'               => "f",
        'setup-config.php'          => "f",
        'menu.php'                  => "f",
        'link-add.php'              => "f",
        'edit-tags.php'             => "f",
        'edit-link-form.php'        => "f",
        'edit-form-advanced.php'    => "f",
        'admin-header.php'          => "f",
        'maint'                     => array('repair.php' => "f"),
        'options-reading.php'       => "f",
        'export.php'                => "f",
        'ms-admin.php'              => "f",
        'options-privacy.php'       => "f",
        'edit-tag-form.php'         => "f",
        'ms-themes.php'             => "f",
        'load-styles.php'           => "f",
        'edit.php'                  => "f",
        'site-health.php'           => "f",
        'edit-comments.php'         => "f",
        'moderation.php'            => "f",
        'index.php'                 => "f",
        'async-upload.php'          => "f",
        'site-health-info.php'      => "f",
        'comment.php'               => "f",
        'widgets-form.php'          => "f",
        'contribute.php'            => "f",
        'press-this.php'            => "f",
        'options-writing.php'       => "f",
        'options-general.php'       => "f",
        'admin-functions.php'       => "f",
        'network.php'               => "f",
        'admin-post.php'            => "f",
        'profile.php'               => "f",
        'export-personal-data.php'  => "f",
        'user-edit.php'             => "f",
        'media-upload.php'          => "f",
        'ms-users.php'              => "f",
        'ms-upgrade-network.php'    => "f",
        'options-media.php'         => "f",
        'freedoms.php'              => "f",
        'authorize-application.php' => "f",
        'theme-editor.php'          => "f",
        'options-discussion.php'    => "f",
        'edit-form-comment.php'     => "f",
        'about.php'                 => "f",
        'options.php'               => "f",
        'link-parse-opml.php'       => "f",
        'admin-footer.php'          => "f",
        'network'                   => array(
            'settings.php'       => "f",
            'admin.php'          => "f",
            'update.php'         => "f",
            'site-settings.php'  => "f",
            'user-new.php'       => "f",
            'privacy.php'        => "f",
            'site-info.php'      => "f",
            'upgrade.php'        => "f",
            'update-core.php'    => "f",
            'plugin-install.php' => "f",
            'theme-install.php'  => "f",
            'plugin-editor.php'  => "f",
            'site-new.php'       => "f",
            'credits.php'        => "f",
            'site-users.php'     => "f",
            'users.php'          => "f",
            'plugins.php'        => "f",
            'menu.php'           => "f",
            'edit.php'           => "f",
            'index.php'          => "f",
            'contribute.php'     => "f",
            'sites.php'          => "f",
            'site-themes.php'    => "f",
            'profile.php'        => "f",
            'user-edit.php'      => "f",
            'freedoms.php'       => "f",
            'setup.php'          => "f",
            'theme-editor.php'   => "f",
            'about.php'          => "f",
            'themes.php'         => "f",
        ),
        'custom-header.php'         => "f",
        'user'                      => array(
            'admin.php'      => "f",
            'privacy.php'    => "f",
            'credits.php'    => "f",
            'menu.php'       => "f",
            'index.php'      => "f",
            'contribute.php' => "f",
            'profile.php'    => "f",
            'user-edit.php'  => "f",
            'freedoms.php'   => "f",
            'about.php'      => "f",
        ),
        'themes.php'                => "f",
        'ms-edit.php'               => "f",
        'includes'                  => array(
            'import.php'                                            => "f",
            'admin-filters.php'                                     => "f",
            'image-edit.php'                                        => "f",
            'class-wp-ms-themes-list-table.php'                     => "f",
            'class-wp-plugins-list-table.php'                       => "f",
            'user.php'                                              => "f",
            'admin.php'                                             => "f",
            'theme.php'                                             => "f",
            'class-wp-filesystem-direct.php'                        => "f",
            'class-wp-privacy-data-export-requests-list-table.php'  => "f",
            'update.php'                                            => "f",
            'class-wp-upgrader-skin.php'                            => "f",
            'class-wp-filesystem-base.php'                          => "f",
            'class-wp-theme-install-list-table.php'                 => "f",
            'class-wp-ms-users-list-table.php'                      => "f",
            'class-wp-ajax-upgrader-skin.php'                       => "f",
            'file.php'                                              => "f",
            'class-wp-site-health.php'                              => "f",
            'bookmark.php'                                          => "f",
            'class-wp-list-table.php'                               => "f",
            'ms-admin-filters.php'                                  => "f",
            'upgrade.php'                                           => "f",
            'class-wp-community-events.php'                         => "f",
            'class-wp-themes-list-table.php'                        => "f",
            'class-wp-post-comments-list-table.php'                 => "f",
            'class-wp-terms-list-table.php'                         => "f",
            'class-custom-background.php'                           => "f",
            'ajax-actions.php'                                      => "f",
            'class-custom-image-header.php'                         => "f",
            'update-core.php'                                       => "f",
            'class-bulk-theme-upgrader-skin.php'                    => "f",
            'class-pclzip.php'                                      => "f",
            'class-wp-links-list-table.php'                         => "f",
            'class-ftp.php'                                         => "f",
            'plugin-install.php'                                    => "f",
            'class-wp-application-passwords-list-table.php'         => "f",
            'class-walker-nav-menu-edit.php'                        => "f",
            'class-plugin-installer-skin.php'                       => "f",
            'screen.php'                                            => "f",
            'class-wp-upgrader.php'                                 => "f",
            'misc.php'                                              => "f",
            'noop.php'                                              => "f",
            'image.php'                                             => "f",
            'theme-install.php'                                     => "f",
            'post.php'                                              => "f",
            'class-wp-list-table-compat.php'                        => "f",
            'class-wp-posts-list-table.php'                         => "f",
            'class-language-pack-upgrader-skin.php'                 => "f",
            'meta-boxes.php'                                        => "f",
            'media.php'                                             => "f",
            'credits.php'                                           => "f",
            'class-wp-filesystem-ftpext.php'                        => "f",
            'revision.php'                                          => "f",
            'class-wp-upgrader-skins.php'                           => "f",
            'taxonomy.php'                                          => "f",
            'dashboard.php'                                         => "f",
            'class-wp-privacy-data-removal-requests-list-table.php' => "f",
            'class-wp-debug-data.php'                               => "f",
            'class-wp-press-this.php'                               => "f",
            'class-ftp-sockets.php'                                 => "f",
            'widgets.php'                                           => "f",
            'menu.php'                                              => "f",
            'class-wp-privacy-requests-table.php'                   => "f",
            'list-table.php'                                        => "f",
            'class-walker-category-checklist.php'                   => "f",
            'class-theme-upgrader-skin.php'                         => "f",
            'class-file-upload-upgrader.php'                        => "f",
            'export.php'                                            => "f",
            'class-wp-screen.php'                                   => "f",
            'class-automatic-upgrader-skin.php'                     => "f",
            'class-wp-ms-sites-list-table.php'                      => "f",
            'class-theme-upgrader.php'                              => "f",
            'class-core-upgrader.php'                               => "f",
            'plugin.php'                                            => "f",
            'class-wp-automatic-updater.php'                        => "f",
            'comment.php'                                           => "f",
            'class-wp-comments-list-table.php'                      => "f",
            'schema.php'                                            => "f",
            'class-wp-plugin-install-list-table.php'                => "f",
            'nav-menu.php'                                          => "f",
            'network.php'                                           => "f",
            'class-wp-privacy-policy-content.php'                   => "f",
            'edit-tag-messages.php'                                 => "f",
            'class-wp-filesystem-ssh2.php'                          => "f",
            'class-walker-nav-menu-checklist.php'                   => "f",
            'class-wp-internal-pointers.php'                        => "f",
            'class-wp-users-list-table.php'                         => "f",
            'class-bulk-plugin-upgrader-skin.php'                   => "f",
            'class-ftp-pure.php'                                    => "f",
            'ms-deprecated.php'                                     => "f",
            'ms.php'                                                => "f",
            'class-theme-installer-skin.php'                        => "f",
            'translation-install.php'                               => "f",
            'continents-cities.php'                                 => "f",
            'class-wp-site-icon.php'                                => "f",
            'class-wp-importer.php'                                 => "f",
            'class-bulk-upgrader-skin.php'                          => "f",
            'class-language-pack-upgrader.php'                      => "f",
            'class-plugin-upgrader-skin.php'                        => "f",
            'deprecated.php'                                        => "f",
            'class-wp-media-list-table.php'                         => "f",
            'options.php'                                           => "f",
            'class-plugin-upgrader.php'                             => "f",
            'class-wp-filesystem-ftpsockets.php'                    => "f",
            'class-wp-site-health-auto-updates.php'                 => "f",
            'privacy-tools.php'                                     => "f",
            'template.php'                                          => "f",
        ),
        'menu-header.php'           => "f",
    ),
    'wp-includes'          => array(
        'class-wp-customize-section.php'                 => "f",
        'Requests'                                       => array(
            'Session.php'     => "f",
            'SSL.php'         => "f",
            'Exception'       => array(
                'HTTP'          => array(
                    '417.php'     => "f",
                    '502.php'     => "f",
                    '306.php'     => "f",
                    'Unknown.php' => "f",
                    '418.php'     => "f",
                    '408.php'     => "f",
                    '504.php'     => "f",
                    '431.php'     => "f",
                    '406.php'     => "f",
                    '404.php'     => "f",
                    '409.php'     => "f",
                    '411.php'     => "f",
                    '401.php'     => "f",
                    '400.php'     => "f",
                    '405.php'     => "f",
                    '500.php'     => "f",
                    '429.php'     => "f",
                    '402.php'     => "f",
                    '505.php'     => "f",
                    '413.php'     => "f",
                    '305.php'     => "f",
                    '412.php'     => "f",
                    '304.php'     => "f",
                    '428.php'     => "f",
                    '416.php'     => "f",
                    '414.php'     => "f",
                    '407.php'     => "f",
                    '501.php'     => "f",
                    '410.php'     => "f",
                    '415.php'     => "f",
                    '503.php'     => "f",
                    '403.php'     => "f",
                    '511.php'     => "f",
                ),
                'Transport.php' => "f",
                'HTTP.php'      => "f",
                'Transport'     => array('cURL.php' => "f"),
            ),
            'Hooker.php'      => "f",
            'src'             => array(
                'Session.php'     => "f",
                'Exception'       => array(
                    'Http.php'            => "f",
                    'ArgumentCount.php'   => "f",
                    'Transport.php'       => "f",
                    'Http'                => array(
                        'Status503.php'     => "f",
                        'Status406.php'     => "f",
                        'Status405.php'     => "f",
                        'Status404.php'     => "f",
                        'StatusUnknown.php' => "f",
                        'Status501.php'     => "f",
                        'Status414.php'     => "f",
                        'Status429.php'     => "f",
                        'Status431.php'     => "f",
                        'Status409.php'     => "f",
                        'Status416.php'     => "f",
                        'Status403.php'     => "f",
                        'Status407.php'     => "f",
                        'Status428.php'     => "f",
                        'Status413.php'     => "f",
                        'Status411.php'     => "f",
                        'Status306.php'     => "f",
                        'Status415.php'     => "f",
                        'Status417.php'     => "f",
                        'Status412.php'     => "f",
                        'Status418.php'     => "f",
                        'Status408.php'     => "f",
                        'Status410.php'     => "f",
                        'Status401.php'     => "f",
                        'Status305.php'     => "f",
                        'Status504.php'     => "f",
                        'Status502.php'     => "f",
                        'Status500.php'     => "f",
                        'Status400.php'     => "f",
                        'Status505.php'     => "f",
                        'Status402.php'     => "f",
                        'Status304.php'     => "f",
                        'Status511.php'     => "f",
                    ),
                    'Transport'           => array('Curl.php' => "f"),
                    'InvalidArgument.php' => "f",
                ),
                'Auth.php'        => "f",
                'Cookie'          => array('Jar.php' => "f"),
                'Transport.php'   => "f",
                'Auth'            => array('Basic.php' => "f"),
                'Ipv6.php'        => "f",
                'Response'        => array('Headers.php' => "f"),
                'Cookie.php'      => "f",
                'HookManager.php' => "f",
                'Proxy.php'       => "f",
                'Response.php'    => "f",
                'Hooks.php'       => "f",
                'IdnaEncoder.php' => "f",
                'Iri.php'         => "f",
                'Capability.php'  => "f",
                'Requests.php'    => "f",
                'Ssl.php'         => "f",
                'Proxy'           => array('Http.php' => "f"),
                'Autoload.php'    => "f",
                'Exception.php'   => "f",
                'Utility'         => array(
                    'InputValidator.php'            => "f",
                    'FilteredIterator.php'          => "f",
                    'CaseInsensitiveDictionary.php' => "f",
                ),
                'Port.php'        => "f",
                'Transport'       => array(
                    'Fsockopen.php' => "f",
                    'Curl.php'      => "f",
                ),
            ),
            'IDNAEncoder.php' => "f",
            'Auth.php'        => "f",
            'Cookie'          => array('Jar.php' => "f"),
            'Transport.php'   => "f",
            'Auth'            => array('Basic.php' => "f"),
            'Response'        => array('Headers.php' => "f"),
            'Cookie.php'      => "f",
            'Proxy.php'       => "f",
            'IPv6.php'        => "f",
            'Response.php'    => "f",
            'Hooks.php'       => "f",
            'library'         => array('Requests.php' => "f"),
            'Proxy'           => array('HTTP.php' => "f"),
            'IRI.php'         => "f",
            'Exception.php'   => "f",
            'Utility'         => array(
                'FilteredIterator.php'          => "f",
                'CaseInsensitiveDictionary.php' => "f",
            ),
            'Transport'       => array(
                'fsockopen.php' => "f",
                'cURL.php'      => "f",
            ),
        ),
        'class-wp-oembed-controller.php'                 => "f",
        'category-template.php'                          => "f",
        'canonical.php'                                  => "f",
        'class-wp-http-requests-hooks.php'               => "f",
        'cache.php'                                      => "f",
        'SimplePie'                                      => array(
            'Cache'           => array(
                'Redis.php'     => "f",
                'Base.php'      => "f",
                'Memcached.php' => "f",
                'Memcache.php'  => "f",
                'DB.php'        => "f",
                'File.php'      => "f",
                'MySQL.php'     => "f",
            ),
            'autoloader.php'  => "f",
            'Content'         => array(
                'Type' => array('Sniffer.php' => "f"),
            ),
            'Sanitize.php'    => "f",
            'Restriction.php' => "f",
            'Parser.php'      => "f",
            'Cache.php'       => "f",
            'Item.php'        => "f",
            'HTTP'            => array('Parser.php' => "f"),
            'src'             => array(
                'RegistryAware.php' => "f",
                'Cache'             => array(
                    'BaseDataCache.php'      => "f",
                    'Redis.php'              => "f",
                    'DataCache.php'          => "f",
                    'CallableNameFilter.php' => "f",
                    'Base.php'               => "f",
                    'Memcached.php'          => "f",
                    'Memcache.php'           => "f",
                    'NameFilter.php'         => "f",
                    'DB.php'                 => "f",
                    'File.php'               => "f",
                    'MySQL.php'              => "f",
                    'Psr16.php'              => "f",
                ),
                'Gzdecode.php'      => "f",
                'Content'           => array(
                    'Type' => array('Sniffer.php' => "f"),
                ),
                'Sanitize.php'      => "f",
                'Restriction.php'   => "f",
                'Parser.php'        => "f",
                'Cache.php'         => "f",
                'Item.php'          => "f",
                'HTTP'              => array('Parser.php' => "f"),
                'Misc.php'          => "f",
                'Decode'            => array(
                    'HTML' => array('Entities.php' => "f"),
                ),
                'Enclosure.php'     => "f",
                'XML'               => array(
                    'Declaration' => array('Parser.php' => "f"),
                ),
                'Caption.php'       => "f",
                'Category.php'      => "f",
                'Author.php'        => "f",
                'File.php'          => "f",
                'Net'               => array('IPv6.php' => "f"),
                'Parse'             => array('Date.php' => "f"),
                'IRI.php'           => "f",
                'SimplePie.php'     => "f",
                'Exception.php'     => "f",
                'Source.php'        => "f",
                'Rating.php'        => "f",
                'Registry.php'      => "f",
                'Credit.php'        => "f",
                'Locator.php'       => "f",
                'Copyright.php'     => "f",
                'Core.php'          => "f",
            ),
            'Misc.php'        => "f",
            'Decode'          => array(
                'HTML' => array('Entities.php' => "f"),
            ),
            'gzdecode.php'    => "f",
            'Enclosure.php'   => "f",
            'XML'             => array(
                'Declaration' => array('Parser.php' => "f"),
            ),
            'Caption.php'     => "f",
            'Category.php'    => "f",
            'Author.php'      => "f",
            'File.php'        => "f",
            'Net'             => array('IPv6.php' => "f"),
            'Parse'           => array('Date.php' => "f"),
            'library'         => array(
                'SimplePie'     => array(
                    'Cache'           => array(
                        'Redis.php'     => "f",
                        'Base.php'      => "f",
                        'Memcached.php' => "f",
                        'Memcache.php'  => "f",
                        'DB.php'        => "f",
                        'File.php'      => "f",
                        'MySQL.php'     => "f",
                    ),
                    'Content'         => array(
                        'Type' => array('Sniffer.php' => "f"),
                    ),
                    'Sanitize.php'    => "f",
                    'Restriction.php' => "f",
                    'Parser.php'      => "f",
                    'Cache.php'       => "f",
                    'Item.php'        => "f",
                    'HTTP'            => array('Parser.php' => "f"),
                    'Misc.php'        => "f",
                    'Decode'          => array(
                        'HTML' => array('Entities.php' => "f"),
                    ),
                    'gzdecode.php'    => "f",
                    'Enclosure.php'   => "f",
                    'XML'             => array(
                        'Declaration' => array('Parser.php' => "f"),
                    ),
                    'Caption.php'     => "f",
                    'Category.php'    => "f",
                    'Author.php'      => "f",
                    'File.php'        => "f",
                    'Net'             => array('IPv6.php' => "f"),
                    'Parse'           => array('Date.php' => "f"),
                    'IRI.php'         => "f",
                    'Exception.php'   => "f",
                    'Source.php'      => "f",
                    'Rating.php'      => "f",
                    'Registry.php'    => "f",
                    'Credit.php'      => "f",
                    'Locator.php'     => "f",
                    'Copyright.php'   => "f",
                    'Core.php'        => "f",
                ),
                'SimplePie.php' => "f",
            ),
            'IRI.php'         => "f",
            'Exception.php'   => "f",
            'Source.php'      => "f",
            'Rating.php'      => "f",
            'Registry.php'    => "f",
            'Credit.php'      => "f",
            'Locator.php'     => "f",
            'Copyright.php'   => "f",
            'Core.php'        => "f",
        ),
        'embed-template.php'                             => "f",
        'class-wp-hook.php'                              => "f",
        'feed.php'                                       => "f",
        'class-wp-walker.php'                            => "f",
        'registration-functions.php'                     => "f",
        'user.php'                                       => "f",
        'class-snoopy.php'                               => "f",
        'class-simplepie.php'                            => "f",
        'block-i18n.json'                                => "f",
        'ID3'                                            => array(
            'license.commercial.txt'           => "f",
            'module.audio.ogg.php'             => "f",
            'module.tag.id3v1.php'             => "f",
            'license.txt'                      => "f",
            'module.tag.apetag.php'            => "f",
            'getid3.php'                       => "f",
            'readme.txt'                       => "f",
            'module.audio.flac.php'            => "f",
            'module.audio.ac3.php'             => "f",
            'getid3.lib.php'                   => "f",
            'module.audio-video.asf.php'       => "f",
            'module.tag.id3v2.php'             => "f",
            'module.tag.lyrics3.php'           => "f",
            'module.audio.dts.php'             => "f",
            'module.audio-video.quicktime.php' => "f",
            'module.audio-video.riff.php'      => "f",
            'module.audio-video.flv.php'       => "f",
            'module.audio-video.matroska.php'  => "f",
            'module.audio.mp3.php'             => "f",
        ),
        'theme.php'                                      => "f",
        'Text'                                           => array(
            'Diff.php'      => "f",
            'Diff'          => array(
                'Engine'       => array(
                    'shell.php'  => "f",
                    'string.php' => "f",
                    'xdiff.php'  => "f",
                    'native.php' => "f",
                ),
                'Renderer'     => array('inline.php' => "f"),
                'Renderer.php' => "f",
            ),
            'Exception.php' => "f",
        ),
        'update.php'                                     => "f",
        'ms-default-constants.php'                       => "f",
        'ms-load.php'                                    => "f",
        'class-wp-http-requests-response.php'            => "f",
        'block-template-utils.php'                       => "f",
        'class-wp-term-query.php'                        => "f",
        'class-wp-list-util.php'                         => "f",
        'blocks.php'                                     => "f",
        'class-wp-block-supports.php'                    => "f",
        'class-wp-block.php'                             => "f",
        'class-wp-user-meta-session-tokens.php'          => "f",
        'block-template.php'                             => "f",
        'script-loader.php'                              => "f",
        'block-bindings'                                 => array(
            'pattern-overrides.php' => "f",
            'post-meta.php'         => "f",
        ),
        'PHPMailer'                                      => array(
            'SMTP.php'      => "f",
            'Exception.php' => "f",
            'PHPMailer.php' => "f",
        ),
        'class-wp-customize-manager.php'                 => "f",
        'vars.php'                                       => "f",
        'bookmark.php'                                   => "f",
        'class-wp-theme-json-schema.php'                 => "f",
        'class-wp-role.php'                              => "f",
        'default-constants.php'                          => "f",
        'html-api'                                       => array(
            'html5-named-character-references.php'         => "f",
            'class-wp-html-attribute-token.php'            => "f",
            'class-wp-html-stack-event.php'                => "f",
            'class-wp-html-active-formatting-elements.php' => "f",
            'class-wp-html-text-replacement.php'           => "f",
            'class-wp-html-processor-state.php'            => "f",
            'class-wp-html-doctype-info.php'               => "f",
            'class-wp-html-token.php'                      => "f",
            'class-wp-html-processor.php'                  => "f",
            'class-wp-html-span.php'                       => "f",
            'class-wp-html-decoder.php'                    => "f",
            'class-wp-html-unsupported-exception.php'      => "f",
            'class-wp-html-tag-processor.php'              => "f",
            'class-wp-html-open-elements.php'              => "f",
        ),
        'sodium_compat'                                  => array(
            'autoload.php'      => "f",
            'src'               => array(
                'PHP52'               => array('SplFixedArray.php' => "f"),
                'Crypto.php'          => "f",
                'Compat.php'          => "f",
                'Core32'              => array(
                    'Poly1305.php'   => "f",
                    'Salsa20.php'    => "f",
                    'XChaCha20.php'  => "f",
                    'BLAKE2b.php'    => "f",
                    'X25519.php'     => "f",
                    'SecretStream'   => array('State.php' => "f"),
                    'Curve25519'     => array(
                        'Fe.php'    => "f",
                        'H.php'     => "f",
                        'README.md' => "f",
                        'Ge'        => array(
                            'P3.php'      => "f",
                            'Cached.php'  => "f",
                            'P1p1.php'    => "f",
                            'Precomp.php' => "f",
                            'P2.php'      => "f",
                        ),
                    ),
                    'Int64.php'      => "f",
                    'SipHash.php'    => "f",
                    'Curve25519.php' => "f",
                    'XSalsa20.php'   => "f",
                    'HSalsa20.php'   => "f",
                    'ChaCha20.php'   => "f",
                    'Ed25519.php'    => "f",
                    'HChaCha20.php'  => "f",
                    'Int32.php'      => "f",
                    'Poly1305'       => array('State.php' => "f"),
                    'Util.php'       => "f",
                    'ChaCha20'       => array(
                        'IetfCtx.php' => "f",
                        'Ctx.php'     => "f",
                    ),
                ),
                'SodiumException.php' => "f",
                'Crypto32.php'        => "f",
                'Core'                => array(
                    'Poly1305.php'     => "f",
                    'Base64'           => array(
                        'Common.php'   => "f",
                        'UrlSafe.php'  => "f",
                        'Original.php' => "f",
                    ),
                    'Salsa20.php'      => "f",
                    'Ristretto255.php' => "f",
                    'XChaCha20.php'    => "f",
                    'BLAKE2b.php'      => "f",
                    'X25519.php'       => "f",
                    'AEGIS128L.php'    => "f",
                    'SecretStream'     => array('State.php' => "f"),
                    'AES'              => array(
                        'KeySchedule.php' => "f",
                        'Block.php'       => "f",
                        'Expanded.php'    => "f",
                    ),
                    'Curve25519'       => array(
                        'Fe.php'    => "f",
                        'H.php'     => "f",
                        'README.md' => "f",
                        'Ge'        => array(
                            'P3.php'      => "f",
                            'Cached.php'  => "f",
                            'P1p1.php'    => "f",
                            'Precomp.php' => "f",
                            'P2.php'      => "f",
                        ),
                    ),
                    'SipHash.php'      => "f",
                    'Curve25519.php'   => "f",
                    'AES.php'          => "f",
                    'XSalsa20.php'     => "f",
                    'HSalsa20.php'     => "f",
                    'ChaCha20.php'     => "f",
                    'Ed25519.php'      => "f",
                    'HChaCha20.php'    => "f",
                    'AEGIS'            => array(
                        'State128L.php' => "f",
                        'State256.php'  => "f",
                    ),
                    'AEGIS256.php'     => "f",
                    'Poly1305'         => array('State.php' => "f"),
                    'Util.php'         => "f",
                    'ChaCha20'         => array(
                        'IetfCtx.php' => "f",
                        'Ctx.php'     => "f",
                    ),
                ),
                'File.php'            => "f",
            ),
            'namespaced'        => array(
                'Crypto.php' => "f",
                'Compat.php' => "f",
                'Core'       => array(
                    'Poly1305.php'   => "f",
                    'Salsa20.php'    => "f",
                    'XChaCha20.php'  => "f",
                    'BLAKE2b.php'    => "f",
                    'X25519.php'     => "f",
                    'Curve25519'     => array(
                        'Fe.php' => "f",
                        'H.php'  => "f",
                        'Ge'     => array(
                            'P3.php'      => "f",
                            'Cached.php'  => "f",
                            'P1p1.php'    => "f",
                            'Precomp.php' => "f",
                            'P2.php'      => "f",
                        ),
                    ),
                    'SipHash.php'    => "f",
                    'Curve25519.php' => "f",
                    'HSalsa20.php'   => "f",
                    'ChaCha20.php'   => "f",
                    'Ed25519.php'    => "f",
                    'HChaCha20.php'  => "f",
                    'Poly1305'       => array('State.php' => "f"),
                    'Xsalsa20.php'   => "f",
                    'Util.php'       => "f",
                    'ChaCha20'       => array(
                        'IetfCtx.php' => "f",
                        'Ctx.php'     => "f",
                    ),
                ),
                'File.php'   => "f",
            ),
            'composer.json'     => "f",
            'autoload-php7.php' => "f",
            'LICENSE'           => "f",
            'lib'               => array(
                'php84compat.php'       => "f",
                'sodium_compat.php'     => "f",
                'php72compat_const.php' => "f",
                'constants.php'         => "f",
                'namespaced.php'        => "f",
                'ristretto255.php'      => "f",
                'stream-xchacha20.php'  => "f",
                'php72compat.php'       => "f",
                'php84compat_const.php' => "f",
            ),
        ),
        'fonts'                                          => array(
            'class-wp-font-utils.php'         => "f",
            'dashicons.woff2'                 => "f",
            'dashicons.woff'                  => "f",
            'class-wp-font-collection.php'    => "f",
            'dashicons.svg'                   => "f",
            'class-wp-font-face-resolver.php' => "f",
            'class-wp-font-library.php'       => "f",
            'dashicons.eot'                   => "f",
            'class-wp-font-face.php'          => "f",
            'dashicons.ttf'                   => "f",
        ),
        'embed.php'                                      => "f",
        'load.php'                                       => "f",
        'class-wp-comment-query.php'                     => "f",
        'cache-compat.php'                               => "f",
        'ms-site.php'                                    => "f",
        'class-oembed.php'                               => "f",
        'class-wp-block-list.php'                        => "f",
        'js'                                             => array(
            'customize-preview-nav-menus.min.js' => "f",
            'jquery'                             => array(
                'jquery.table-hotkeys.min.js' => "f",
                'jquery.ui.touch-punch.js'    => "f",
                'jquery.js'                   => "f",
                'jquery.schedule.js'          => "f",
                'jquery-migrate.js'           => "f",
                'jquery.masonry.min.js'       => "f",
                'suggest.js'                  => "f",
                'suggest.min.js'              => "f",
                'ui'                          => array(
                    'jquery.ui.effect-slide.min.js'     => "f",
                    'tooltip.min.js'                    => "f",
                    'effect-bounce.min.js'              => "f",
                    'jquery.ui.effect-transfer.min.js'  => "f",
                    'effect-slide.min.js'               => "f",
                    'jquery.ui.slider.min.js'           => "f",
                    'resizable.min.js'                  => "f",
                    'selectmenu.min.js'                 => "f",
                    'effect-fold.js'                    => "f",
                    'jquery.ui.datepicker.min.js'       => "f",
                    'menu.min.js'                       => "f",
                    'jquery.ui.effect.min.js'           => "f",
                    'checkboxradio.min.js'              => "f",
                    'effect-explode.js'                 => "f",
                    'jquery.ui.effect-shake.min.js'     => "f",
                    'dialog.min.js'                     => "f",
                    'effect.js'                         => "f",
                    'effect-explode.min.js'             => "f",
                    'effect-shake.min.js'               => "f",
                    'sortable.min.js'                   => "f",
                    'draggable.min.js'                  => "f",
                    'tabs.js'                           => "f",
                    'jquery.ui.accordion.min.js'        => "f",
                    'mouse.js'                          => "f",
                    'effect-fade.js'                    => "f",
                    'effect-blind.js'                   => "f",
                    'selectmenu.js'                     => "f",
                    'datepicker.min.js'                 => "f",
                    'jquery.ui.effect-bounce.min.js'    => "f",
                    'button.min.js'                     => "f",
                    'slider.js'                         => "f",
                    'jquery.ui.position.min.js'         => "f",
                    'effect-transfer.js'                => "f",
                    'jquery.ui.autocomplete.min.js'     => "f",
                    'jquery.ui.effect-clip.min.js'      => "f",
                    'jquery.ui.button.min.js'           => "f",
                    'resizable.js'                      => "f",
                    'effect-puff.min.js'                => "f",
                    'effect-puff.js'                    => "f",
                    'effect-scale.js'                   => "f",
                    'effect-size.min.js'                => "f",
                    'progressbar.js'                    => "f",
                    'jquery.ui.spinner.min.js'          => "f",
                    'dialog.js'                         => "f",
                    'effect-drop.js'                    => "f",
                    'effect-clip.js'                    => "f",
                    'jquery.ui.effect-blind.min.js'     => "f",
                    'controlgroup.js'                   => "f",
                    'tooltip.js'                        => "f",
                    'jquery.ui.core.min.js'             => "f",
                    'jquery.ui.progressbar.min.js'      => "f",
                    'progressbar.min.js'                => "f",
                    'jquery.ui.draggable.min.js'        => "f",
                    'effect.min.js'                     => "f",
                    'effect-highlight.js'               => "f",
                    'droppable.js'                      => "f",
                    'jquery.ui.tabs.min.js'             => "f",
                    'effect-fade.min.js'                => "f",
                    'effect-pulsate.js'                 => "f",
                    'core.js'                           => "f",
                    'effect-highlight.min.js'           => "f",
                    'jquery.ui.widget.min.js'           => "f",
                    'effect-slide.js'                   => "f",
                    'effect-transfer.min.js'            => "f",
                    'effect-fold.min.js'                => "f",
                    'jquery.ui.effect-fade.min.js'      => "f",
                    'mouse.min.js'                      => "f",
                    'spinner.js'                        => "f",
                    'sortable.js'                       => "f",
                    'spinner.min.js'                    => "f",
                    'autocomplete.min.js'               => "f",
                    'jquery.ui.effect-pulsate.min.js'   => "f",
                    'tabs.min.js'                       => "f",
                    'effect-pulsate.min.js'             => "f",
                    'selectable.js'                     => "f",
                    'effect-shake.js'                   => "f",
                    'jquery.ui.resizable.min.js'        => "f",
                    'accordion.js'                      => "f",
                    'effect-size.js'                    => "f",
                    'menu.js'                           => "f",
                    'jquery.ui.droppable.min.js'        => "f",
                    'jquery.ui.mouse.min.js'            => "f",
                    'core.min.js'                       => "f",
                    'effect-blind.min.js'               => "f",
                    'jquery.ui.effect-fold.min.js'      => "f",
                    'jquery.ui.effect-drop.min.js'      => "f",
                    'effect-drop.min.js'                => "f",
                    'droppable.min.js'                  => "f",
                    'jquery.ui.effect-highlight.min.js' => "f",
                    'jquery.ui.effect-explode.min.js'   => "f",
                    'effect-scale.min.js'               => "f",
                    'effect-clip.min.js'                => "f",
                    'slider.min.js'                     => "f",
                    'autocomplete.js'                   => "f",
                    'jquery.ui.dialog.min.js'           => "f",
                    'widget.min.js'                     => "f",
                    'controlgroup.min.js'               => "f",
                    'jquery.ui.tooltip.min.js'          => "f",
                    'position.min.js'                   => "f",
                    'button.js'                         => "f",
                    'checkboxradio.js'                  => "f",
                    'datepicker.js'                     => "f",
                    'jquery.ui.menu.min.js'             => "f",
                    'jquery.ui.sortable.min.js'         => "f",
                    'selectable.min.js'                 => "f",
                    'jquery.ui.selectable.min.js'       => "f",
                    'draggable.js'                      => "f",
                    'accordion.min.js'                  => "f",
                    'jquery.ui.effect-scale.min.js'     => "f",
                    'effect-bounce.js'                  => "f",
                ),
                'jquery.min.js'               => "f",
                'jquery.hotkeys.min.js'       => "f",
                'jquery.form.min.js'          => "f",
                'jquery.serialize-object.js'  => "f",
                'jquery.query.js'             => "f",
                'jquery.color.min.js'         => "f",
                'jquery.table-hotkeys.js'     => "f",
                'jquery.hotkeys.js'           => "f",
                'jquery.form.js'              => "f",
                'jquery-migrate.min.js'       => "f",
            ),
            'admin-bar.js'                       => "f",
            'backbone.js'                        => "f",
            'media-views.min.js'                 => "f",
            'customize-preview.min.js'           => "f",
            'customize-models.js'                => "f",
            'wp-emoji-release.min.js'            => "f",
            'wp-emoji-loader.min.js'             => "f",
            'zxcvbn.min.js'                      => "f",
            'customize-base.js'                  => "f",
            'customize-models.min.js'            => "f",
            'wp-embed-template.js'               => "f",
            'twemoji.js'                         => "f",
            'clipboard.min.js'                   => "f",
            'zxcvbn-async.min.js'                => "f",
            'shortcode.js'                       => "f",
            'customize-preview-nav-menus.js'     => "f",
            'customize-selective-refresh.min.js' => "f",
            'customize-views.js'                 => "f",
            'wpdialog.min.js'                    => "f",
            'mce-view.js'                        => "f",
            'wp-lists.min.js'                    => "f",
            'tinymce'                            => array(
                'utils'             => array(
                    'validate.js'         => "f",
                    'mctabs.js'           => "f",
                    'editable_selects.js' => "f",
                    'form_utils.js'       => "f",
                ),
                'wp-mce-help.php'   => "f",
                'wp-tinymce.js'     => "f",
                'license.txt'       => "f",
                'langs'             => array('wp-langs-en.js' => "f"),
                'tinymce.min.js'    => "f",
                'plugins'           => array(
                    'wpembed'        => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'wplink'         => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'wpview'         => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'wpfullscreen'   => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'tabfocus'       => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'wpeditimage'    => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'link'           => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'wptextpattern'  => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'wordpress'      => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'media'          => array(
                        'plugin.js'       => "f",
                        'plugin.min.js'   => "f",
                        'moxieplayer.swf' => "f",
                    ),
                    'wpdialogs'      => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'wpautoresize'   => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'wpgallery'      => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'lists'          => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'wpemoji'        => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'charmap'        => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'colorpicker'    => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'fullscreen'     => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'directionality' => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'paste'          => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'hr'             => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'compat3x'       => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                        'css'           => array('dialog.css' => "f"),
                    ),
                    'image'          => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                    'textcolor'      => array(
                        'plugin.js'     => "f",
                        'plugin.min.js' => "f",
                    ),
                ),
                'wp-tinymce.js.gz'  => "f",
                'tiny_mce_popup.js' => "f",
                'wp-tinymce.php'    => "f",
                'skins'             => array(
                    'lightgray' => array(
                        'fonts'                  => array(
                            'tinymce.woff'       => "f",
                            'tinymce.json'       => "f",
                            'tinymce-small.eot'  => "f",
                            'tinymce.ttf'        => "f",
                            'tinymce-small.svg'  => "f",
                            'tinymce-small.json' => "f",
                            'tinymce-small.woff' => "f",
                            'tinymce.svg'        => "f",
                            'readme.md'          => "f",
                            'tinymce.eot'        => "f",
                            'tinymce-small.ttf'  => "f",
                        ),
                        'skin.ie7.min.css'       => "f",
                        'skin.min.css'           => "f",
                        'content.inline.min.css' => "f",
                        'content.min.css'        => "f",
                        'img'                    => array(
                            'trans.gif'  => "f",
                            'loader.gif' => "f",
                            'object.gif' => "f",
                            'anchor.gif' => "f",
                        ),
                    ),
                    'wordpress' => array(
                        'wp-content.css' => "f",
                        'images'         => array(
                            'dashicon-no.png'     => "f",
                            'pagebreak.png'       => "f",
                            'script.svg'          => "f",
                            'playlist-video.png'  => "f",
                            'pagebreak-2x.png'    => "f",
                            'gallery-2x.png'      => "f",
                            'dashicon-edit.png'   => "f",
                            'video.png'           => "f",
                            'more.png'            => "f",
                            'embedded.png'        => "f",
                            'more-2x.png'         => "f",
                            'dashicon-no-alt.png' => "f",
                            'gallery.png'         => "f",
                            'audio.png'           => "f",
                            'playlist-audio.png'  => "f",
                            'style.svg'           => "f",
                        ),
                    ),
                ),
                'themes'            => array(
                    'inlite' => array(
                        'theme.js'     => "f",
                        'theme.min.js' => "f",
                    ),
                    'modern' => array(
                        'theme.js'     => "f",
                        'theme.min.js' => "f",
                    ),
                ),
            ),
            'wp-emoji.js'                        => "f",
            'wpdialog.js'                        => "f",
            'underscore.min.js'                  => "f",
            'media-grid.js'                      => "f",
            'json2.js'                           => "f",
            'wp-sanitize.js'                     => "f",
            'wp-list-revisions.min.js'           => "f",
            'twemoji.min.js'                     => "f",
            'codemirror'                         => array(
                'esprima.js'         => "f",
                'htmlhint-kses.js'   => "f",
                'codemirror.min.css' => "f",
                'csslint.js'         => "f",
                'htmlhint.js'        => "f",
                'fakejshint.js'      => "f",
                'codemirror.min.js'  => "f",
                'jshint.js'          => "f",
                'jsonlint.js'        => "f",
            ),
            'customize-preview.js'               => "f",
            'wplink.js'                          => "f",
            'heartbeat.js'                       => "f",
            'swfupload'                          => array(
                'swfupload.swf'   => "f",
                'handlers.js'     => "f",
                'license.txt'     => "f",
                'plugins'         => array(
                    'swfupload.queue.js'     => "f",
                    'swfupload.speed.js'     => "f",
                    'swfupload.swfobject.js' => "f",
                    'swfupload.cookies.js'   => "f",
                ),
                'handlers.min.js' => "f",
                'swfupload.js'    => "f",
            ),
            'wp-auth-check.min.js'               => "f",
            'colorpicker.js'                     => "f",
            'comment-reply.min.js'               => "f",
            'wp-backbone.js'                     => "f",
            'plupload'                           => array(
                'handlers.js'              => "f",
                'plupload.full.min.js'     => "f",
                'license.txt'              => "f",
                'wp-plupload.js'           => "f",
                'plupload.min.js'          => "f",
                'plupload.js'              => "f",
                'moxie.js'                 => "f",
                'moxie.min.js'             => "f",
                'handlers.min.js'          => "f",
                'wp-plupload.min.js'       => "f",
                'plupload.silverlight.xap' => "f",
                'plupload.flash.swf'       => "f",
            ),
            'wp-a11y.js'                         => "f",
            'json2.min.js'                       => "f",
            'api-request.js'                     => "f",
            'customize-loader.js'                => "f",
            'wp-backbone.min.js'                 => "f",
            'wp-custom-header.min.js'            => "f",
            'hoverIntent.js'                     => "f",
            'utils.js'                           => "f",
            'mce-view.min.js'                    => "f",
            'tw-sack.js'                         => "f",
            'wp-custom-header.js'                => "f",
            'imgareaselect'                      => array(
                'border-anim-v.gif'           => "f",
                'border-anim-h.gif'           => "f",
                'jquery.imgareaselect.min.js' => "f",
                'jquery.imgareaselect.js'     => "f",
                'imgareaselect.css'           => "f",
            ),
            'wp-embed-template.min.js'           => "f",
            'heartbeat.min.js'                   => "f",
            'imagesloaded.min.js'                => "f",
            'customize-views.min.js'             => "f",
            'wp-a11y.min.js'                     => "f",
            'wp-sanitize.min.js'                 => "f",
            'crop'                               => array(
                'marqueeHoriz.gif' => "f",
                'marqueeVert.gif'  => "f",
                'cropper.css'      => "f",
                'cropper.js'       => "f",
            ),
            'customize-selective-refresh.js'     => "f",
            'media-models.js'                    => "f",
            'wp-ajax-response.js'                => "f",
            'wp-emoji.min.js'                    => "f",
            'wp-list-revisions.js'               => "f",
            'media-audiovideo.min.js'            => "f",
            'autosave.js'                        => "f",
            'wp-pointer.min.js'                  => "f",
            'clipboard.js'                       => "f",
            'utils.min.js'                       => "f",
            'underscore.js'                      => "f",
            'customize-loader.min.js'            => "f",
            'wp-ajax-response.min.js'            => "f",
            'backbone.min.js'                    => "f",
            'shortcode.min.js'                   => "f",
            'customize-base.min.js'              => "f",
            'autosave.min.js'                    => "f",
            'wp-auth-check.js'                   => "f",
            'zxcvbn-async.js'                    => "f",
            'wp-pointer.js'                      => "f",
            'wp-util.js'                         => "f",
            'tw-sack.min.js'                     => "f",
            'media-models.min.js'                => "f",
            'thickbox'                           => array(
                'loadingAnimation.gif' => "f",
                'macFFBgHack.png'      => "f",
                'thickbox.css'         => "f",
                'thickbox.js'          => "f",
            ),
            'media-grid.min.js'                  => "f",
            'wplink.min.js'                      => "f",
            'wp-util.min.js'                     => "f",
            'mediaelement'                       => array(
                'skipback.png'                      => "f",
                'mediaelement.min.js'               => "f",
                'wp-mediaelement.min.css'           => "f",
                'controls.png'                      => "f",
                'wp-mediaelement.min.js'            => "f",
                'wp-playlist.js'                    => "f",
                'mejs-controls.png'                 => "f",
                'bigplay.svg'                       => "f",
                'mediaelement-and-player.js'        => "f",
                'froogaloop.min.js'                 => "f",
                'mediaelementplayer.min.css'        => "f",
                'jumpforward.png'                   => "f",
                'mediaelement-and-player.min.js'    => "f",
                'background.png'                    => "f",
                'mediaelement-migrate.js'           => "f",
                'mediaelementplayer-legacy.min.css' => "f",
                'mediaelement-migrate.min.js'       => "f",
                'renderers'                         => array(
                    'vimeo.js'     => "f",
                    'vimeo.min.js' => "f",
                ),
                'controls.svg'                      => "f",
                'wp-mediaelement.css'               => "f",
                'loading.gif'                       => "f",
                'mediaelement.js'                   => "f",
                'wp-mediaelement.js'                => "f",
                'mejs-controls.svg'                 => "f",
                'mediaelementplayer-legacy.css'     => "f",
                'bigplay.png'                       => "f",
                'mediaelementplayer.css'            => "f",
                'wp-playlist.min.js'                => "f",
            ),
            'customize-preview-widgets.js'       => "f",
            'quicktags.js'                       => "f",
            'hoverintent-js.min.js'              => "f",
            'wp-emoji-loader.js'                 => "f",
            'dist'                               => array(
                'i18n.js'                                   => "f",
                'list-reusable-blocks.min.js'               => "f",
                'private-apis.min.js'                       => "f",
                'html-entities.min.js'                      => "f",
                'deprecated.js'                             => "f",
                'interactivity-router.asset.php'            => "f",
                'edit-post.min.js'                          => "f",
                'core-data.min.js'                          => "f",
                'patterns.js'                               => "f",
                'editor.min.js'                             => "f",
                'autop.min.js'                              => "f",
                'escape-html.min.js'                        => "f",
                'private-apis.js'                           => "f",
                'annotations.min.js'                        => "f",
                'list-reusable-blocks.js'                   => "f",
                'shortcode.js'                              => "f",
                'keycodes.js'                               => "f",
                'reusable-blocks.min.js'                    => "f",
                'element.min.js'                            => "f",
                'format-library.js'                         => "f",
                'style-engine.min.js'                       => "f",
                'preferences.js'                            => "f",
                'nux.min.js'                                => "f",
                'block-editor.js'                           => "f",
                'data-controls.js'                          => "f",
                'customize-widgets.min.js'                  => "f",
                'warning.js'                                => "f",
                'annotations.js'                            => "f",
                'components.js'                             => "f",
                'compose.min.js'                            => "f",
                'priority-queue.min.js'                     => "f",
                'rich-text.min.js'                          => "f",
                'components.min.js'                         => "f",
                'block-directory.js'                        => "f",
                'customize-widgets.js'                      => "f",
                'undo-manager.js'                           => "f",
                'blob.min.js'                               => "f",
                'compose.js'                                => "f",
                'preferences.min.js'                        => "f",
                'editor.js'                                 => "f",
                'core-commands.js'                          => "f",
                'a11y.min.js'                               => "f",
                'style-engine.js'                           => "f",
                'commands.js'                               => "f",
                'fields.min.js'                             => "f",
                'escape-html.js'                            => "f",
                'warning.min.js'                            => "f",
                'core-data.js'                              => "f",
                'redux-routine.min.js'                      => "f",
                'format-library.min.js'                     => "f",
                'plugins.js'                                => "f",
                'blob.js'                                   => "f",
                'edit-widgets.min.js'                       => "f",
                'server-side-render.min.js'                 => "f",
                'primitives.js'                             => "f",
                'commands.min.js'                           => "f",
                'notices.min.js'                            => "f",
                'autop.js'                                  => "f",
                'dom-ready.js'                              => "f",
                'url.js'                                    => "f",
                'interactivity-router.min.asset.php'        => "f",
                'date.js'                                   => "f",
                'viewport.js'                               => "f",
                'html-entities.js'                          => "f",
                'undo-manager.min.js'                       => "f",
                'a11y.js'                                   => "f",
                'dom.js'                                    => "f",
                'keycodes.min.js'                           => "f",
                'redux-routine.js'                          => "f",
                'wordcount.js'                              => "f",
                'core-commands.min.js'                      => "f",
                'hooks.min.js'                              => "f",
                'api-fetch.js'                              => "f",
                'date.min.js'                               => "f",
                'widgets.js'                                => "f",
                'deprecated.min.js'                         => "f",
                'media-utils.min.js'                        => "f",
                'nux.js'                                    => "f",
                'block-library.js'                          => "f",
                'server-side-render.js'                     => "f",
                'router.min.js'                             => "f",
                'widgets.min.js'                            => "f",
                'dom.min.js'                                => "f",
                'priority-queue.js'                         => "f",
                'media-utils.js'                            => "f",
                'edit-widgets.js'                           => "f",
                'shortcode.min.js'                          => "f",
                'script-modules'                            => array(
                    'a11y'                 => array(
                        'index.js'     => "f",
                        'index.min.js' => "f",
                    ),
                    'interactivity-router' => array(
                        'index.js'     => "f",
                        'index.min.js' => "f",
                    ),
                    'block-library'        => array(
                        'form'       => array(
                            'view.min.js' => "f",
                            'view.js'     => "f",
                        ),
                        'search'     => array(
                            'view.min.js' => "f",
                            'view.js'     => "f",
                        ),
                        'navigation' => array(
                            'view.min.js' => "f",
                            'view.js'     => "f",
                        ),
                        'query'      => array(
                            'view.min.js' => "f",
                            'view.js'     => "f",
                        ),
                        'file'       => array(
                            'view.min.js' => "f",
                            'view.js'     => "f",
                        ),
                        'image'      => array(
                            'view.min.js' => "f",
                            'view.js'     => "f",
                        ),
                    ),
                    'interactivity'        => array(
                        'debug.js'     => "f",
                        'index.js'     => "f",
                        'index.min.js' => "f",
                        'debug.min.js' => "f",
                    ),
                ),
                'plugins.min.js'                            => "f",
                'preferences-persistence.min.js'            => "f",
                'primitives.min.js'                         => "f",
                'reusable-blocks.js'                        => "f",
                'block-serialization-default-parser.js'     => "f",
                'keyboard-shortcuts.js'                     => "f",
                'interactivity.js'                          => "f",
                'data-controls.min.js'                      => "f",
                'data.js'                                   => "f",
                'edit-post.js'                              => "f",
                'rich-text.js'                              => "f",
                'dom-ready.min.js'                          => "f",
                'i18n.min.js'                               => "f",
                'data.min.js'                               => "f",
                'is-shallow-equal.min.js'                   => "f",
                'element.js'                                => "f",
                'router.js'                                 => "f",
                'edit-site.js'                              => "f",
                'token-list.min.js'                         => "f",
                'block-editor.min.js'                       => "f",
                'vendor'                                    => array(
                    'lodash.js'                            => "f",
                    'wp-polyfill-fetch.js'                 => "f",
                    'wp-polyfill-node-contains.js'         => "f",
                    'wp-polyfill-element-closest.min.js'   => "f",
                    'wp-polyfill.min.js'                   => "f",
                    'wp-polyfill-object-fit.min.js'        => "f",
                    'wp-polyfill-importmap.min.js'         => "f",
                    'wp-polyfill-formdata.js'              => "f",
                    'wp-polyfill-formdata.min.js'          => "f",
                    'wp-polyfill-node-contains.min.js'     => "f",
                    'wp-polyfill.js'                       => "f",
                    'lodash.min.js'                        => "f",
                    'moment.js'                            => "f",
                    'wp-polyfill-importmap.js'             => "f",
                    'react-dom.min.js.LICENSE.txt'         => "f",
                    'wp-polyfill-object-fit.js'            => "f",
                    'react.js'                             => "f",
                    'wp-polyfill-inert.min.js'             => "f",
                    'wp-polyfill-fetch.min.js'             => "f",
                    'regenerator-runtime.min.js'           => "f",
                    'regenerator-runtime.js'               => "f",
                    'wp-polyfill-element-closest.js'       => "f",
                    'wp-polyfill-inert.js'                 => "f",
                    'moment.min.js'                        => "f",
                    'react-jsx-runtime.min.js'             => "f",
                    'react.min.js'                         => "f",
                    'wp-polyfill-url.min.js'               => "f",
                    'react-jsx-runtime.js'                 => "f",
                    'react.min.js.LICENSE.txt'             => "f",
                    'wp-polyfill-dom-rect.min.js'          => "f",
                    'react-jsx-runtime.min.js.LICENSE.txt' => "f",
                    'react-dom.js'                         => "f",
                    'wp-polyfill-url.js'                   => "f",
                    'react-dom.min.js'                     => "f",
                    'wp-polyfill-dom-rect.js'              => "f",
                ),
                'is-shallow-equal.js'                       => "f",
                'block-library.min.js'                      => "f",
                'blocks.js'                                 => "f",
                'interactivity-router.js'                   => "f",
                'interactivity-router.min.js'               => "f",
                'interactivity.min.js'                      => "f",
                'development'                               => array(
                    'react-refresh-runtime.min.js' => "f",
                    'react-refresh-entry.js'       => "f",
                    'react-refresh-runtime.js'     => "f",
                    'react-refresh-entry.min.js'   => "f",
                ),
                'edit-site.min.js'                          => "f",
                'hooks.js'                                  => "f",
                'wordcount.min.js'                          => "f",
                'token-list.js'                             => "f",
                'block-serialization-default-parser.min.js' => "f",
                'keyboard-shortcuts.min.js'                 => "f",
                'notices.js'                                => "f",
                'url.min.js'                                => "f",
                'preferences-persistence.js'                => "f",
                'viewport.min.js'                           => "f",
                'api-fetch.min.js'                          => "f",
                'blocks.min.js'                             => "f",
                'block-directory.min.js'                    => "f",
                'fields.js'                                 => "f",
                'patterns.min.js'                           => "f",
            ),
            'swfobject.js'                       => "f",
            'jcrop'                              => array(
                'Jcrop.gif'            => "f",
                'jquery.Jcrop.min.js'  => "f",
                'jquery.Jcrop.min.css' => "f",
            ),
            'media-views.js'                     => "f",
            'wp-embed.min.js'                    => "f",
            'customize-preview-widgets.min.js'   => "f",
            'wp-embed.js'                        => "f",
            'wp-api.min.js'                      => "f",
            'hoverIntent.min.js'                 => "f",
            'colorpicker.min.js'                 => "f",
            'api-request.min.js'                 => "f",
            'wp-api.js'                          => "f",
            'masonry.min.js'                     => "f",
            'quicktags.min.js'                   => "f",
            'wp-lists.js'                        => "f",
            'media-editor.min.js'                => "f",
            'media-audiovideo.js'                => "f",
            'media-editor.js'                    => "f",
            'admin-bar.min.js'                   => "f",
            'comment-reply.js'                   => "f",
        ),
        'customize'                                      => array(
            'class-wp-customize-code-editor-control.php'         => "f",
            'class-wp-customize-background-position-control.php' => "f",
            'class-wp-customize-upload-control.php'              => "f",
            'class-wp-customize-header-image-setting.php'        => "f",
            'class-wp-customize-nav-menu-auto-add-control.php'   => "f",
            'class-wp-customize-filter-setting.php'              => "f",
            'class-wp-customize-date-time-control.php'           => "f",
            'class-wp-widget-form-customize-control.php'         => "f",
            'class-wp-customize-themes-panel.php'                => "f",
            'class-wp-customize-theme-control.php'               => "f",
            'class-wp-customize-partial.php'                     => "f",
            'class-wp-customize-nav-menus-panel.php'             => "f",
            'class-wp-customize-nav-menu-section.php'            => "f",
            'class-wp-customize-custom-css-setting.php'          => "f",
            'class-wp-customize-new-menu-section.php'            => "f",
            'class-wp-customize-new-menu-control.php'            => "f",
            'class-wp-customize-site-icon-control.php'           => "f",
            'class-wp-customize-nav-menu-name-control.php'       => "f",
            'class-wp-customize-background-image-setting.php'    => "f",
            'class-wp-customize-nav-menu-control.php'            => "f",
            'class-wp-customize-nav-menu-setting.php'            => "f",
            'class-wp-customize-sidebar-section.php'             => "f",
            'class-wp-customize-background-image-control.php'    => "f",
            'class-wp-sidebar-block-editor-control.php'          => "f",
            'class-wp-customize-header-image-control.php'        => "f",
            'class-wp-customize-nav-menu-item-setting.php'       => "f",
            'class-wp-customize-media-control.php'               => "f",
            'class-wp-customize-nav-menu-item-control.php'       => "f",
            'class-wp-customize-nav-menu-location-control.php'   => "f",
            'class-wp-customize-nav-menu-locations-control.php'  => "f",
            'class-wp-customize-themes-section.php'              => "f",
            'class-wp-widget-area-customize-control.php'         => "f",
            'class-wp-customize-color-control.php'               => "f",
            'class-wp-customize-image-control.php'               => "f",
            'class-wp-customize-cropped-image-control.php'       => "f",
            'class-wp-customize-selective-refresh.php'           => "f",
        ),
        'class-wp-tax-query.php'                         => "f",
        'class-wp-navigation-fallback.php'               => "f",
        'class-wp-xmlrpc-server.php'                     => "f",
        'class-walker-comment.php'                       => "f",
        'media-template.php'                             => "f",
        'functions.php'                                  => "f",
        'class-wp-exception.php'                         => "f",
        'feed-rss.php'                                   => "f",
        'post-thumbnail-template.php'                    => "f",
        'class-wp-metadata-lazyloader.php'               => "f",
        'class-wp-object-cache.php'                      => "f",
        'class-wp-site.php'                              => "f",
        'class-wp-block-bindings-registry.php'           => "f",
        'navigation-fallback.php'                        => "f",
        'class-wp-post-type.php'                         => "f",
        'ms-network.php'                                 => "f",
        'class-wp-block-template.php'                    => "f",
        'class-wp-date-query.php'                        => "f",
        'class-wp-query.php'                             => "f",
        'class-wp-theme-json-resolver.php'               => "f",
        'class-wp-network-query.php'                     => "f",
        'class-wp-customize-nav-menus.php'               => "f",
        'class-walker-page-dropdown.php'                 => "f",
        'class-wp-block-type-registry.php'               => "f",
        'general-template.php'                           => "f",
        'class-wp-recovery-mode.php'                     => "f",
        'php-compat'                                     => array('readonly.php' => "f"),
        'widgets'                                        => array(
            'class-wp-widget-recent-posts.php'    => "f",
            'class-wp-widget-pages.php'           => "f",
            'class-wp-widget-media-video.php'     => "f",
            'class-wp-widget-text.php'            => "f",
            'class-wp-widget-media-image.php'     => "f",
            'class-wp-widget-meta.php'            => "f",
            'class-wp-widget-media-gallery.php'   => "f",
            'class-wp-widget-rss.php'             => "f",
            'class-wp-widget-tag-cloud.php'       => "f",
            'class-wp-nav-menu-widget.php'        => "f",
            'class-wp-widget-categories.php'      => "f",
            'class-wp-widget-block.php'           => "f",
            'class-wp-widget-calendar.php'        => "f",
            'class-wp-widget-media.php'           => "f",
            'class-wp-widget-search.php'          => "f",
            'class-wp-widget-archives.php'        => "f",
            'class-wp-widget-recent-comments.php' => "f",
            'class-wp-widget-links.php'           => "f",
            'class-wp-widget-custom-html.php'     => "f",
            'class-wp-widget-media-audio.php'     => "f",
        ),
        'class-wp-text-diff-renderer-inline.php'         => "f",
        'cron.php'                                       => "f",
        'class-walker-category-dropdown.php'             => "f",
        'post.php'                                       => "f",
        'IXR'                                            => array(
            'class-IXR-client.php'              => "f",
            'class-IXR-introspectionserver.php' => "f",
            'class-IXR-base64.php'              => "f",
            'class-IXR-date.php'                => "f",
            'class-IXR-server.php'              => "f",
            'class-IXR-request.php'             => "f",
            'class-IXR-value.php'               => "f",
            'class-IXR-message.php'             => "f",
            'class-IXR-error.php'               => "f",
            'class-IXR-clientmulticall.php'     => "f",
        ),
        'theme-previews.php'                             => "f",
        'class-walker-category.php'                      => "f",
        'class-wp-application-passwords.php'             => "f",
        'category.php'                                   => "f",
        'comment-template.php'                           => "f",
        'template-loader.php'                            => "f",
        'class-wp-classic-to-block-menu-converter.php'   => "f",
        'functions.wp-scripts.php'                       => "f",
        'style-engine.php'                               => "f",
        'class.wp-scripts.php'                           => "f",
        'query.php'                                      => "f",
        'class-wp-theme-json.php'                        => "f",
        'class-wp-duotone.php'                           => "f",
        'images'                                         => array(
            'icon-pointer-flag.png'     => "f",
            'admin-bar-sprite.png'      => "f",
            'spinner.gif'               => "f",
            'uploader-icons-2x.png'     => "f",
            'toggle-arrow-2x.png'       => "f",
            'rss.png'                   => "f",
            'arrow-pointer-blue-2x.png' => "f",
            'icon-pointer-flag-2x.png'  => "f",
            'toggle-arrow.png'          => "f",
            'wlw'                       => array(
                'wp-comments.png'  => "f",
                'wp-watermark.png' => "f",
                'wp-icon.png'      => "f",
            ),
            'media'                     => array(
                'interactive.png' => "f",
                'video.svg'       => "f",
                'document.png'    => "f",
                'text.png'        => "f",
                'code.svg'        => "f",
                'default.png'     => "f",
                'spreadsheet.png' => "f",
                'video.png'       => "f",
                'interactive.svg' => "f",
                'spreadsheet.svg' => "f",
                'audio.svg'       => "f",
                'document.svg'    => "f",
                'default.svg'     => "f",
                'audio.png'       => "f",
                'text.svg'        => "f",
                'archive.png'     => "f",
                'code.png'        => "f",
                'archive.svg'     => "f",
            ),
            'wpicons.png'               => "f",
            'rss-2x.png'                => "f",
            'arrow-pointer-blue.png'    => "f",
            'uploader-icons.png'        => "f",
            'w-logo-blue-white-bg.png'  => "f",
            'wpicons-2x.png'            => "f",
            'crystal'                   => array(
                'interactive.png' => "f",
                'license.txt'     => "f",
                'document.png'    => "f",
                'text.png'        => "f",
                'default.png'     => "f",
                'spreadsheet.png' => "f",
                'video.png'       => "f",
                'audio.png'       => "f",
                'archive.png'     => "f",
                'code.png'        => "f",
            ),
            'spinner-2x.gif'            => "f",
            'admin-bar-sprite-2x.png'   => "f",
            'w-logo-blue.png'           => "f",
            'xit-2x.gif'                => "f",
            'wpspin.gif'                => "f",
            'blank.gif'                 => "f",
            'smilies'                   => array(
                'icon_surprised.gif' => "f",
                'frownie.png'        => "f",
                'icon_cool.gif'      => "f",
                'icon_exclaim.gif'   => "f",
                'icon_razz.gif'      => "f",
                'icon_question.gif'  => "f",
                'icon_lol.gif'       => "f",
                'simple-smile.png'   => "f",
                'icon_neutral.gif'   => "f",
                'icon_mad.gif'       => "f",
                'icon_evil.gif'      => "f",
                'icon_wink.gif'      => "f",
                'icon_eek.gif'       => "f",
                'icon_smile.gif'     => "f",
                'icon_mrgreen.gif'   => "f",
                'icon_arrow.gif'     => "f",
                'icon_biggrin.gif'   => "f",
                'icon_confused.gif'  => "f",
                'mrgreen.png'        => "f",
                'rolleyes.png'       => "f",
                'icon_cry.gif'       => "f",
                'icon_twisted.gif'   => "f",
                'icon_rolleyes.gif'  => "f",
                'icon_idea.gif'      => "f",
                'icon_redface.gif'   => "f",
                'icon_sad.gif'       => "f",
            ),
            'xit.gif'                   => "f",
            'down_arrow.gif'            => "f",
            'wpspin-2x.gif'             => "f",
            'down_arrow-2x.gif'         => "f",
        ),
        'css'                                            => array(
            'buttons-rtl.min.css'             => "f",
            'editor.css'                      => "f",
            'wp-empty-template-alert.min.css' => "f",
            'wp-embed-template.css'           => "f",
            'dashicons.min.css'               => "f",
            'buttons-rtl.css'                 => "f",
            'jquery-ui-dialog-rtl.min.css'    => "f",
            'customize-preview-rtl.min.css'   => "f",
            'customize-preview-rtl.css'       => "f",
            'media-views-rtl.css'             => "f",
            'wp-auth-check.min.css'           => "f",
            'wp-pointer-rtl.min.css'          => "f",
            'wp-embed-template-ie.min.css'    => "f",
            'dashicons.css'                   => "f",
            'admin-bar.min.css'               => "f",
            'wp-pointer.min.css'              => "f",
            'admin-bar-rtl.css'               => "f",
            'wp-auth-check-rtl.css'           => "f",
            'jquery-ui-dialog-rtl.css'        => "f",
            'wp-empty-template-alert.css'     => "f",
            'classic-themes.css'              => "f",
            'buttons.min.css'                 => "f",
            'admin-bar-rtl.min.css'           => "f",
            'admin-bar.css'                   => "f",
            'wp-auth-check.css'               => "f",
            'editor.min.css'                  => "f",
            'classic-themes.min.css'          => "f",
            'editor-rtl.min.css'              => "f",
            'wp-embed-template.min.css'       => "f",
            'media-views.min.css'             => "f",
            'media-views.css'                 => "f",
            'buttons.css'                     => "f",
            'editor-rtl.css'                  => "f",
            'jquery-ui-dialog.css'            => "f",
            'media-views-rtl.min.css'         => "f",
            'dist'                            => array(
                'edit-widgets'         => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
                'block-directory'      => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
                'list-reusable-blocks' => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
                'edit-site'            => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'posts-rtl.min.css' => "f",
                    'posts.css'         => "f",
                    'posts.min.css'     => "f",
                    'style.min.css'     => "f",
                    'posts-rtl.css'     => "f",
                    'style.css'         => "f",
                ),
                'edit-post'            => array(
                    'style-rtl.min.css'   => "f",
                    'style-rtl.css'       => "f",
                    'classic.css'         => "f",
                    'classic-rtl.css'     => "f",
                    'classic.min.css'     => "f",
                    'style.min.css'       => "f",
                    'classic-rtl.min.css' => "f",
                    'style.css'           => "f",
                ),
                'commands'             => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
                'widgets'              => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
                'block-library'        => array(
                    'style-rtl.min.css'           => "f",
                    'editor.css'                  => "f",
                    'elements-rtl.css'            => "f",
                    'style-rtl.css'               => "f",
                    'reset.css'                   => "f",
                    'reset-rtl.min.css'           => "f",
                    'theme-rtl.min.css'           => "f",
                    'classic.css'                 => "f",
                    'theme.css'                   => "f",
                    'elements.min.css'            => "f",
                    'reset.min.css'               => "f",
                    'common-rtl.min.css'          => "f",
                    'editor-elements.css'         => "f",
                    'classic-rtl.css'             => "f",
                    'classic.min.css'             => "f",
                    'editor-elements-rtl.css'     => "f",
                    'editor-elements.min.css'     => "f",
                    'theme-rtl.css'               => "f",
                    'style.min.css'               => "f",
                    'reset-rtl.css'               => "f",
                    'editor.min.css'              => "f",
                    'common.css'                  => "f",
                    'editor-rtl.min.css'          => "f",
                    'theme.min.css'               => "f",
                    'elements.css'                => "f",
                    'editor-rtl.css'              => "f",
                    'classic-rtl.min.css'         => "f",
                    'style.css'                   => "f",
                    'common.min.css'              => "f",
                    'elements-rtl.min.css'        => "f",
                    'editor-elements-rtl.min.css' => "f",
                    'common-rtl.css'              => "f",
                ),
                'reusable-blocks'      => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
                'customize-widgets'    => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
                'block-editor'         => array(
                    'style-rtl.min.css'                 => "f",
                    'default-editor-styles.css'         => "f",
                    'content-rtl.min.css'               => "f",
                    'style-rtl.css'                     => "f",
                    'content-rtl.css'                   => "f",
                    'default-editor-styles.min.css'     => "f",
                    'default-editor-styles-rtl.css'     => "f",
                    'style.min.css'                     => "f",
                    'default-editor-styles-rtl.min.css' => "f",
                    'content.css'                       => "f",
                    'style.css'                         => "f",
                    'content.min.css'                   => "f",
                ),
                'format-library'       => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
                'nux'                  => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
                'components'           => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
                'preferences'          => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
                'editor'               => array(
                    'style-rtl.min.css'         => "f",
                    'editor-styles-rtl.min.css' => "f",
                    'editor-styles.css'         => "f",
                    'style-rtl.css'             => "f",
                    'editor-styles.min.css'     => "f",
                    'style.min.css'             => "f",
                    'editor-styles-rtl.css'     => "f",
                    'style.css'                 => "f",
                ),
                'patterns'             => array(
                    'style-rtl.min.css' => "f",
                    'style-rtl.css'     => "f",
                    'style.min.css'     => "f",
                    'style.css'         => "f",
                ),
            ),
            'wp-embed-template-ie.css'        => "f",
            'wp-pointer-rtl.css'              => "f",
            'customize-preview.css'           => "f",
            'customize-preview.min.css'       => "f",
            'wp-auth-check-rtl.min.css'       => "f",
            'jquery-ui-dialog.min.css'        => "f",
            'wp-pointer.css'                  => "f",
        ),
        'block-bindings.php'                             => "f",
        'wp-db.php'                                      => "f",
        'rest-api'                                       => array(
            'class-wp-rest-request.php'  => "f",
            'search'                     => array(
                'class-wp-rest-term-search-handler.php'        => "f",
                'class-wp-rest-post-format-search-handler.php' => "f",
                'class-wp-rest-search-handler.php'             => "f",
                'class-wp-rest-post-search-handler.php'        => "f",
            ),
            'fields'                     => array(
                'class-wp-rest-term-meta-fields.php'    => "f",
                'class-wp-rest-user-meta-fields.php'    => "f",
                'class-wp-rest-comment-meta-fields.php' => "f",
                'class-wp-rest-post-meta-fields.php'    => "f",
                'class-wp-rest-meta-fields.php'         => "f",
            ),
            'endpoints'                  => array(
                'class-wp-rest-font-faces-controller.php'               => "f",
                'class-wp-rest-block-patterns-controller.php'           => "f",
                'class-wp-rest-terms-controller.php'                    => "f",
                'class-wp-rest-menu-locations-controller.php'           => "f",
                'class-wp-rest-block-types-controller.php'              => "f",
                'class-wp-rest-menus-controller.php'                    => "f",
                'class-wp-rest-attachments-controller.php'              => "f",
                'class-wp-rest-template-revisions-controller.php'       => "f",
                'class-wp-rest-autosaves-controller.php'                => "f",
                'class-wp-rest-widget-types-controller.php'             => "f",
                'class-wp-rest-post-statuses-controller.php'            => "f",
                'class-wp-rest-posts-controller.php'                    => "f",
                'class-wp-rest-blocks-controller.php'                   => "f",
                'class-wp-rest-menu-items-controller.php'               => "f",
                'class-wp-rest-global-styles-controller.php'            => "f",
                'class-wp-rest-edit-site-export-controller.php'         => "f",
                'class-wp-rest-search-controller.php'                   => "f",
                'class-wp-rest-sidebars-controller.php'                 => "f",
                'class-wp-rest-font-families-controller.php'            => "f",
                'class-wp-rest-site-health-controller.php'              => "f",
                'class-wp-rest-font-collections-controller.php'         => "f",
                'class-wp-rest-taxonomies-controller.php'               => "f",
                'class-wp-rest-themes-controller.php'                   => "f",
                'class-wp-rest-navigation-fallback-controller.php'      => "f",
                'class-wp-rest-block-directory-controller.php'          => "f",
                'class-wp-rest-comments-controller.php'                 => "f",
                'class-wp-rest-widgets-controller.php'                  => "f",
                'class-wp-rest-templates-controller.php'                => "f",
                'class-wp-rest-global-styles-revisions-controller.php'  => "f",
                'class-wp-rest-controller.php'                          => "f",
                'class-wp-rest-plugins-controller.php'                  => "f",
                'class-wp-rest-block-renderer-controller.php'           => "f",
                'class-wp-rest-url-details-controller.php'              => "f",
                'class-wp-rest-settings-controller.php'                 => "f",
                'class-wp-rest-revisions-controller.php'                => "f",
                'class-wp-rest-users-controller.php'                    => "f",
                'class-wp-rest-block-pattern-categories-controller.php' => "f",
                'class-wp-rest-pattern-directory-controller.php'        => "f",
                'class-wp-rest-application-passwords-controller.php'    => "f",
                'class-wp-rest-post-types-controller.php'               => "f",
                'class-wp-rest-template-autosaves-controller.php'       => "f",
            ),
            'class-wp-rest-server.php'   => "f",
            'class-wp-rest-response.php' => "f",
        ),
        'media.php'                                      => "f",
        'functions.wp-styles.php'                        => "f",
        'class-wp-textdomain-registry.php'               => "f",
        'atomlib.php'                                    => "f",
        'spl-autoload-compat.php'                        => "f",
        'formatting.php'                                 => "f",
        'class-wp-site-query.php'                        => "f",
        'revision.php'                                   => "f",
        'class-json.php'                                 => "f",
        'class-wp-image-editor.php'                      => "f",
        'https-detection.php'                            => "f",
        'class-wp-customize-setting.php'                 => "f",
        'class-wp-phpmailer.php'                         => "f",
        'class-avif-info.php'                            => "f",
        'sitemaps'                                       => array(
            'class-wp-sitemaps-provider.php'   => "f",
            'class-wp-sitemaps-renderer.php'   => "f",
            'class-wp-sitemaps-stylesheet.php' => "f",
            'class-wp-sitemaps-index.php'      => "f",
            'class-wp-sitemaps.php'            => "f",
            'class-wp-sitemaps-registry.php'   => "f",
            'providers'                        => array(
                'class-wp-sitemaps-posts.php'      => "f",
                'class-wp-sitemaps-taxonomies.php' => "f",
                'class-wp-sitemaps-users.php'      => "f",
            ),
        ),
        'class-wp-plugin-dependencies.php'               => "f",
        'https-migration.php'                            => "f",
        'ms-settings.php'                                => "f",
        'class-wp-theme-json-data.php'                   => "f",
        'class-wp-scripts.php'                           => "f",
        'class-walker-nav-menu.php'                      => "f",
        'class-wp-script-modules.php'                    => "f",
        'rss.php'                                        => "f",
        'taxonomy.php'                                   => "f",
        'rss-functions.php'                              => "f",
        'class-wp-block-metadata-registry.php'           => "f",
        'rest-api.php'                                   => "f",
        'session.php'                                    => "f",
        'class-wp-block-styles-registry.php'             => "f",
        'nav-menu-template.php'                          => "f",
        'admin-bar.php'                                  => "f",
        'class-wp-block-pattern-categories-registry.php' => "f",
        'class.wp-styles.php'                            => "f",
        'class-wp-block-type.php'                        => "f",
        'feed-atom.php'                                  => "f",
        'class-wp-post.php'                              => "f",
        'date.php'                                       => "f",
        'pomo'                                           => array(
            'translations.php' => "f",
            'mo.php'           => "f",
            'entry.php'        => "f",
            'po.php'           => "f",
            'plural-forms.php' => "f",
            'streams.php'      => "f",
        ),
        'class-wp-block-editor-context.php'              => "f",
        'widgets.php'                                    => "f",
        'wlwmanifest.xml'                                => "f",
        'class-wp-block-bindings-source.php'             => "f",
        'class-wp-session-tokens.php'                    => "f",
        'class-wp-taxonomy.php'                          => "f",
        'class-wp-simplepie-file.php'                    => "f",
        'class-wp-block-templates-registry.php'          => "f",
        'block-patterns'                                 => array(
            'query-grid-posts.php'                     => "f",
            'social-links-shared-background-color.php' => "f",
            'query-offset-posts.php'                   => "f",
            'heading-paragraph.php'                    => "f",
            'query-large-title-posts.php'              => "f",
            'text-three-columns-buttons.php'           => "f",
            'query-small-posts.php'                    => "f",
            'query-standard-posts.php'                 => "f",
            'two-buttons.php'                          => "f",
            'text-two-columns.php'                     => "f",
            'text-two-columns-with-images.php'         => "f",
            'two-images.php'                           => "f",
            'quote.php'                                => "f",
            'three-buttons.php'                        => "f",
            'large-header.php'                         => "f",
            'query-medium-posts.php'                   => "f",
            'large-header-button.php'                  => "f",
        ),
        'class-wp-rewrite.php'                           => "f",
        'class-wp-recovery-mode-email-service.php'       => "f",
        'class-wp-meta-query.php'                        => "f",
        'class-wp-term.php'                              => "f",
        'bookmark-template.php'                          => "f",
        'default-widgets.php'                            => "f",
        'class-phpmailer.php'                            => "f",
        'class-wp-matchesmapregex.php'                   => "f",
        'class-wp-styles.php'                            => "f",
        'class-wp-image-editor-imagick.php'              => "f",
        'class-wp-feed-cache-transient.php'              => "f",
        'class-wp-feed-cache.php'                        => "f",
        'class-wp-widget.php'                            => "f",
        'class-wp-roles.php'                             => "f",
        'global-styles-and-settings.php'                 => "f",
        'meta.php'                                       => "f",
        'class-pop3.php'                                 => "f",
        'ms-default-filters.php'                         => "f",
        'class-wp-comment.php'                           => "f",
        'class-wp-customize-widgets.php'                 => "f",
        'ms-functions.php'                               => "f",
        'class-wp-error.php'                             => "f",
        'l10n.php'                                       => "f",
        'theme-i18n.json'                                => "f",
        'class-wp-paused-extensions-storage.php'         => "f",
        'class-walker-page.php'                          => "f",
        'class-wp-image-editor-gd.php'                   => "f",
        'registration.php'                               => "f",
        'pluggable-deprecated.php'                       => "f",
        'class-wp-editor.php'                            => "f",
        'author-template.php'                            => "f",
        'class-wp-http-response.php'                     => "f",
        'class-wp-simplepie-sanitize-kses.php'           => "f",
        'feed-rss2.php'                                  => "f",
        'script-modules.php'                             => "f",
        'assets'                                         => array(
            'script-loader-react-refresh-runtime.min.php' => "f",
            'script-modules-packages.php'                 => "f",
            'script-modules-packages.min.php'             => "f",
            'script-loader-packages.php'                  => "f",
            'script-loader-packages.min.php'              => "f",
            'script-loader-react-refresh-entry.php'       => "f",
            'script-loader-react-refresh-entry.min.php'   => "f",
            'script-loader-react-refresh-runtime.php'     => "f",
        ),
        'locale.php'                                     => "f",
        'plugin.php'                                     => "f",
        'class-wp-admin-bar.php'                         => "f",
        'comment.php'                                    => "f",
        'class-wp-speculation-rules.php'                 => "f",
        'style-engine'                                   => array(
            'class-wp-style-engine-css-declarations.php' => "f",
            'class-wp-style-engine-css-rule.php'         => "f",
            'class-wp-style-engine-css-rules-store.php'  => "f",
            'class-wp-style-engine-processor.php'        => "f",
            'class-wp-style-engine.php'                  => "f",
        ),
        'capabilities.php'                               => "f",
        'class-smtp.php'                                 => "f",
        'class-feed.php'                                 => "f",
        'class-wp-widget-factory.php'                    => "f",
        'class-wp-http-curl.php'                         => "f",
        'class-wp-ajax-response.php'                     => "f",
        'ms-files.php'                                   => "f",
        'class-wp-network.php'                           => "f",
        'class-wp.php'                                   => "f",
        'error-protection.php'                           => "f",
        'class-wp-block-parser-frame.php'                => "f",
        'option.php'                                     => "f",
        'class-wp-embed.php'                             => "f",
        'nav-menu.php'                                   => "f",
        'blocks'                                         => array(
            'query-no-results.php'             => "f",
            'missing'                          => array('block.json' => "f"),
            'post-navigation-link'             => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'post-comments.php'                => "f",
            'query-pagination-previous'        => array('block.json' => "f"),
            'post-date.php'                    => "f",
            'post-comments-form'               => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'cover.php'                        => "f",
            'verse'                            => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'social-link.php'                  => "f",
            'query-total'                      => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'read-more.php'                    => "f",
            'block.php'                        => "f",
            'file.php'                         => "f",
            'comments-pagination.php'          => "f",
            'legacy-widget.php'                => "f",
            'blocks-json.php'                  => "f",
            'comment-date'                     => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'post-terms.php'                   => "f",
            'code'                             => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.min.css'  => "f",
                'theme.css'          => "f",
                'theme-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'media-text'                       => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'more'                             => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'shortcode.php'                    => "f",
            'query-title'                      => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'post-content.php'                 => "f",
            'post-terms'                       => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'gallery'                          => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.min.css'  => "f",
                'theme.css'          => "f",
                'theme-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'loginout.php'                     => "f",
            'group'                            => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.min.css'  => "f",
                'theme.css'          => "f",
                'theme-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'search'                           => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.min.css'  => "f",
                'view.asset.php'     => "f",
                'view.min.js'        => "f",
                'view.min.asset.php' => "f",
                'theme.css'          => "f",
                'view.js'            => "f",
                'theme-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'table'                            => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.min.css'  => "f",
                'theme.css'          => "f",
                'theme-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'home-link.php'                    => "f",
            'video'                            => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.min.css'  => "f",
                'theme.css'          => "f",
                'theme-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'template-part.php'                => "f",
            'avatar.php'                       => "f",
            'post-featured-image.php'          => "f",
            'comments-pagination-numbers'      => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'comments-query-loop'              => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'post-title.php'                   => "f",
            'comment-content.php'              => "f",
            'post-excerpt.php'                 => "f",
            'require-dynamic-blocks.php'       => "f",
            'nextpage'                         => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'navigation-link'                  => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'latest-posts.php'                 => "f",
            'page-list'                        => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'tag-cloud.php'                    => "f",
            'post-content'                     => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'comment-author-name'              => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'query-no-results'                 => array('block.json' => "f"),
            'image.php'                        => "f",
            'navigation-submenu'               => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'pullquote'                        => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.min.css'  => "f",
                'theme.css'          => "f",
                'theme-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'navigation.php'                   => "f",
            'comments-pagination-next'         => array('block.json' => "f"),
            'comment-template.php'             => "f",
            'pattern.php'                      => "f",
            'comment-edit-link.php'            => "f",
            'require-static-blocks.php'        => "f",
            'post-date'                        => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'query.php'                        => "f",
            'columns'                          => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'html'                             => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'buttons'                          => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'site-title'                       => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'latest-posts'                     => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'navigation-submenu.php'           => "f",
            'query-pagination-numbers.php'     => "f",
            'navigation'                       => array(
                'style-rtl.min.css'        => "f",
                'editor.css'               => "f",
                'block.json'               => "f",
                'style-rtl.css'            => "f",
                'view.asset.php'           => "f",
                'view.min.js'              => "f",
                'view.min.asset.php'       => "f",
                'view-modal.asset.php'     => "f",
                'view-modal.js'            => "f",
                'view.js'                  => "f",
                'style.min.css'            => "f",
                'editor.min.css'           => "f",
                'editor-rtl.min.css'       => "f",
                'editor-rtl.css'           => "f",
                'style.css'                => "f",
                'view-modal.min.asset.php' => "f",
                'view-modal.min.js'        => "f",
            ),
            'button'                           => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'comments'                         => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'post-navigation-link.php'         => "f",
            'pattern'                          => array('block.json' => "f"),
            'post-author'                      => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'spacer'                           => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'search.php'                       => "f",
            'column'                           => array('block.json' => "f"),
            'loginout'                         => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'block'                            => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'rss.php'                          => "f",
            'embed'                            => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.min.css'  => "f",
                'theme.css'          => "f",
                'theme-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'query-pagination-previous.php'    => "f",
            'post-author.php'                  => "f",
            'archives.php'                     => "f",
            'categories'                       => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'preformatted'                     => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'post-comments-form.php'           => "f",
            'footnotes.php'                    => "f",
            'audio'                            => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.min.css'  => "f",
                'theme.css'          => "f",
                'theme-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'page-list-item'                   => array('block.json' => "f"),
            'post-comments'                    => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'social-link'                      => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'comments-pagination-previous.php' => "f",
            'query-title.php'                  => "f",
            'latest-comments'                  => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'comment-reply-link'               => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'latest-comments.php'              => "f",
            'rss'                              => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'site-tagline.php'                 => "f",
            'cover'                            => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'comments-pagination'              => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'tag-cloud'                        => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'media-text.php'                   => "f",
            'site-title.php'                   => "f",
            'heading'                          => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'comment-reply-link.php'           => "f",
            'comment-template'                 => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'query-pagination'                 => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'comment-content'                  => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'comment-edit-link'                => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'term-description'                 => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'site-logo'                        => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'query'                            => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'view.asset.php'     => "f",
                'view.min.js'        => "f",
                'view.min.asset.php' => "f",
                'view.js'            => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'comment-date.php'                 => "f",
            'comments-title.php'               => "f",
            'classic'                          => array('block.json' => "f"),
            'index.php'                        => "f",
            'post-excerpt'                     => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'shortcode'                        => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'post-author-biography'            => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'post-template'                    => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'query-total.php'                  => "f",
            'navigation-link.php'              => "f",
            'subhead'                          => array('block.json' => "f"),
            'read-more'                        => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'comments-pagination-previous'     => array('block.json' => "f"),
            'site-logo.php'                    => "f",
            'query-pagination-numbers'         => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'gallery.php'                      => "f",
            'button.php'                       => "f",
            'list-item'                        => array('block.json' => "f"),
            'query-pagination-next.php'        => "f",
            'comments-pagination-next.php'     => "f",
            'categories.php'                   => "f",
            'term-description.php'             => "f",
            'social-links'                     => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'details'                          => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'widget-group'                     => array('block.json' => "f"),
            'text-columns'                     => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'home-link'                        => array('block.json' => "f"),
            'query-pagination.php'             => "f",
            'page-list.php'                    => "f",
            'footnotes'                        => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'post-author-name'                 => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'post-author-name.php'             => "f",
            'legacy-widget'                    => array('block.json' => "f"),
            'comments-title'                   => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'freeform'                         => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
            ),
            'separator'                        => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.min.css'  => "f",
                'theme.css'          => "f",
                'theme-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'calendar.php'                     => "f",
            'page-list-item.php'               => "f",
            'paragraph'                        => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'site-tagline'                     => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'file'                             => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'view.asset.php'     => "f",
                'view.min.js'        => "f",
                'view.min.asset.php' => "f",
                'view.js'            => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'query-pagination-next'            => array('block.json' => "f"),
            'post-featured-image'              => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'quote'                            => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'theme-rtl.min.css' => "f",
                'theme.css'         => "f",
                'theme-rtl.css'     => "f",
                'style.min.css'     => "f",
                'theme.min.css'     => "f",
                'style.css'         => "f",
            ),
            'comments.php'                     => "f",
            'avatar'                           => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'list'                             => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'image'                            => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.min.css'  => "f",
                'view.asset.php'     => "f",
                'view.min.js'        => "f",
                'view.min.asset.php' => "f",
                'theme.css'          => "f",
                'view.js'            => "f",
                'theme-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'comments-pagination-numbers.php'  => "f",
            'calendar'                         => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'comment-author-name.php'          => "f",
            'heading.php'                      => "f",
            'post-author-biography.php'        => "f",
            'post-title'                       => array(
                'style-rtl.min.css' => "f",
                'block.json'        => "f",
                'style-rtl.css'     => "f",
                'style.min.css'     => "f",
                'style.css'         => "f",
            ),
            'template-part'                    => array(
                'editor.css'         => "f",
                'block.json'         => "f",
                'theme-rtl.min.css'  => "f",
                'theme.css'          => "f",
                'theme-rtl.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'theme.min.css'      => "f",
                'editor-rtl.css'     => "f",
            ),
            'archives'                         => array(
                'style-rtl.min.css'  => "f",
                'editor.css'         => "f",
                'block.json'         => "f",
                'style-rtl.css'      => "f",
                'style.min.css'      => "f",
                'editor.min.css'     => "f",
                'editor-rtl.min.css' => "f",
                'editor-rtl.css'     => "f",
                'style.css'          => "f",
            ),
            'widget-group.php'                 => "f",
            'post-template.php'                => "f",
            'list.php'                         => "f",
        ),
        'ms-blogs.php'                                   => "f",
        'block-supports'                                 => array(
            'settings.php'               => "f",
            'dimensions.php'             => "f",
            'layout.php'                 => "f",
            'colors.php'                 => "f",
            'generated-classname.php'    => "f",
            'duotone.php'                => "f",
            'custom-classname.php'       => "f",
            'border.php'                 => "f",
            'utils.php'                  => "f",
            'elements.php'               => "f",
            'typography.php'             => "f",
            'spacing.php'                => "f",
            'background.php'             => "f",
            'aria-label.php'             => "f",
            'block-style-variations.php' => "f",
            'position.php'               => "f",
            'align.php'                  => "f",
            'shadow.php'                 => "f",
        ),
        'class-wp-http.php'                              => "f",
        'class-wp-recovery-mode-key-service.php'         => "f",
        'class-wpdb.php'                                 => "f",
        'sitemaps.php'                                   => "f",
        'class-wp-locale-switcher.php'                   => "f",
        'template-canvas.php'                            => "f",
        'class-wp-user.php'                              => "f",
        'class-wp-http-ixr-client.php'                   => "f",
        'compat.php'                                     => "f",
        'certificates'                                   => array('ca-bundle.crt' => "f"),
        'class-http.php'                                 => "f",
        'class-wp-http-encoding.php'                     => "f",
        'class-wp-theme.php'                             => "f",
        'fonts.php'                                      => "f",
        'theme-templates.php'                            => "f",
        'feed-rss2-comments.php'                         => "f",
        'link-template.php'                              => "f",
        'speculative-loading.php'                        => "f",
        'class-wp-fatal-error-handler.php'               => "f",
        'class-wp-block-parser-block.php'                => "f",
        'class-wp-token-map.php'                         => "f",
        'robots-template.php'                            => "f",
        'ms-deprecated.php'                              => "f",
        'kses.php'                                       => "f",
        'theme.json'                                     => "f",
        'block-patterns.php'                             => "f",
        'feed-atom-comments.php'                         => "f",
        'version.php'                                    => "f",
        'theme-compat'                                   => array(
            'embed.php'          => "f",
            'embed-404.php'      => "f",
            'footer-embed.php'   => "f",
            'footer.php'         => "f",
            'sidebar.php'        => "f",
            'embed-content.php'  => "f",
            'header.php'         => "f",
            'header-embed.php'   => "f",
            'comments-popup.php' => "f",
            'comments.php'       => "f",
        ),
        'class-wp-dependencies.php'                      => "f",
        'class-wp-recovery-mode-link-service.php'        => "f",
        'class-phpass.php'                               => "f",
        'class-wp-recovery-mode-cookie-service.php'      => "f",
        'class-wp-dependency.php'                        => "f",
        'class-wp-http-proxy.php'                        => "f",
        'post-formats.php'                               => "f",
        'class-wp-text-diff-renderer-table.php'          => "f",
        'wp-diff.php'                                    => "f",
        'class-requests.php'                             => "f",
        'feed-rdf.php'                                   => "f",
        'class-wp-user-request.php'                      => "f",
        'default-filters.php'                            => "f",
        'deprecated.php'                                 => "f",
        'class-wp-block-patterns-registry.php'           => "f",
        'class-wp-http-streams.php'                      => "f",
        'class-IXR.php'                                  => "f",
        'class-wp-oembed.php'                            => "f",
        'block-editor.php'                               => "f",
        'class-wp-customize-control.php'                 => "f",
        'class-wp-customize-panel.php'                   => "f",
        'pluggable.php'                                  => "f",
        'interactivity-api'                              => array(
            'class-wp-interactivity-api-directives-processor.php' => "f",
            'class-wp-interactivity-api.php'                      => "f",
            'interactivity-api.php'                               => "f",
        ),
        'class-wp-url-pattern-prefixer.php'              => "f",
        'random_compat'                                  => array(
            'random_bytes_libsodium.php'        => "f",
            'random_bytes_com_dotnet.php'       => "f",
            'random_bytes_libsodium_legacy.php' => "f",
            'random_int.php'                    => "f",
            'random_bytes_openssl.php'          => "f",
            'random.php'                        => "f",
            'error_polyfill.php'                => "f",
            'random_bytes_mcrypt.php'           => "f",
            'byte_safe_strings.php'             => "f",
            'cast_to_int.php'                   => "f",
            'random_bytes_dev_urandom.php'      => "f",
        ),
        'class-wp-block-parser.php'                      => "f",
        'class-wp-locale.php'                            => "f",
        'shortcodes.php'                                 => "f",
        'class.wp-dependencies.php'                      => "f",
        'post-template.php'                              => "f",
        'template.php'                                   => "f",
        'l10n'                                           => array(
            'class-wp-translations.php'           => "f",
            'class-wp-translation-controller.php' => "f",
            'class-wp-translation-file-php.php'   => "f",
            'class-wp-translation-file-mo.php'    => "f",
            'class-wp-translation-file.php'       => "f",
        ),
        'http.php'                                       => "f",
        'rewrite.php'                                    => "f",
        'class-wp-http-cookie.php'                       => "f",
        'class-wp-user-query.php'                        => "f",
    ),
);
