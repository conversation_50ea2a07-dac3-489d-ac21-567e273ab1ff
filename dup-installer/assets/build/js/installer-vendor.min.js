(()=>{var t={328:(t,e,i)=>{var n,s,o;s=[i(692)],void 0===(o="function"==typeof(n=function(t){"use strict";var e=/\r?\n/g,i=/^(?:submit|button|image|reset|file)$/i,n=/^(?:input|select|textarea|keygen)/i,s=/^(?:checkbox|radio)$/i;t.fn.serializeJSON=function(e){var i=t.serializeJSON,n=this,s=i.setupOpts(e),o=t.extend({},s.defaultTypes,s.customTypes),r=i.serializeArray(n,s),a={};return t.each(r,(function(e,n){var r=n.name,l=t(n.el).attr("data-value-type");if(!l&&!s.disableColonTypes){var h=i.splitType(n.name);r=h[0],l=h[1]}if("skip"!==l){l||(l=s.defaultType);var u=i.applyTypeFunc(n.name,n.value,l,n.el,o);if(u||!i.shouldSkipFalsy(n.name,r,l,n.el,s)){var c=i.splitInputNameIntoKeysArray(r);i.deepSet(a,c,u,s)}}})),a},t.serializeJSON={defaultOptions:{},defaultBaseOptions:{checkboxUncheckedValue:void 0,useIntKeysAsArrayIndex:!1,skipFalsyValuesForTypes:[],skipFalsyValuesForFields:[],disableColonTypes:!1,customTypes:{},defaultTypes:{string:function(t){return String(t)},number:function(t){return Number(t)},boolean:function(t){return-1===["false","null","undefined","","0"].indexOf(t)},null:function(t){return-1===["false","null","undefined","","0"].indexOf(t)?t:null},array:function(t){return JSON.parse(t)},object:function(t){return JSON.parse(t)},skip:null},defaultType:"string"},setupOpts:function(e){null==e&&(e={});var i=t.serializeJSON,n=["checkboxUncheckedValue","useIntKeysAsArrayIndex","skipFalsyValuesForTypes","skipFalsyValuesForFields","disableColonTypes","customTypes","defaultTypes","defaultType"];for(var s in e)if(-1===n.indexOf(s))throw new Error("serializeJSON ERROR: invalid option '"+s+"'. Please use one of "+n.join(", "));return t.extend({},i.defaultBaseOptions,i.defaultOptions,e)},serializeArray:function(o,r){null==r&&(r={});var a=t.serializeJSON;return o.map((function(){var e=t.prop(this,"elements");return e?t.makeArray(e):this})).filter((function(){var e=t(this),o=this.type;return this.name&&!e.is(":disabled")&&n.test(this.nodeName)&&!i.test(o)&&(this.checked||!s.test(o)||null!=a.getCheckboxUncheckedValue(e,r))})).map((function(i,n){var o=t(this),l=o.val(),u=this.type;return null==l?null:(s.test(u)&&!this.checked&&(l=a.getCheckboxUncheckedValue(o,r)),h(l)?t.map(l,(function(t){return{name:n.name,value:t.replace(e,"\r\n"),el:n}})):{name:n.name,value:l.replace(e,"\r\n"),el:n})})).get()},getCheckboxUncheckedValue:function(t,e){var i=t.attr("data-unchecked-value");return null==i&&(i=e.checkboxUncheckedValue),i},applyTypeFunc:function(t,e,i,n,s){var r=s[i];if(!r)throw new Error("serializeJSON ERROR: Invalid type "+i+" found in input name '"+t+"', please use one of "+o(s).join(", "));return r(e,n)},splitType:function(t){var e=t.split(":");if(e.length>1){var i=e.pop();return[e.join(":"),i]}return[t,""]},shouldSkipFalsy:function(e,i,n,s,o){var r=t(s).attr("data-skip-falsy");if(null!=r)return"false"!==r;var a=o.skipFalsyValuesForFields;if(a&&(-1!==a.indexOf(i)||-1!==a.indexOf(e)))return!0;var l=o.skipFalsyValuesForTypes;return!(!l||-1===l.indexOf(n))},splitInputNameIntoKeysArray:function(e){var i=e.split("[");return""===(i=t.map(i,(function(t){return t.replace(/\]/g,"")})))[0]&&i.shift(),i},deepSet:function(e,i,n,s){null==s&&(s={});var o=t.serializeJSON;if(a(e))throw new Error("ArgumentError: param 'o' expected to be an object or array, found undefined");if(!i||0===i.length)throw new Error("ArgumentError: param 'keys' expected to be an array with least one element");var u=i[0];if(1!==i.length){var c=i[1],d=i.slice(1);if(""===u){var p=e.length-1,f=e[p];u=r(f)&&a(o.deepGet(f,d))?p:p+1}""===c||s.useIntKeysAsArrayIndex&&l(c)?!a(e[u])&&h(e[u])||(e[u]=[]):!a(e[u])&&r(e[u])||(e[u]={}),o.deepSet(e[u],d,n,s)}else""===u?e.push(n):e[u]=n},deepGet:function(e,i){var n=t.serializeJSON;if(a(e)||a(i)||0===i.length||!r(e)&&!h(e))return e;var s=i[0];if(""!==s){if(1===i.length)return e[s];var o=i.slice(1);return n.deepGet(e[s],o)}}};var o=function(t){if(Object.keys)return Object.keys(t);var e,i=[];for(e in t)i.push(e);return i},r=function(t){return t===Object(t)},a=function(t){return void 0===t},l=function(t){return/^[0-9]+$/.test(String(t))},h=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)}})?n.apply(e,s):n)||(t.exports=o)},409:function(t,e,i){t.exports=function(t){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}function n(){return n=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},n.apply(this,arguments)}function s(t,e){return a(t)||h(t,e)||c()}function o(t){return r(t)||l(t)||u()}function r(t){if(Array.isArray(t)){for(var e=0,i=new Array(t.length);e<t.length;e++)i[e]=t[e];return i}}function a(t){if(Array.isArray(t))return t}function l(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function h(t,e){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t)){var i=[],n=!0,s=!1,o=void 0;try{for(var r,a=t[Symbol.iterator]();!(n=(r=a.next()).done)&&(i.push(r.value),!e||i.length!==e);n=!0);}catch(t){s=!0,o=t}finally{try{n||null==a.return||a.return()}finally{if(s)throw o}}return i}}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}var d=1,p={},f={attr:function(t,e,i){var n,s,o,r=new RegExp("^"+e,"i");if(void 0===i)i={};else for(n in i)i.hasOwnProperty(n)&&delete i[n];if(!t)return i;for(n=(o=t.attributes).length;n--;)(s=o[n])&&s.specified&&r.test(s.name)&&(i[this.camelize(s.name.slice(e.length))]=this.deserializeValue(s.value));return i},checkAttr:function(t,e,i){return t.hasAttribute(e+i)},setAttr:function(t,e,i,n){t.setAttribute(this.dasherize(e+i),String(n))},getType:function(t){return t.getAttribute("type")||"text"},generateID:function(){return""+d++},deserializeValue:function(t){var e;try{return t?"true"==t||"false"!=t&&("null"==t?null:isNaN(e=Number(t))?/^[\[\{]/.test(t)?JSON.parse(t):t:e):t}catch(e){return t}},camelize:function(t){return t.replace(/-+(.)?/g,(function(t,e){return e?e.toUpperCase():""}))},dasherize:function(t){return t.replace(/::/g,"/").replace(/([A-Z]+)([A-Z][a-z])/g,"$1_$2").replace(/([a-z\d])([A-Z])/g,"$1_$2").replace(/_/g,"-").toLowerCase()},warn:function(){var t;window.console&&"function"==typeof window.console.warn&&(t=window.console).warn.apply(t,arguments)},warnOnce:function(t){p[t]||(p[t]=!0,this.warn.apply(this,arguments))},_resetWarnings:function(){p={}},trimString:function(t){return t.replace(/^\s+|\s+$/g,"")},parse:{date:function(t){var e=t.match(/^(\d{4,})-(\d\d)-(\d\d)$/);if(!e)return null;var i=s(e.map((function(t){return parseInt(t,10)})),4),n=(i[0],i[1]),o=i[2],r=i[3],a=new Date(n,o-1,r);return a.getFullYear()!==n||a.getMonth()+1!==o||a.getDate()!==r?null:a},string:function(t){return t},integer:function(t){return isNaN(t)?null:parseInt(t,10)},number:function(t){if(isNaN(t))throw null;return parseFloat(t)},boolean:function(t){return!/^\s*false\s*$/i.test(t)},object:function(t){return f.deserializeValue(t)},regexp:function(t){var e="";return/^\/.*\/(?:[gimy]*)$/.test(t)?(e=t.replace(/.*\/([gimy]*)$/,"$1"),t=t.replace(new RegExp("^/(.*?)/"+e+"$"),"$1")):t="^"+t+"$",new RegExp(t,e)}},parseRequirement:function(t,e){var i=this.parse[t||"string"];if(!i)throw'Unknown requirement specification: "'+t+'"';var n=i(e);if(null===n)throw"Requirement is not a ".concat(t,': "').concat(e,'"');return n},namespaceEvents:function(e,i){return(e=this.trimString(e||"").split(/\s+/))[0]?t.map(e,(function(t){return"".concat(t,".").concat(i)})).join(" "):""},difference:function(e,i){var n=[];return t.each(e,(function(t,e){-1==i.indexOf(e)&&n.push(e)})),n},all:function(e){return t.when.apply(t,o(e).concat([42,42]))},objectCreate:Object.create||function(){var t=function(){};return function(i){if(arguments.length>1)throw Error("Second argument not supported");if("object"!=e(i))throw TypeError("Argument must be an object");t.prototype=i;var n=new t;return t.prototype=null,n}}(),_SubmitSelector:'input[type="submit"], button:submit'},g={namespace:"data-parsley-",inputs:"input, textarea, select",excluded:"input[type=button], input[type=submit], input[type=reset], input[type=hidden]",priorityEnabled:!0,multiple:null,group:null,uiEnabled:!0,validationThreshold:3,focus:"first",trigger:!1,triggerAfterFailure:"input",errorClass:"parsley-error",successClass:"parsley-success",classHandler:function(t){},errorsContainer:function(t){},errorsWrapper:'<ul class="parsley-errors-list"></ul>',errorTemplate:"<li></li>"},m=function(){this.__id__=f.generateID()};m.prototype={asyncSupport:!0,_pipeAccordingToValidationResult:function(){var e=this,i=function(){var i=t.Deferred();return!0!==e.validationResult&&i.reject(),i.resolve().promise()};return[i,i]},actualizeOptions:function(){return f.attr(this.element,this.options.namespace,this.domOptions),this.parent&&this.parent.actualizeOptions&&this.parent.actualizeOptions(),this},_resetOptions:function(t){for(var e in this.domOptions=f.objectCreate(this.parent.options),this.options=f.objectCreate(this.domOptions),t)t.hasOwnProperty(e)&&(this.options[e]=t[e]);this.actualizeOptions()},_listeners:null,on:function(t,e){return this._listeners=this._listeners||{},(this._listeners[t]=this._listeners[t]||[]).push(e),this},subscribe:function(e,i){t.listenTo(this,e.toLowerCase(),i)},off:function(t,e){var i=this._listeners&&this._listeners[t];if(i)if(e)for(var n=i.length;n--;)i[n]===e&&i.splice(n,1);else delete this._listeners[t];return this},unsubscribe:function(e,i){t.unsubscribeTo(this,e.toLowerCase())},trigger:function(t,e,i){e=e||this;var n,s=this._listeners&&this._listeners[t];if(s)for(var o=s.length;o--;)if(!1===(n=s[o].call(e,e,i)))return n;return!this.parent||this.parent.trigger(t,e,i)},asyncIsValid:function(t,e){return f.warnOnce("asyncIsValid is deprecated; please use whenValid instead"),this.whenValid({group:t,force:e})},_findRelated:function(){return this.options.multiple?t(this.parent.element.querySelectorAll("[".concat(this.options.namespace,'multiple="').concat(this.options.multiple,'"]'))):this.$element}};var v=function(t,e){var i=t.match(/^\s*\[(.*)\]\s*$/);if(!i)throw'Requirement is not an array: "'+t+'"';var n=i[1].split(",").map(f.trimString);if(n.length!==e)throw"Requirement has "+n.length+" values when "+e+" are needed";return n},_=function(t,e,i){var n=null,s={};for(var o in t)if(o){var r=i(o);"string"==typeof r&&(r=f.parseRequirement(t[o],r)),s[o]=r}else n=f.parseRequirement(t[o],e);return[n,s]},b=function(e){t.extend(!0,this,e)};b.prototype={validate:function(t,e){if(this.fn)return arguments.length>3&&(e=[].slice.call(arguments,1,-1)),this.fn(t,e);if(Array.isArray(t)){if(!this.validateMultiple)throw"Validator `"+this.name+"` does not handle multiple values";return this.validateMultiple.apply(this,arguments)}var i=arguments[arguments.length-1];if(this.validateDate&&i._isDateInput())return arguments[0]=f.parse.date(arguments[0]),null!==arguments[0]&&this.validateDate.apply(this,arguments);if(this.validateNumber)return!t||!isNaN(t)&&(arguments[0]=parseFloat(arguments[0]),this.validateNumber.apply(this,arguments));if(this.validateString)return this.validateString.apply(this,arguments);throw"Validator `"+this.name+"` only handles multiple values"},parseRequirements:function(e,i){if("string"!=typeof e)return Array.isArray(e)?e:[e];var n=this.requirementType;if(Array.isArray(n)){for(var s=v(e,n.length),o=0;o<s.length;o++)s[o]=f.parseRequirement(n[o],s[o]);return s}return t.isPlainObject(n)?_(n,e,i):[f.parseRequirement(n,e)]},requirementType:"string",priority:2};var y=function(t,e){this.__class__="ValidatorRegistry",this.locale="en",this.init(t||{},e||{})},w={email:/^((([a-zA-Z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-zA-Z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-zA-Z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-zA-Z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-zA-Z]|\d|-|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-zA-Z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-zA-Z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-zA-Z]|\d|-|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-zA-Z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))$/,number:/^-?(\d*\.)?\d+(e[-+]?\d+)?$/i,integer:/^-?\d+$/,digits:/^\d+$/,alphanum:/^\w+$/i,date:{test:function(t){return null!==f.parse.date(t)}},url:new RegExp("^(?:(?:https?|ftp)://)?(?:\\S+(?::\\S*)?@)?(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-zA-Z\\u00a1-\\uffff0-9]-*)*[a-zA-Z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-zA-Z\\u00a1-\\uffff0-9]-*)*[a-zA-Z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-zA-Z\\u00a1-\\uffff]{2,})))(?::\\d{2,5})?(?:/\\S*)?$")};w.range=w.number;var x=function(t){var e=(""+t).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return e?Math.max(0,(e[1]?e[1].length:0)-(e[2]?+e[2]:0)):0},C=function(t,e){return e.map(f.parse[t])},k=function(t,e){return function(i){for(var n=arguments.length,s=new Array(n>1?n-1:0),r=1;r<n;r++)s[r-1]=arguments[r];return s.pop(),e.apply(void 0,[i].concat(o(C(t,s))))}},D=function(t){return{validateDate:k("date",t),validateNumber:k("number",t),requirementType:t.length<=2?"string":["string","string"],priority:30}};y.prototype={init:function(t,e){for(var i in this.catalog=e,this.validators=n({},this.validators),t)this.addValidator(i,t[i].fn,t[i].priority);window.Parsley.trigger("parsley:validator:init")},setLocale:function(t){if(void 0===this.catalog[t])throw new Error(t+" is not available in the catalog");return this.locale=t,this},addCatalog:function(t,i,n){return"object"===e(i)&&(this.catalog[t]=i),!0===n?this.setLocale(t):this},addMessage:function(t,e,i){return void 0===this.catalog[t]&&(this.catalog[t]={}),this.catalog[t][e]=i,this},addMessages:function(t,e){for(var i in e)this.addMessage(t,i,e[i]);return this},addValidator:function(t,e,i){if(this.validators[t])f.warn('Validator "'+t+'" is already defined.');else if(g.hasOwnProperty(t))return void f.warn('"'+t+'" is a restricted keyword and is not a valid validator name.');return this._setValidator.apply(this,arguments)},hasValidator:function(t){return!!this.validators[t]},updateValidator:function(t,e,i){return this.validators[t]?this._setValidator.apply(this,arguments):(f.warn('Validator "'+t+'" is not already defined.'),this.addValidator.apply(this,arguments))},removeValidator:function(t){return this.validators[t]||f.warn('Validator "'+t+'" is not defined.'),delete this.validators[t],this},_setValidator:function(t,i,n){for(var s in"object"!==e(i)&&(i={fn:i,priority:n}),i.validate||(i=new b(i)),this.validators[t]=i,i.messages||{})this.addMessage(s,t,i.messages[s]);return this},getErrorMessage:function(t){return("type"===t.name?(this.catalog[this.locale][t.name]||{})[t.requirements]:this.formatMessage(this.catalog[this.locale][t.name],t.requirements))||this.catalog[this.locale].defaultMessage||this.catalog.en.defaultMessage},formatMessage:function(t,i){if("object"===e(i)){for(var n in i)t=this.formatMessage(t,i[n]);return t}return"string"==typeof t?t.replace(/%s/i,i):""},validators:{notblank:{validateString:function(t){return/\S/.test(t)},priority:2},required:{validateMultiple:function(t){return t.length>0},validateString:function(t){return/\S/.test(t)},priority:512},type:{validateString:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=i.step,s=void 0===n?"any":n,o=i.base,r=void 0===o?0:o,a=w[e];if(!a)throw new Error("validator type `"+e+"` is not supported");if(!t)return!0;if(!a.test(t))return!1;if("number"===e&&!/^any$/i.test(s||"")){var l=Number(t),h=Math.max(x(s),x(r));if(x(l)>h)return!1;var u=function(t){return Math.round(t*Math.pow(10,h))};if((u(l)-u(r))%u(s)!=0)return!1}return!0},requirementType:{"":"string",step:"string",base:"number"},priority:256},pattern:{validateString:function(t,e){return!t||e.test(t)},requirementType:"regexp",priority:64},minlength:{validateString:function(t,e){return!t||t.length>=e},requirementType:"integer",priority:30},maxlength:{validateString:function(t,e){return t.length<=e},requirementType:"integer",priority:30},length:{validateString:function(t,e,i){return!t||t.length>=e&&t.length<=i},requirementType:["integer","integer"],priority:30},mincheck:{validateMultiple:function(t,e){return t.length>=e},requirementType:"integer",priority:30},maxcheck:{validateMultiple:function(t,e){return t.length<=e},requirementType:"integer",priority:30},check:{validateMultiple:function(t,e,i){return t.length>=e&&t.length<=i},requirementType:["integer","integer"],priority:30},min:D((function(t,e){return t>=e})),max:D((function(t,e){return t<=e})),range:D((function(t,e,i){return t>=e&&t<=i})),equalto:{validateString:function(e,i){if(!e)return!0;var n=t(i);return n.length?e===n.val():e===i},priority:256},euvatin:{validateString:function(t,e){return!t||/^[A-Z][A-Z][A-Za-z0-9 -]{2,}$/.test(t)},priority:30}}};var T={},S=function t(e,i,n){for(var s=[],o=[],r=0;r<e.length;r++){for(var a=!1,l=0;l<i.length;l++)if(e[r].assert.name===i[l].assert.name){a=!0;break}a?o.push(e[r]):s.push(e[r])}return{kept:o,added:s,removed:n?[]:t(i,e,!0).added}};T.Form={_actualizeTriggers:function(){var t=this;this.$element.on("submit.Parsley",(function(e){t.onSubmitValidate(e)})),this.$element.on("click.Parsley",f._SubmitSelector,(function(e){t.onSubmitButton(e)})),!1!==this.options.uiEnabled&&this.element.setAttribute("novalidate","")},focus:function(){if(this._focusedField=null,!0===this.validationResult||"none"===this.options.focus)return null;for(var t=0;t<this.fields.length;t++){var e=this.fields[t];if(!0!==e.validationResult&&e.validationResult.length>0&&void 0===e.options.noFocus&&(this._focusedField=e.$element,"first"===this.options.focus))break}return null===this._focusedField?null:this._focusedField.focus()},_destroyUI:function(){this.$element.off(".Parsley")}},T.Field={_reflowUI:function(){if(this._buildUI(),this._ui){var t=S(this.validationResult,this._ui.lastValidationResult);this._ui.lastValidationResult=this.validationResult,this._manageStatusClass(),this._manageErrorsMessages(t),this._actualizeTriggers(),!t.kept.length&&!t.added.length||this._failedOnce||(this._failedOnce=!0,this._actualizeTriggers())}},getErrorsMessages:function(){if(!0===this.validationResult)return[];for(var t=[],e=0;e<this.validationResult.length;e++)t.push(this.validationResult[e].errorMessage||this._getErrorMessage(this.validationResult[e].assert));return t},addError:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.message,n=e.assert,s=e.updateClass,o=void 0===s||s;this._buildUI(),this._addError(t,{message:i,assert:n}),o&&this._errorClass()},updateError:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.message,n=e.assert,s=e.updateClass,o=void 0===s||s;this._buildUI(),this._updateError(t,{message:i,assert:n}),o&&this._errorClass()},removeError:function(t){var e=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).updateClass,i=void 0===e||e;this._buildUI(),this._removeError(t),i&&this._manageStatusClass()},_manageStatusClass:function(){this.hasConstraints()&&this.needsValidation()&&!0===this.validationResult?this._successClass():this.validationResult.length>0?this._errorClass():this._resetClass()},_manageErrorsMessages:function(e){if(void 0===this.options.errorsMessagesDisabled){if(void 0!==this.options.errorMessage)return e.added.length||e.kept.length?(this._insertErrorWrapper(),0===this._ui.$errorsWrapper.find(".parsley-custom-error-message").length&&this._ui.$errorsWrapper.append(t(this.options.errorTemplate).addClass("parsley-custom-error-message")),this._ui.$errorClassHandler.attr("aria-describedby",this._ui.errorsWrapperId),this._ui.$errorsWrapper.addClass("filled").attr("aria-hidden","false").find(".parsley-custom-error-message").html(this.options.errorMessage)):(this._ui.$errorClassHandler.removeAttr("aria-describedby"),this._ui.$errorsWrapper.removeClass("filled").attr("aria-hidden","true").find(".parsley-custom-error-message").remove());for(var i=0;i<e.removed.length;i++)this._removeError(e.removed[i].assert.name);for(i=0;i<e.added.length;i++)this._addError(e.added[i].assert.name,{message:e.added[i].errorMessage,assert:e.added[i].assert});for(i=0;i<e.kept.length;i++)this._updateError(e.kept[i].assert.name,{message:e.kept[i].errorMessage,assert:e.kept[i].assert})}},_addError:function(e,i){var n=i.message,s=i.assert;this._insertErrorWrapper(),this._ui.$errorClassHandler.attr("aria-describedby",this._ui.errorsWrapperId),this._ui.$errorsWrapper.addClass("filled").attr("aria-hidden","false").append(t(this.options.errorTemplate).addClass("parsley-"+e).html(n||this._getErrorMessage(s)))},_updateError:function(t,e){var i=e.message,n=e.assert;this._ui.$errorsWrapper.addClass("filled").find(".parsley-"+t).html(i||this._getErrorMessage(n))},_removeError:function(t){this._ui.$errorClassHandler.removeAttr("aria-describedby"),this._ui.$errorsWrapper.removeClass("filled").attr("aria-hidden","true").find(".parsley-"+t).remove()},_getErrorMessage:function(t){var e=t.name+"Message";return void 0!==this.options[e]?window.Parsley.formatMessage(this.options[e],t.requirements):window.Parsley.getErrorMessage(t)},_buildUI:function(){if(!this._ui&&!1!==this.options.uiEnabled){var e={};this.element.setAttribute(this.options.namespace+"id",this.__id__),e.$errorClassHandler=this._manageClassHandler(),e.errorsWrapperId="parsley-id-"+(this.options.multiple?"multiple-"+this.options.multiple:this.__id__),e.$errorsWrapper=t(this.options.errorsWrapper).attr("id",e.errorsWrapperId),e.lastValidationResult=[],e.validationInformationVisible=!1,this._ui=e}},_manageClassHandler:function(){if("string"==typeof this.options.classHandler&&t(this.options.classHandler).length)return t(this.options.classHandler);var i=this.options.classHandler;if("string"==typeof this.options.classHandler&&"function"==typeof window[this.options.classHandler]&&(i=window[this.options.classHandler]),"function"==typeof i){var n=i.call(this,this);if(void 0!==n&&n.length)return n}else{if("object"===e(i)&&i instanceof jQuery&&i.length)return i;i&&f.warn("The class handler `"+i+"` does not exist in DOM nor as a global JS function")}return this._inputHolder()},_inputHolder:function(){return this.options.multiple&&"SELECT"!==this.element.nodeName?this.$element.parent():this.$element},_insertErrorWrapper:function(){var i=this.options.errorsContainer;if(0!==this._ui.$errorsWrapper.parent().length)return this._ui.$errorsWrapper.parent();if("string"==typeof i){if(t(i).length)return t(i).append(this._ui.$errorsWrapper);"function"==typeof window[i]?i=window[i]:f.warn("The errors container `"+i+"` does not exist in DOM nor as a global JS function")}return"function"==typeof i&&(i=i.call(this,this)),"object"===e(i)&&i.length?i.append(this._ui.$errorsWrapper):this._inputHolder().after(this._ui.$errorsWrapper)},_actualizeTriggers:function(){var t,e=this,i=this._findRelated();i.off(".Parsley"),this._failedOnce?i.on(f.namespaceEvents(this.options.triggerAfterFailure,"Parsley"),(function(){e._validateIfNeeded()})):(t=f.namespaceEvents(this.options.trigger,"Parsley"))&&i.on(t,(function(t){e._validateIfNeeded(t)}))},_validateIfNeeded:function(t){var e=this;t&&/key|input/.test(t.type)&&(!this._ui||!this._ui.validationInformationVisible)&&this.getValue().length<=this.options.validationThreshold||(this.options.debounce?(window.clearTimeout(this._debounced),this._debounced=window.setTimeout((function(){return e.validate()}),this.options.debounce)):this.validate())},_resetUI:function(){this._failedOnce=!1,this._actualizeTriggers(),void 0!==this._ui&&(this._ui.$errorsWrapper.removeClass("filled").children().remove(),this._resetClass(),this._ui.lastValidationResult=[],this._ui.validationInformationVisible=!1)},_destroyUI:function(){this._resetUI(),void 0!==this._ui&&this._ui.$errorsWrapper.remove(),delete this._ui},_successClass:function(){this._ui.validationInformationVisible=!0,this._ui.$errorClassHandler.removeClass(this.options.errorClass).addClass(this.options.successClass)},_errorClass:function(){this._ui.validationInformationVisible=!0,this._ui.$errorClassHandler.removeClass(this.options.successClass).addClass(this.options.errorClass)},_resetClass:function(){this._ui.$errorClassHandler.removeClass(this.options.successClass).removeClass(this.options.errorClass)}};var I=function(e,i,n){this.__class__="Form",this.element=e,this.$element=t(e),this.domOptions=i,this.options=n,this.parent=window.Parsley,this.fields=[],this.validationResult=null},P={pending:null,resolved:!0,rejected:!1};I.prototype={onSubmitValidate:function(t){var e=this;if(!0!==t.parsley){var i=this._submitSource||this.$element.find(f._SubmitSelector)[0];if(this._submitSource=null,this.$element.find(".parsley-synthetic-submit-button").prop("disabled",!0),!i||null===i.getAttribute("formnovalidate")){window.Parsley._remoteCache={};var n=this.whenValidate({event:t});"resolved"===n.state()&&!1!==this._trigger("submit")||(t.stopImmediatePropagation(),t.preventDefault(),"pending"===n.state()&&n.done((function(){e._submit(i)})))}}},onSubmitButton:function(t){this._submitSource=t.currentTarget},_submit:function(e){if(!1!==this._trigger("submit")){if(e){var i=this.$element.find(".parsley-synthetic-submit-button").prop("disabled",!1);0===i.length&&(i=t('<input class="parsley-synthetic-submit-button" type="hidden">').appendTo(this.$element)),i.attr({name:e.getAttribute("name"),value:e.getAttribute("value")})}this.$element.trigger(n(t.Event("submit"),{parsley:!0}))}},validate:function(e){if(arguments.length>=1&&!t.isPlainObject(e)){f.warnOnce("Calling validate on a parsley form without passing arguments as an object is deprecated.");var i=Array.prototype.slice.call(arguments);e={group:i[0],force:i[1],event:i[2]}}return P[this.whenValidate(e).state()]},whenValidate:function(){var e,i=this,s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=s.group,a=s.force,l=s.event;this.submitEvent=l,l&&(this.submitEvent=n({},l,{preventDefault:function(){f.warnOnce("Using `this.submitEvent.preventDefault()` is deprecated; instead, call `this.validationResult = false`"),i.validationResult=!1}})),this.validationResult=!0,this._trigger("validate"),this._refreshFields();var h=this._withoutReactualizingFormOptions((function(){return t.map(i.fields,(function(t){return t.whenValidate({force:a,group:r})}))}));return(e=f.all(h).done((function(){i._trigger("success")})).fail((function(){i.validationResult=!1,i.focus(),i._trigger("error")})).always((function(){i._trigger("validated")}))).pipe.apply(e,o(this._pipeAccordingToValidationResult()))},isValid:function(e){if(arguments.length>=1&&!t.isPlainObject(e)){f.warnOnce("Calling isValid on a parsley form without passing arguments as an object is deprecated.");var i=Array.prototype.slice.call(arguments);e={group:i[0],force:i[1]}}return P[this.whenValid(e).state()]},whenValid:function(){var e=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=i.group,s=i.force;this._refreshFields();var o=this._withoutReactualizingFormOptions((function(){return t.map(e.fields,(function(t){return t.whenValid({group:n,force:s})}))}));return f.all(o)},refresh:function(){return this._refreshFields(),this},reset:function(){for(var t=0;t<this.fields.length;t++)this.fields[t].reset();this._trigger("reset")},destroy:function(){this._destroyUI();for(var t=0;t<this.fields.length;t++)this.fields[t].destroy();this.$element.removeData("Parsley"),this._trigger("destroy")},_refreshFields:function(){return this.actualizeOptions()._bindFields()},_bindFields:function(){var e=this,i=this.fields;return this.fields=[],this.fieldsMappedById={},this._withoutReactualizingFormOptions((function(){e.$element.find(e.options.inputs).not(e.options.excluded).not("[".concat(e.options.namespace,"excluded=true]")).each((function(t,i){var n=new window.Parsley.Factory(i,{},e);if("Field"===n.__class__||"FieldMultiple"===n.__class__){var s=n.__class__+"-"+n.__id__;void 0===e.fieldsMappedById[s]&&(e.fieldsMappedById[s]=n,e.fields.push(n))}})),t.each(f.difference(i,e.fields),(function(t,e){e.reset()}))})),this},_withoutReactualizingFormOptions:function(t){var e=this.actualizeOptions;this.actualizeOptions=function(){return this};var i=t();return this.actualizeOptions=e,i},_trigger:function(t){return this.trigger("form:"+t)}};var A=function(t,e,i,s,o){var r=window.Parsley._validatorRegistry.validators[e],a=new b(r);n(this,{validator:a,name:e,requirements:i,priority:s=s||t.options[e+"Priority"]||a.priority,isDomConstraint:o=!0===o}),this._parseRequirements(t.options)},M=function(t){return t[0].toUpperCase()+t.slice(1)};A.prototype={validate:function(t,e){var i;return(i=this.validator).validate.apply(i,[t].concat(o(this.requirementList),[e]))},_parseRequirements:function(t){var e=this;this.requirementList=this.validator.parseRequirements(this.requirements,(function(i){return t[e.name+M(i)]}))}};var E=function(e,i,n,s){this.__class__="Field",this.element=e,this.$element=t(e),void 0!==s&&(this.parent=s),this.options=n,this.domOptions=i,this.constraints=[],this.constraintsByName={},this.validationResult=!0,this._bindConstraints()},O={pending:null,resolved:!0,rejected:!1};E.prototype={validate:function(e){arguments.length>=1&&!t.isPlainObject(e)&&(f.warnOnce("Calling validate on a parsley field without passing arguments as an object is deprecated."),e={options:e});var i=this.whenValidate(e);if(!i)return!0;switch(i.state()){case"pending":return null;case"resolved":return!0;case"rejected":return this.validationResult}},whenValidate:function(){var t,e=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=i.force,s=i.group;if(this.refresh(),!s||this._isInGroup(s))return this.value=this.getValue(),this._trigger("validate"),(t=this.whenValid({force:n,value:this.value,_refreshed:!0}).always((function(){e._reflowUI()})).done((function(){e._trigger("success")})).fail((function(){e._trigger("error")})).always((function(){e._trigger("validated")}))).pipe.apply(t,o(this._pipeAccordingToValidationResult()))},hasConstraints:function(){return 0!==this.constraints.length},needsValidation:function(t){return void 0===t&&(t=this.getValue()),!(!t.length&&!this._isRequired()&&void 0===this.options.validateIfEmpty)},_isInGroup:function(e){return Array.isArray(this.options.group)?-1!==t.inArray(e,this.options.group):this.options.group===e},isValid:function(e){if(arguments.length>=1&&!t.isPlainObject(e)){f.warnOnce("Calling isValid on a parsley field without passing arguments as an object is deprecated.");var i=Array.prototype.slice.call(arguments);e={force:i[0],value:i[1]}}var n=this.whenValid(e);return!n||O[n.state()]},whenValid:function(){var e=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=i.force,s=void 0!==n&&n,o=i.value,r=i.group;if(i._refreshed||this.refresh(),!r||this._isInGroup(r)){if(this.validationResult=!0,!this.hasConstraints())return t.when();if(null==o&&(o=this.getValue()),!this.needsValidation(o)&&!0!==s)return t.when();var a=this._getGroupedConstraints(),l=[];return t.each(a,(function(i,n){var s=f.all(t.map(n,(function(t){return e._validateConstraint(o,t)})));if(l.push(s),"rejected"===s.state())return!1})),f.all(l)}},_validateConstraint:function(e,i){var n=this,s=i.validate(e,this);return!1===s&&(s=t.Deferred().reject()),f.all([s]).fail((function(t){n.validationResult instanceof Array||(n.validationResult=[]),n.validationResult.push({assert:i,errorMessage:"string"==typeof t&&t})}))},getValue:function(){var t;return null==(t="function"==typeof this.options.value?this.options.value(this):void 0!==this.options.value?this.options.value:this.$element.val())?"":this._handleWhitespace(t)},reset:function(){return this._resetUI(),this._trigger("reset")},destroy:function(){this._destroyUI(),this.$element.removeData("Parsley"),this.$element.removeData("FieldMultiple"),this._trigger("destroy")},refresh:function(){return this._refreshConstraints(),this},_refreshConstraints:function(){return this.actualizeOptions()._bindConstraints()},refreshConstraints:function(){return f.warnOnce("Parsley's refreshConstraints is deprecated. Please use refresh"),this.refresh()},addConstraint:function(t,e,i,n){if(window.Parsley._validatorRegistry.validators[t]){var s=new A(this,t,e,i,n);"undefined"!==this.constraintsByName[s.name]&&this.removeConstraint(s.name),this.constraints.push(s),this.constraintsByName[s.name]=s}return this},removeConstraint:function(t){for(var e=0;e<this.constraints.length;e++)if(t===this.constraints[e].name){this.constraints.splice(e,1);break}return delete this.constraintsByName[t],this},updateConstraint:function(t,e,i){return this.removeConstraint(t).addConstraint(t,e,i)},_bindConstraints:function(){for(var t=[],e={},i=0;i<this.constraints.length;i++)!1===this.constraints[i].isDomConstraint&&(t.push(this.constraints[i]),e[this.constraints[i].name]=this.constraints[i]);for(var n in this.constraints=t,this.constraintsByName=e,this.options)this.addConstraint(n,this.options[n],void 0,!0);return this._bindHtml5Constraints()},_bindHtml5Constraints:function(){null!==this.element.getAttribute("required")&&this.addConstraint("required",!0,void 0,!0),null!==this.element.getAttribute("pattern")&&this.addConstraint("pattern",this.element.getAttribute("pattern"),void 0,!0);var t=this.element.getAttribute("min"),e=this.element.getAttribute("max");null!==t&&null!==e?this.addConstraint("range",[t,e],void 0,!0):null!==t?this.addConstraint("min",t,void 0,!0):null!==e&&this.addConstraint("max",e,void 0,!0),null!==this.element.getAttribute("minlength")&&null!==this.element.getAttribute("maxlength")?this.addConstraint("length",[this.element.getAttribute("minlength"),this.element.getAttribute("maxlength")],void 0,!0):null!==this.element.getAttribute("minlength")?this.addConstraint("minlength",this.element.getAttribute("minlength"),void 0,!0):null!==this.element.getAttribute("maxlength")&&this.addConstraint("maxlength",this.element.getAttribute("maxlength"),void 0,!0);var i=f.getType(this.element);return"number"===i?this.addConstraint("type",["number",{step:this.element.getAttribute("step")||"1",base:t||this.element.getAttribute("value")}],void 0,!0):/^(email|url|range|date)$/i.test(i)?this.addConstraint("type",i,void 0,!0):this},_isRequired:function(){return void 0!==this.constraintsByName.required&&!1!==this.constraintsByName.required.requirements},_trigger:function(t){return this.trigger("field:"+t)},_handleWhitespace:function(t){return!0===this.options.trimValue&&f.warnOnce('data-parsley-trim-value="true" is deprecated, please use data-parsley-whitespace="trim"'),"squish"===this.options.whitespace&&(t=t.replace(/\s{2,}/g," ")),"trim"!==this.options.whitespace&&"squish"!==this.options.whitespace&&!0!==this.options.trimValue||(t=f.trimString(t)),t},_isDateInput:function(){var t=this.constraintsByName.type;return t&&"date"===t.requirements},_getGroupedConstraints:function(){if(!1===this.options.priorityEnabled)return[this.constraints];for(var t=[],e={},i=0;i<this.constraints.length;i++){var n=this.constraints[i].priority;e[n]||t.push(e[n]=[]),e[n].push(this.constraints[i])}return t.sort((function(t,e){return e[0].priority-t[0].priority})),t}};var H=function(){this.__class__="FieldMultiple"};H.prototype={addElement:function(t){return this.$elements.push(t),this},_refreshConstraints:function(){var e;if(this.constraints=[],"SELECT"===this.element.nodeName)return this.actualizeOptions()._bindConstraints(),this;for(var i=0;i<this.$elements.length;i++)if(t("html").has(this.$elements[i]).length){e=this.$elements[i].data("FieldMultiple")._refreshConstraints().constraints;for(var n=0;n<e.length;n++)this.addConstraint(e[n].name,e[n].requirements,e[n].priority,e[n].isDomConstraint)}else this.$elements.splice(i,1);return this},getValue:function(){if("function"==typeof this.options.value)return this.options.value(this);if(void 0!==this.options.value)return this.options.value;if("INPUT"===this.element.nodeName){var e=f.getType(this.element);if("radio"===e)return this._findRelated().filter(":checked").val()||"";if("checkbox"===e){var i=[];return this._findRelated().filter(":checked").each((function(){i.push(t(this).val())})),i}}return"SELECT"===this.element.nodeName&&null===this.$element.val()?[]:this.$element.val()},_init:function(){return this.$elements=[this.$element],this}};var F=function(i,s,o){this.element=i,this.$element=t(i);var r=this.$element.data("Parsley");if(r)return void 0!==o&&r.parent===window.Parsley&&(r.parent=o,r._resetOptions(r.options)),"object"===e(s)&&n(r.options,s),r;if(!this.$element.length)throw new Error("You must bind Parsley on an existing element.");if(void 0!==o&&"Form"!==o.__class__)throw new Error("Parent instance must be a Form instance");return this.parent=o||window.Parsley,this.init(s)};F.prototype={init:function(t){return this.__class__="Parsley",this.__version__="2.9.2",this.__id__=f.generateID(),this._resetOptions(t),"FORM"===this.element.nodeName||f.checkAttr(this.element,this.options.namespace,"validate")&&!this.$element.is(this.options.inputs)?this.bind("parsleyForm"):this.isMultiple()?this.handleMultiple():this.bind("parsleyField")},isMultiple:function(){var t=f.getType(this.element);return"radio"===t||"checkbox"===t||"SELECT"===this.element.nodeName&&null!==this.element.getAttribute("multiple")},handleMultiple:function(){var e,i,n=this;if(this.options.multiple=this.options.multiple||(e=this.element.getAttribute("name"))||this.element.getAttribute("id"),"SELECT"===this.element.nodeName&&null!==this.element.getAttribute("multiple"))return this.options.multiple=this.options.multiple||this.__id__,this.bind("parsleyFieldMultiple");if(!this.options.multiple)return f.warn("To be bound by Parsley, a radio, a checkbox and a multiple select input must have either a name or a multiple option.",this.$element),this;this.options.multiple=this.options.multiple.replace(/(:|\.|\[|\]|\{|\}|\$)/g,""),e&&t('input[name="'+e+'"]').each((function(t,e){var i=f.getType(e);"radio"!==i&&"checkbox"!==i||e.setAttribute(n.options.namespace+"multiple",n.options.multiple)}));for(var s=this._findRelated(),o=0;o<s.length;o++)if(void 0!==(i=t(s.get(o)).data("Parsley"))){this.$element.data("FieldMultiple")||i.addElement(this.$element);break}return this.bind("parsleyField",!0),i||this.bind("parsleyFieldMultiple")},bind:function(e,i){var n;switch(e){case"parsleyForm":n=t.extend(new I(this.element,this.domOptions,this.options),new m,window.ParsleyExtend)._bindFields();break;case"parsleyField":n=t.extend(new E(this.element,this.domOptions,this.options,this.parent),new m,window.ParsleyExtend);break;case"parsleyFieldMultiple":n=t.extend(new E(this.element,this.domOptions,this.options,this.parent),new H,new m,window.ParsleyExtend)._init();break;default:throw new Error(e+"is not a supported Parsley type")}return this.options.multiple&&f.setAttr(this.element,this.options.namespace,"multiple",this.options.multiple),void 0!==i?(this.$element.data("FieldMultiple",n),n):(this.$element.data("Parsley",n),n._actualizeTriggers(),n._trigger("init"),n)}};var N=t.fn.jquery.split(".");if(parseInt(N[0])<=1&&parseInt(N[1])<8)throw"The loaded version of jQuery is too old. Please upgrade to 1.8.x or better.";N.forEach||f.warn("Parsley requires ES5 to run properly. Please include https://github.com/es-shims/es5-shim");var z=n(new m,{element:document,$element:t(document),actualizeOptions:null,_resetOptions:null,Factory:F,version:"2.9.2"});n(E.prototype,T.Field,m.prototype),n(I.prototype,T.Form,m.prototype),n(F.prototype,m.prototype),t.fn.parsley=t.fn.psly=function(e){if(this.length>1){var i=[];return this.each((function(){i.push(t(this).parsley(e))})),i}if(0!=this.length)return new F(this[0],e)},void 0===window.ParsleyExtend&&(window.ParsleyExtend={}),z.options=n(f.objectCreate(g),window.ParsleyConfig),window.ParsleyConfig=z.options,window.Parsley=window.psly=z,z.Utils=f,window.ParsleyUtils={},t.each(f,(function(t,e){"function"==typeof e&&(window.ParsleyUtils[t]=function(){return f.warnOnce("Accessing `window.ParsleyUtils` is deprecated. Use `window.Parsley.Utils` instead."),f[t].apply(f,arguments)})}));var W=window.Parsley._validatorRegistry=new y(window.ParsleyConfig.validators,window.ParsleyConfig.i18n);window.ParsleyValidator={},t.each("setLocale addCatalog addMessage addMessages getErrorMessage formatMessage addValidator updateValidator removeValidator hasValidator".split(" "),(function(t,e){window.Parsley[e]=function(){return W[e].apply(W,arguments)},window.ParsleyValidator[e]=function(){var t;return f.warnOnce("Accessing the method '".concat(e,"' through Validator is deprecated. Simply call 'window.Parsley.").concat(e,"(...)'")),(t=window.Parsley)[e].apply(t,arguments)}})),window.Parsley.UI=T,window.ParsleyUI={removeError:function(t,e,i){var n=!0!==i;return f.warnOnce("Accessing UI is deprecated. Call 'removeError' on the instance directly. Please comment in issue 1073 as to your need to call this method."),t.removeError(e,{updateClass:n})},getErrorsMessages:function(t){return f.warnOnce("Accessing UI is deprecated. Call 'getErrorsMessages' on the instance directly."),t.getErrorsMessages()}},t.each("addError updateError".split(" "),(function(t,e){window.ParsleyUI[e]=function(t,i,n,s,o){var r=!0!==o;return f.warnOnce("Accessing UI is deprecated. Call '".concat(e,"' on the instance directly. Please comment in issue 1073 as to your need to call this method.")),t[e](i,{message:n,assert:s,updateClass:r})}})),!1!==window.ParsleyConfig.autoBind&&t((function(){t("[data-parsley-validate]").length&&t("[data-parsley-validate]").parsley()}));var R=t({}),L=function(){f.warnOnce("Parsley's pubsub module is deprecated; use the 'on' and 'off' methods on parsley instances or window.Parsley")};function j(t,e){return t.parsleyAdaptedCallback||(t.parsleyAdaptedCallback=function(){var i=Array.prototype.slice.call(arguments,0);i.unshift(this),t.apply(e||R,i)}),t.parsleyAdaptedCallback}var q="parsley:";function B(t){return 0===t.lastIndexOf(q,0)?t.substr(q.length):t}function $(){var e=this,s=window||i.g;n(this,{isNativeEvent:function(t){return t.originalEvent&&!1!==t.originalEvent.isTrusted},fakeInputEvent:function(i){e.isNativeEvent(i)&&t(i.target).trigger("input")},misbehaves:function(i){e.isNativeEvent(i)&&(e.behavesOk(i),t(document).on("change.inputevent",i.data.selector,e.fakeInputEvent),e.fakeInputEvent(i))},behavesOk:function(i){e.isNativeEvent(i)&&t(document).off("input.inputevent",i.data.selector,e.behavesOk).off("change.inputevent",i.data.selector,e.misbehaves)},install:function(){if(!s.inputEventPatched){s.inputEventPatched="0.0.3";for(var i=0,n=["select",'input[type="checkbox"]','input[type="radio"]','input[type="file"]'];i<n.length;i++){var o=n[i];t(document).on("input.inputevent",o,{selector:o},e.behavesOk).on("change.inputevent",o,{selector:o},e.misbehaves)}}},uninstall:function(){delete s.inputEventPatched,t(document).off(".inputevent")}})}return t.listen=function(t,i){var n;if(L(),"object"===e(arguments[1])&&"function"==typeof arguments[2]&&(n=arguments[1],i=arguments[2]),"function"!=typeof i)throw new Error("Wrong parameters");window.Parsley.on(B(t),j(i,n))},t.listenTo=function(t,e,i){if(L(),!(t instanceof E||t instanceof I))throw new Error("Must give Parsley instance");if("string"!=typeof e||"function"!=typeof i)throw new Error("Wrong parameters");t.on(B(e),j(i))},t.unsubscribe=function(t,e){if(L(),"string"!=typeof t||"function"!=typeof e)throw new Error("Wrong arguments");window.Parsley.off(B(t),e.parsleyAdaptedCallback)},t.unsubscribeTo=function(t,e){if(L(),!(t instanceof E||t instanceof I))throw new Error("Must give Parsley instance");t.off(B(e))},t.unsubscribeAll=function(e){L(),window.Parsley.off(B(e)),t("form,input,textarea,select").each((function(){var i=t(this).data("Parsley");i&&i.off(B(e))}))},t.emit=function(t,e){var i;L();var n=e instanceof E||e instanceof I,s=Array.prototype.slice.call(arguments,n?2:1);s.unshift(B(t)),n||(e=window.Parsley),(i=e).trigger.apply(i,o(s))},t.extend(!0,z,{asyncValidators:{default:{fn:function(t){return t.status>=200&&t.status<300},url:!1},reverse:{fn:function(t){return t.status<200||t.status>=300},url:!1}},addAsyncValidator:function(t,e,i,n){return z.asyncValidators[t]={fn:e,url:i||!1,options:n||{}},this}}),z.addValidator("remote",{requirementType:{"":"string",validator:"string",reverse:"boolean",options:"object"},validateString:function(e,i,n,s){var o,r,a={},l=n.validator||(!0===n.reverse?"reverse":"default");if(void 0===z.asyncValidators[l])throw new Error("Calling an undefined async validator: `"+l+"`");(i=z.asyncValidators[l].url||i).indexOf("{value}")>-1?i=i.replace("{value}",encodeURIComponent(e)):a[s.element.getAttribute("name")||s.element.getAttribute("id")]=e;var h=t.extend(!0,n.options||{},z.asyncValidators[l].options);o=t.extend(!0,{},{url:i,data:a,type:"GET"},h),s.trigger("field:ajaxoptions",s,o),r=t.param(o),void 0===z._remoteCache&&(z._remoteCache={});var u=z._remoteCache[r]=z._remoteCache[r]||t.ajax(o),c=function(){var e=z.asyncValidators[l].fn.call(s,u,i,n);return e||(e=t.Deferred().reject()),t.when(e)};return u.then(c,c)},priority:-1}),z.on("form:submit",(function(){z._remoteCache={}})),m.prototype.addAsyncValidator=function(){return f.warnOnce("Accessing the method `addAsyncValidator` through an instance is deprecated. Simply call `Parsley.addAsyncValidator(...)`"),z.addAsyncValidator.apply(z,arguments)},z.addMessages("en",{defaultMessage:"This value seems to be invalid.",type:{email:"This value should be a valid email.",url:"This value should be a valid url.",number:"This value should be a valid number.",integer:"This value should be a valid integer.",digits:"This value should be digits.",alphanum:"This value should be alphanumeric."},notblank:"This value should not be blank.",required:"This value is required.",pattern:"This value seems to be invalid.",min:"This value should be greater than or equal to %s.",max:"This value should be lower than or equal to %s.",range:"This value should be between %s and %s.",minlength:"This value is too short. It should have %s characters or more.",maxlength:"This value is too long. It should have %s characters or fewer.",length:"This value length is invalid. It should be between %s and %s characters long.",mincheck:"You must select at least %s choices.",maxcheck:"You must select %s choices or fewer.",check:"You must select between %s and %s choices.",equalto:"This value should be the same.",euvatin:"It's not a valid VAT Identification Number."}),z.setLocale("en"),(new $).install(),z}(i(692))},428:(t,e,i)=>{"use strict";var n=i(692),s=i.n(n);window.$=window.jQuery=s()},590:()=>{},692:function(t,e){var i;!function(e,i){"use strict";"object"==typeof t.exports?t.exports=e.document?i(e,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return i(t)}:i(e)}("undefined"!=typeof window?window:this,(function(n,s){"use strict";var o=[],r=Object.getPrototypeOf,a=o.slice,l=o.flat?function(t){return o.flat.call(t)}:function(t){return o.concat.apply([],t)},h=o.push,u=o.indexOf,c={},d=c.toString,p=c.hasOwnProperty,f=p.toString,g=f.call(Object),m={},v=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},_=function(t){return null!=t&&t===t.window},b=n.document,y={type:!0,src:!0,nonce:!0,noModule:!0};function w(t,e,i){var n,s,o=(i=i||b).createElement("script");if(o.text=t,e)for(n in y)(s=e[n]||e.getAttribute&&e.getAttribute(n))&&o.setAttribute(n,s);i.head.appendChild(o).parentNode.removeChild(o)}function x(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?c[d.call(t)]||"object":typeof t}var C="3.7.1",k=/HTML$/i,D=function(t,e){return new D.fn.init(t,e)};function T(t){var e=!!t&&"length"in t&&t.length,i=x(t);return!v(t)&&!_(t)&&("array"===i||0===e||"number"==typeof e&&e>0&&e-1 in t)}function S(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}D.fn=D.prototype={jquery:C,constructor:D,length:0,toArray:function(){return a.call(this)},get:function(t){return null==t?a.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=D.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return D.each(this,t)},map:function(t){return this.pushStack(D.map(this,(function(e,i){return t.call(e,i,e)})))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(D.grep(this,(function(t,e){return(e+1)%2})))},odd:function(){return this.pushStack(D.grep(this,(function(t,e){return e%2})))},eq:function(t){var e=this.length,i=+t+(t<0?e:0);return this.pushStack(i>=0&&i<e?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:h,sort:o.sort,splice:o.splice},D.extend=D.fn.extend=function(){var t,e,i,n,s,o,r=arguments[0]||{},a=1,l=arguments.length,h=!1;for("boolean"==typeof r&&(h=r,r=arguments[a]||{},a++),"object"==typeof r||v(r)||(r={}),a===l&&(r=this,a--);a<l;a++)if(null!=(t=arguments[a]))for(e in t)n=t[e],"__proto__"!==e&&r!==n&&(h&&n&&(D.isPlainObject(n)||(s=Array.isArray(n)))?(i=r[e],o=s&&!Array.isArray(i)?[]:s||D.isPlainObject(i)?i:{},s=!1,r[e]=D.extend(h,o,n)):void 0!==n&&(r[e]=n));return r},D.extend({expando:"jQuery"+(C+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,i;return!(!t||"[object Object]"!==d.call(t))&&(!(e=r(t))||"function"==typeof(i=p.call(e,"constructor")&&e.constructor)&&f.call(i)===g)},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,i){w(t,{nonce:e&&e.nonce},i)},each:function(t,e){var i,n=0;if(T(t))for(i=t.length;n<i&&!1!==e.call(t[n],n,t[n]);n++);else for(n in t)if(!1===e.call(t[n],n,t[n]))break;return t},text:function(t){var e,i="",n=0,s=t.nodeType;if(!s)for(;e=t[n++];)i+=D.text(e);return 1===s||11===s?t.textContent:9===s?t.documentElement.textContent:3===s||4===s?t.nodeValue:i},makeArray:function(t,e){var i=e||[];return null!=t&&(T(Object(t))?D.merge(i,"string"==typeof t?[t]:t):h.call(i,t)),i},inArray:function(t,e,i){return null==e?-1:u.call(e,t,i)},isXMLDoc:function(t){var e=t&&t.namespaceURI,i=t&&(t.ownerDocument||t).documentElement;return!k.test(e||i&&i.nodeName||"HTML")},merge:function(t,e){for(var i=+e.length,n=0,s=t.length;n<i;n++)t[s++]=e[n];return t.length=s,t},grep:function(t,e,i){for(var n=[],s=0,o=t.length,r=!i;s<o;s++)!e(t[s],s)!==r&&n.push(t[s]);return n},map:function(t,e,i){var n,s,o=0,r=[];if(T(t))for(n=t.length;o<n;o++)null!=(s=e(t[o],o,i))&&r.push(s);else for(o in t)null!=(s=e(t[o],o,i))&&r.push(s);return l(r)},guid:1,support:m}),"function"==typeof Symbol&&(D.fn[Symbol.iterator]=o[Symbol.iterator]),D.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(t,e){c["[object "+e+"]"]=e.toLowerCase()}));var I=o.pop,P=o.sort,A=o.splice,M="[\\x20\\t\\r\\n\\f]",E=new RegExp("^"+M+"+|((?:^|[^\\\\])(?:\\\\.)*)"+M+"+$","g");D.contains=function(t,e){var i=e&&e.parentNode;return t===i||!(!i||1!==i.nodeType||!(t.contains?t.contains(i):t.compareDocumentPosition&&16&t.compareDocumentPosition(i)))};var O=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function H(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}D.escapeSelector=function(t){return(t+"").replace(O,H)};var F=b,N=h;!function(){var t,e,i,s,r,l,h,c,d,f,g=N,v=D.expando,_=0,b=0,y=tt(),w=tt(),x=tt(),C=tt(),k=function(t,e){return t===e&&(r=!0),0},T="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",O="(?:\\\\[\\da-fA-F]{1,6}"+M+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",H="\\["+M+"*("+O+")(?:"+M+"*([*^$|!~]?=)"+M+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+O+"))|)"+M+"*\\]",z=":("+O+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+H+")*)|.*)\\)|)",W=new RegExp(M+"+","g"),R=new RegExp("^"+M+"*,"+M+"*"),L=new RegExp("^"+M+"*([>+~]|"+M+")"+M+"*"),j=new RegExp(M+"|>"),q=new RegExp(z),B=new RegExp("^"+O+"$"),$={ID:new RegExp("^#("+O+")"),CLASS:new RegExp("^\\.("+O+")"),TAG:new RegExp("^("+O+"|[*])"),ATTR:new RegExp("^"+H),PSEUDO:new RegExp("^"+z),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+M+"*(even|odd|(([+-]|)(\\d*)n|)"+M+"*(?:([+-]|)"+M+"*(\\d+)|))"+M+"*\\)|)","i"),bool:new RegExp("^(?:"+T+")$","i"),needsContext:new RegExp("^"+M+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+M+"*((?:-\\d)?\\d*)"+M+"*\\)|)(?=[^-]|$)","i")},V=/^(?:input|select|textarea|button)$/i,Y=/^h\d$/i,U=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,K=/[+~]/,X=new RegExp("\\\\[\\da-fA-F]{1,6}"+M+"?|\\\\([^\\r\\n\\f])","g"),G=function(t,e){var i="0x"+t.slice(1)-65536;return e||(i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320))},J=function(){lt()},Z=dt((function(t){return!0===t.disabled&&S(t,"fieldset")}),{dir:"parentNode",next:"legend"});try{g.apply(o=a.call(F.childNodes),F.childNodes),o[F.childNodes.length].nodeType}catch(t){g={apply:function(t,e){N.apply(t,a.call(e))},call:function(t){N.apply(t,a.call(arguments,1))}}}function Q(t,e,i,n){var s,o,r,a,h,u,p,f=e&&e.ownerDocument,_=e?e.nodeType:9;if(i=i||[],"string"!=typeof t||!t||1!==_&&9!==_&&11!==_)return i;if(!n&&(lt(e),e=e||l,c)){if(11!==_&&(h=U.exec(t)))if(s=h[1]){if(9===_){if(!(r=e.getElementById(s)))return i;if(r.id===s)return g.call(i,r),i}else if(f&&(r=f.getElementById(s))&&Q.contains(e,r)&&r.id===s)return g.call(i,r),i}else{if(h[2])return g.apply(i,e.getElementsByTagName(t)),i;if((s=h[3])&&e.getElementsByClassName)return g.apply(i,e.getElementsByClassName(s)),i}if(!(C[t+" "]||d&&d.test(t))){if(p=t,f=e,1===_&&(j.test(t)||L.test(t))){for((f=K.test(t)&&at(e.parentNode)||e)==e&&m.scope||((a=e.getAttribute("id"))?a=D.escapeSelector(a):e.setAttribute("id",a=v)),o=(u=ut(t)).length;o--;)u[o]=(a?"#"+a:":scope")+" "+ct(u[o]);p=u.join(",")}try{return g.apply(i,f.querySelectorAll(p)),i}catch(e){C(t,!0)}finally{a===v&&e.removeAttribute("id")}}}return _t(t.replace(E,"$1"),e,i,n)}function tt(){var t=[];return function i(n,s){return t.push(n+" ")>e.cacheLength&&delete i[t.shift()],i[n+" "]=s}}function et(t){return t[v]=!0,t}function it(t){var e=l.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function nt(t){return function(e){return S(e,"input")&&e.type===t}}function st(t){return function(e){return(S(e,"input")||S(e,"button"))&&e.type===t}}function ot(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&Z(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function rt(t){return et((function(e){return e=+e,et((function(i,n){for(var s,o=t([],i.length,e),r=o.length;r--;)i[s=o[r]]&&(i[s]=!(n[s]=i[s]))}))}))}function at(t){return t&&void 0!==t.getElementsByTagName&&t}function lt(t){var i,n=t?t.ownerDocument||t:F;return n!=l&&9===n.nodeType&&n.documentElement?(h=(l=n).documentElement,c=!D.isXMLDoc(l),f=h.matches||h.webkitMatchesSelector||h.msMatchesSelector,h.msMatchesSelector&&F!=l&&(i=l.defaultView)&&i.top!==i&&i.addEventListener("unload",J),m.getById=it((function(t){return h.appendChild(t).id=D.expando,!l.getElementsByName||!l.getElementsByName(D.expando).length})),m.disconnectedMatch=it((function(t){return f.call(t,"*")})),m.scope=it((function(){return l.querySelectorAll(":scope")})),m.cssHas=it((function(){try{return l.querySelector(":has(*,:jqfake)"),!1}catch(t){return!0}})),m.getById?(e.filter.ID=function(t){var e=t.replace(X,G);return function(t){return t.getAttribute("id")===e}},e.find.ID=function(t,e){if(void 0!==e.getElementById&&c){var i=e.getElementById(t);return i?[i]:[]}}):(e.filter.ID=function(t){var e=t.replace(X,G);return function(t){var i=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return i&&i.value===e}},e.find.ID=function(t,e){if(void 0!==e.getElementById&&c){var i,n,s,o=e.getElementById(t);if(o){if((i=o.getAttributeNode("id"))&&i.value===t)return[o];for(s=e.getElementsByName(t),n=0;o=s[n++];)if((i=o.getAttributeNode("id"))&&i.value===t)return[o]}return[]}}),e.find.TAG=function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):e.querySelectorAll(t)},e.find.CLASS=function(t,e){if(void 0!==e.getElementsByClassName&&c)return e.getElementsByClassName(t)},d=[],it((function(t){var e;h.appendChild(t).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",t.querySelectorAll("[selected]").length||d.push("\\["+M+"*(?:value|"+T+")"),t.querySelectorAll("[id~="+v+"-]").length||d.push("~="),t.querySelectorAll("a#"+v+"+*").length||d.push(".#.+[+~]"),t.querySelectorAll(":checked").length||d.push(":checked"),(e=l.createElement("input")).setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),h.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&d.push(":enabled",":disabled"),(e=l.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||d.push("\\["+M+"*name"+M+"*="+M+"*(?:''|\"\")")})),m.cssHas||d.push(":has"),d=d.length&&new RegExp(d.join("|")),k=function(t,e){if(t===e)return r=!0,0;var i=!t.compareDocumentPosition-!e.compareDocumentPosition;return i||(1&(i=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!m.sortDetached&&e.compareDocumentPosition(t)===i?t===l||t.ownerDocument==F&&Q.contains(F,t)?-1:e===l||e.ownerDocument==F&&Q.contains(F,e)?1:s?u.call(s,t)-u.call(s,e):0:4&i?-1:1)},l):l}for(t in Q.matches=function(t,e){return Q(t,null,null,e)},Q.matchesSelector=function(t,e){if(lt(t),c&&!C[e+" "]&&(!d||!d.test(e)))try{var i=f.call(t,e);if(i||m.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(t){C(e,!0)}return Q(e,l,null,[t]).length>0},Q.contains=function(t,e){return(t.ownerDocument||t)!=l&&lt(t),D.contains(t,e)},Q.attr=function(t,i){(t.ownerDocument||t)!=l&&lt(t);var n=e.attrHandle[i.toLowerCase()],s=n&&p.call(e.attrHandle,i.toLowerCase())?n(t,i,!c):void 0;return void 0!==s?s:t.getAttribute(i)},Q.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},D.uniqueSort=function(t){var e,i=[],n=0,o=0;if(r=!m.sortStable,s=!m.sortStable&&a.call(t,0),P.call(t,k),r){for(;e=t[o++];)e===t[o]&&(n=i.push(o));for(;n--;)A.call(t,i[n],1)}return s=null,t},D.fn.uniqueSort=function(){return this.pushStack(D.uniqueSort(a.apply(this)))},e=D.expr={cacheLength:50,createPseudo:et,match:$,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(X,G),t[3]=(t[3]||t[4]||t[5]||"").replace(X,G),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||Q.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&Q.error(t[0]),t},PSEUDO:function(t){var e,i=!t[6]&&t[2];return $.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":i&&q.test(i)&&(e=ut(i,!0))&&(e=i.indexOf(")",i.length-e)-i.length)&&(t[0]=t[0].slice(0,e),t[2]=i.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(X,G).toLowerCase();return"*"===t?function(){return!0}:function(t){return S(t,e)}},CLASS:function(t){var e=y[t+" "];return e||(e=new RegExp("(^|"+M+")"+t+"("+M+"|$)"))&&y(t,(function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")}))},ATTR:function(t,e,i){return function(n){var s=Q.attr(n,t);return null==s?"!="===e:!e||(s+="","="===e?s===i:"!="===e?s!==i:"^="===e?i&&0===s.indexOf(i):"*="===e?i&&s.indexOf(i)>-1:"$="===e?i&&s.slice(-i.length)===i:"~="===e?(" "+s.replace(W," ")+" ").indexOf(i)>-1:"|="===e&&(s===i||s.slice(0,i.length+1)===i+"-"))}},CHILD:function(t,e,i,n,s){var o="nth"!==t.slice(0,3),r="last"!==t.slice(-4),a="of-type"===e;return 1===n&&0===s?function(t){return!!t.parentNode}:function(e,i,l){var h,u,c,d,p,f=o!==r?"nextSibling":"previousSibling",g=e.parentNode,m=a&&e.nodeName.toLowerCase(),b=!l&&!a,y=!1;if(g){if(o){for(;f;){for(c=e;c=c[f];)if(a?S(c,m):1===c.nodeType)return!1;p=f="only"===t&&!p&&"nextSibling"}return!0}if(p=[r?g.firstChild:g.lastChild],r&&b){for(y=(d=(h=(u=g[v]||(g[v]={}))[t]||[])[0]===_&&h[1])&&h[2],c=d&&g.childNodes[d];c=++d&&c&&c[f]||(y=d=0)||p.pop();)if(1===c.nodeType&&++y&&c===e){u[t]=[_,d,y];break}}else if(b&&(y=d=(h=(u=e[v]||(e[v]={}))[t]||[])[0]===_&&h[1]),!1===y)for(;(c=++d&&c&&c[f]||(y=d=0)||p.pop())&&(!(a?S(c,m):1===c.nodeType)||!++y||(b&&((u=c[v]||(c[v]={}))[t]=[_,y]),c!==e)););return(y-=s)===n||y%n==0&&y/n>=0}}},PSEUDO:function(t,i){var n,s=e.pseudos[t]||e.setFilters[t.toLowerCase()]||Q.error("unsupported pseudo: "+t);return s[v]?s(i):s.length>1?(n=[t,t,"",i],e.setFilters.hasOwnProperty(t.toLowerCase())?et((function(t,e){for(var n,o=s(t,i),r=o.length;r--;)t[n=u.call(t,o[r])]=!(e[n]=o[r])})):function(t){return s(t,0,n)}):s}},pseudos:{not:et((function(t){var e=[],i=[],n=vt(t.replace(E,"$1"));return n[v]?et((function(t,e,i,s){for(var o,r=n(t,null,s,[]),a=t.length;a--;)(o=r[a])&&(t[a]=!(e[a]=o))})):function(t,s,o){return e[0]=t,n(e,null,o,i),e[0]=null,!i.pop()}})),has:et((function(t){return function(e){return Q(t,e).length>0}})),contains:et((function(t){return t=t.replace(X,G),function(e){return(e.textContent||D.text(e)).indexOf(t)>-1}})),lang:et((function(t){return B.test(t||"")||Q.error("unsupported lang: "+t),t=t.replace(X,G).toLowerCase(),function(e){var i;do{if(i=c?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(i=i.toLowerCase())===t||0===i.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}})),target:function(t){var e=n.location&&n.location.hash;return e&&e.slice(1)===t.id},root:function(t){return t===h},focus:function(t){return t===function(){try{return l.activeElement}catch(t){}}()&&l.hasFocus()&&!!(t.type||t.href||~t.tabIndex)},enabled:ot(!1),disabled:ot(!0),checked:function(t){return S(t,"input")&&!!t.checked||S(t,"option")&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!e.pseudos.empty(t)},header:function(t){return Y.test(t.nodeName)},input:function(t){return V.test(t.nodeName)},button:function(t){return S(t,"input")&&"button"===t.type||S(t,"button")},text:function(t){var e;return S(t,"input")&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:rt((function(){return[0]})),last:rt((function(t,e){return[e-1]})),eq:rt((function(t,e,i){return[i<0?i+e:i]})),even:rt((function(t,e){for(var i=0;i<e;i+=2)t.push(i);return t})),odd:rt((function(t,e){for(var i=1;i<e;i+=2)t.push(i);return t})),lt:rt((function(t,e,i){var n;for(n=i<0?i+e:i>e?e:i;--n>=0;)t.push(n);return t})),gt:rt((function(t,e,i){for(var n=i<0?i+e:i;++n<e;)t.push(n);return t}))}},e.pseudos.nth=e.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})e.pseudos[t]=nt(t);for(t in{submit:!0,reset:!0})e.pseudos[t]=st(t);function ht(){}function ut(t,i){var n,s,o,r,a,l,h,u=w[t+" "];if(u)return i?0:u.slice(0);for(a=t,l=[],h=e.preFilter;a;){for(r in n&&!(s=R.exec(a))||(s&&(a=a.slice(s[0].length)||a),l.push(o=[])),n=!1,(s=L.exec(a))&&(n=s.shift(),o.push({value:n,type:s[0].replace(E," ")}),a=a.slice(n.length)),e.filter)!(s=$[r].exec(a))||h[r]&&!(s=h[r](s))||(n=s.shift(),o.push({value:n,type:r,matches:s}),a=a.slice(n.length));if(!n)break}return i?a.length:a?Q.error(t):w(t,l).slice(0)}function ct(t){for(var e=0,i=t.length,n="";e<i;e++)n+=t[e].value;return n}function dt(t,e,i){var n=e.dir,s=e.next,o=s||n,r=i&&"parentNode"===o,a=b++;return e.first?function(e,i,s){for(;e=e[n];)if(1===e.nodeType||r)return t(e,i,s);return!1}:function(e,i,l){var h,u,c=[_,a];if(l){for(;e=e[n];)if((1===e.nodeType||r)&&t(e,i,l))return!0}else for(;e=e[n];)if(1===e.nodeType||r)if(u=e[v]||(e[v]={}),s&&S(e,s))e=e[n]||e;else{if((h=u[o])&&h[0]===_&&h[1]===a)return c[2]=h[2];if(u[o]=c,c[2]=t(e,i,l))return!0}return!1}}function pt(t){return t.length>1?function(e,i,n){for(var s=t.length;s--;)if(!t[s](e,i,n))return!1;return!0}:t[0]}function ft(t,e,i,n,s){for(var o,r=[],a=0,l=t.length,h=null!=e;a<l;a++)(o=t[a])&&(i&&!i(o,n,s)||(r.push(o),h&&e.push(a)));return r}function gt(t,e,i,n,s,o){return n&&!n[v]&&(n=gt(n)),s&&!s[v]&&(s=gt(s,o)),et((function(o,r,a,l){var h,c,d,p,f=[],m=[],v=r.length,_=o||function(t,e,i){for(var n=0,s=e.length;n<s;n++)Q(t,e[n],i);return i}(e||"*",a.nodeType?[a]:a,[]),b=!t||!o&&e?_:ft(_,f,t,a,l);if(i?i(b,p=s||(o?t:v||n)?[]:r,a,l):p=b,n)for(h=ft(p,m),n(h,[],a,l),c=h.length;c--;)(d=h[c])&&(p[m[c]]=!(b[m[c]]=d));if(o){if(s||t){if(s){for(h=[],c=p.length;c--;)(d=p[c])&&h.push(b[c]=d);s(null,p=[],h,l)}for(c=p.length;c--;)(d=p[c])&&(h=s?u.call(o,d):f[c])>-1&&(o[h]=!(r[h]=d))}}else p=ft(p===r?p.splice(v,p.length):p),s?s(null,r,p,l):g.apply(r,p)}))}function mt(t){for(var n,s,o,r=t.length,a=e.relative[t[0].type],l=a||e.relative[" "],h=a?1:0,c=dt((function(t){return t===n}),l,!0),d=dt((function(t){return u.call(n,t)>-1}),l,!0),p=[function(t,e,s){var o=!a&&(s||e!=i)||((n=e).nodeType?c(t,e,s):d(t,e,s));return n=null,o}];h<r;h++)if(s=e.relative[t[h].type])p=[dt(pt(p),s)];else{if((s=e.filter[t[h].type].apply(null,t[h].matches))[v]){for(o=++h;o<r&&!e.relative[t[o].type];o++);return gt(h>1&&pt(p),h>1&&ct(t.slice(0,h-1).concat({value:" "===t[h-2].type?"*":""})).replace(E,"$1"),s,h<o&&mt(t.slice(h,o)),o<r&&mt(t=t.slice(o)),o<r&&ct(t))}p.push(s)}return pt(p)}function vt(t,n){var s,o=[],r=[],a=x[t+" "];if(!a){for(n||(n=ut(t)),s=n.length;s--;)(a=mt(n[s]))[v]?o.push(a):r.push(a);a=x(t,function(t,n){var s=n.length>0,o=t.length>0,r=function(r,a,h,u,d){var p,f,m,v=0,b="0",y=r&&[],w=[],x=i,C=r||o&&e.find.TAG("*",d),k=_+=null==x?1:Math.random()||.1,T=C.length;for(d&&(i=a==l||a||d);b!==T&&null!=(p=C[b]);b++){if(o&&p){for(f=0,a||p.ownerDocument==l||(lt(p),h=!c);m=t[f++];)if(m(p,a||l,h)){g.call(u,p);break}d&&(_=k)}s&&((p=!m&&p)&&v--,r&&y.push(p))}if(v+=b,s&&b!==v){for(f=0;m=n[f++];)m(y,w,a,h);if(r){if(v>0)for(;b--;)y[b]||w[b]||(w[b]=I.call(u));w=ft(w)}g.apply(u,w),d&&!r&&w.length>0&&v+n.length>1&&D.uniqueSort(u)}return d&&(_=k,i=x),y};return s?et(r):r}(r,o)),a.selector=t}return a}function _t(t,i,n,s){var o,r,a,l,h,u="function"==typeof t&&t,d=!s&&ut(t=u.selector||t);if(n=n||[],1===d.length){if((r=d[0]=d[0].slice(0)).length>2&&"ID"===(a=r[0]).type&&9===i.nodeType&&c&&e.relative[r[1].type]){if(!(i=(e.find.ID(a.matches[0].replace(X,G),i)||[])[0]))return n;u&&(i=i.parentNode),t=t.slice(r.shift().value.length)}for(o=$.needsContext.test(t)?0:r.length;o--&&(a=r[o],!e.relative[l=a.type]);)if((h=e.find[l])&&(s=h(a.matches[0].replace(X,G),K.test(r[0].type)&&at(i.parentNode)||i))){if(r.splice(o,1),!(t=s.length&&ct(r)))return g.apply(n,s),n;break}}return(u||vt(t,d))(s,i,!c,n,!i||K.test(t)&&at(i.parentNode)||i),n}ht.prototype=e.filters=e.pseudos,e.setFilters=new ht,m.sortStable=v.split("").sort(k).join("")===v,lt(),m.sortDetached=it((function(t){return 1&t.compareDocumentPosition(l.createElement("fieldset"))})),D.find=Q,D.expr[":"]=D.expr.pseudos,D.unique=D.uniqueSort,Q.compile=vt,Q.select=_t,Q.setDocument=lt,Q.tokenize=ut,Q.escape=D.escapeSelector,Q.getText=D.text,Q.isXML=D.isXMLDoc,Q.selectors=D.expr,Q.support=D.support,Q.uniqueSort=D.uniqueSort}();var z=function(t,e,i){for(var n=[],s=void 0!==i;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(s&&D(t).is(i))break;n.push(t)}return n},W=function(t,e){for(var i=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i},R=D.expr.match.needsContext,L=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function j(t,e,i){return v(e)?D.grep(t,(function(t,n){return!!e.call(t,n,t)!==i})):e.nodeType?D.grep(t,(function(t){return t===e!==i})):"string"!=typeof e?D.grep(t,(function(t){return u.call(e,t)>-1!==i})):D.filter(e,t,i)}D.filter=function(t,e,i){var n=e[0];return i&&(t=":not("+t+")"),1===e.length&&1===n.nodeType?D.find.matchesSelector(n,t)?[n]:[]:D.find.matches(t,D.grep(e,(function(t){return 1===t.nodeType})))},D.fn.extend({find:function(t){var e,i,n=this.length,s=this;if("string"!=typeof t)return this.pushStack(D(t).filter((function(){for(e=0;e<n;e++)if(D.contains(s[e],this))return!0})));for(i=this.pushStack([]),e=0;e<n;e++)D.find(t,s[e],i);return n>1?D.uniqueSort(i):i},filter:function(t){return this.pushStack(j(this,t||[],!1))},not:function(t){return this.pushStack(j(this,t||[],!0))},is:function(t){return!!j(this,"string"==typeof t&&R.test(t)?D(t):t||[],!1).length}});var q,B=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(D.fn.init=function(t,e,i){var n,s;if(!t)return this;if(i=i||q,"string"==typeof t){if(!(n="<"===t[0]&&">"===t[t.length-1]&&t.length>=3?[null,t,null]:B.exec(t))||!n[1]&&e)return!e||e.jquery?(e||i).find(t):this.constructor(e).find(t);if(n[1]){if(e=e instanceof D?e[0]:e,D.merge(this,D.parseHTML(n[1],e&&e.nodeType?e.ownerDocument||e:b,!0)),L.test(n[1])&&D.isPlainObject(e))for(n in e)v(this[n])?this[n](e[n]):this.attr(n,e[n]);return this}return(s=b.getElementById(n[2]))&&(this[0]=s,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):v(t)?void 0!==i.ready?i.ready(t):t(D):D.makeArray(t,this)}).prototype=D.fn,q=D(b);var $=/^(?:parents|prev(?:Until|All))/,V={children:!0,contents:!0,next:!0,prev:!0};function Y(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}D.fn.extend({has:function(t){var e=D(t,this),i=e.length;return this.filter((function(){for(var t=0;t<i;t++)if(D.contains(this,e[t]))return!0}))},closest:function(t,e){var i,n=0,s=this.length,o=[],r="string"!=typeof t&&D(t);if(!R.test(t))for(;n<s;n++)for(i=this[n];i&&i!==e;i=i.parentNode)if(i.nodeType<11&&(r?r.index(i)>-1:1===i.nodeType&&D.find.matchesSelector(i,t))){o.push(i);break}return this.pushStack(o.length>1?D.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?u.call(D(t),this[0]):u.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(D.uniqueSort(D.merge(this.get(),D(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),D.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return z(t,"parentNode")},parentsUntil:function(t,e,i){return z(t,"parentNode",i)},next:function(t){return Y(t,"nextSibling")},prev:function(t){return Y(t,"previousSibling")},nextAll:function(t){return z(t,"nextSibling")},prevAll:function(t){return z(t,"previousSibling")},nextUntil:function(t,e,i){return z(t,"nextSibling",i)},prevUntil:function(t,e,i){return z(t,"previousSibling",i)},siblings:function(t){return W((t.parentNode||{}).firstChild,t)},children:function(t){return W(t.firstChild)},contents:function(t){return null!=t.contentDocument&&r(t.contentDocument)?t.contentDocument:(S(t,"template")&&(t=t.content||t),D.merge([],t.childNodes))}},(function(t,e){D.fn[t]=function(i,n){var s=D.map(this,e,i);return"Until"!==t.slice(-5)&&(n=i),n&&"string"==typeof n&&(s=D.filter(n,s)),this.length>1&&(V[t]||D.uniqueSort(s),$.test(t)&&s.reverse()),this.pushStack(s)}}));var U=/[^\x20\t\r\n\f]+/g;function K(t){return t}function X(t){throw t}function G(t,e,i,n){var s;try{t&&v(s=t.promise)?s.call(t).done(e).fail(i):t&&v(s=t.then)?s.call(t,e,i):e.apply(void 0,[t].slice(n))}catch(t){i.apply(void 0,[t])}}D.Callbacks=function(t){t="string"==typeof t?function(t){var e={};return D.each(t.match(U)||[],(function(t,i){e[i]=!0})),e}(t):D.extend({},t);var e,i,n,s,o=[],r=[],a=-1,l=function(){for(s=s||t.once,n=e=!0;r.length;a=-1)for(i=r.shift();++a<o.length;)!1===o[a].apply(i[0],i[1])&&t.stopOnFalse&&(a=o.length,i=!1);t.memory||(i=!1),e=!1,s&&(o=i?[]:"")},h={add:function(){return o&&(i&&!e&&(a=o.length-1,r.push(i)),function e(i){D.each(i,(function(i,n){v(n)?t.unique&&h.has(n)||o.push(n):n&&n.length&&"string"!==x(n)&&e(n)}))}(arguments),i&&!e&&l()),this},remove:function(){return D.each(arguments,(function(t,e){for(var i;(i=D.inArray(e,o,i))>-1;)o.splice(i,1),i<=a&&a--})),this},has:function(t){return t?D.inArray(t,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return s=r=[],o=i="",this},disabled:function(){return!o},lock:function(){return s=r=[],i||e||(o=i=""),this},locked:function(){return!!s},fireWith:function(t,i){return s||(i=[t,(i=i||[]).slice?i.slice():i],r.push(i),e||l()),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!n}};return h},D.extend({Deferred:function(t){var e=[["notify","progress",D.Callbacks("memory"),D.Callbacks("memory"),2],["resolve","done",D.Callbacks("once memory"),D.Callbacks("once memory"),0,"resolved"],["reject","fail",D.Callbacks("once memory"),D.Callbacks("once memory"),1,"rejected"]],i="pending",s={state:function(){return i},always:function(){return o.done(arguments).fail(arguments),this},catch:function(t){return s.then(null,t)},pipe:function(){var t=arguments;return D.Deferred((function(i){D.each(e,(function(e,n){var s=v(t[n[4]])&&t[n[4]];o[n[1]]((function(){var t=s&&s.apply(this,arguments);t&&v(t.promise)?t.promise().progress(i.notify).done(i.resolve).fail(i.reject):i[n[0]+"With"](this,s?[t]:arguments)}))})),t=null})).promise()},then:function(t,i,s){var o=0;function r(t,e,i,s){return function(){var a=this,l=arguments,h=function(){var n,h;if(!(t<o)){if((n=i.apply(a,l))===e.promise())throw new TypeError("Thenable self-resolution");h=n&&("object"==typeof n||"function"==typeof n)&&n.then,v(h)?s?h.call(n,r(o,e,K,s),r(o,e,X,s)):(o++,h.call(n,r(o,e,K,s),r(o,e,X,s),r(o,e,K,e.notifyWith))):(i!==K&&(a=void 0,l=[n]),(s||e.resolveWith)(a,l))}},u=s?h:function(){try{h()}catch(n){D.Deferred.exceptionHook&&D.Deferred.exceptionHook(n,u.error),t+1>=o&&(i!==X&&(a=void 0,l=[n]),e.rejectWith(a,l))}};t?u():(D.Deferred.getErrorHook?u.error=D.Deferred.getErrorHook():D.Deferred.getStackHook&&(u.error=D.Deferred.getStackHook()),n.setTimeout(u))}}return D.Deferred((function(n){e[0][3].add(r(0,n,v(s)?s:K,n.notifyWith)),e[1][3].add(r(0,n,v(t)?t:K)),e[2][3].add(r(0,n,v(i)?i:X))})).promise()},promise:function(t){return null!=t?D.extend(t,s):s}},o={};return D.each(e,(function(t,n){var r=n[2],a=n[5];s[n[1]]=r.add,a&&r.add((function(){i=a}),e[3-t][2].disable,e[3-t][3].disable,e[0][2].lock,e[0][3].lock),r.add(n[3].fire),o[n[0]]=function(){return o[n[0]+"With"](this===o?void 0:this,arguments),this},o[n[0]+"With"]=r.fireWith})),s.promise(o),t&&t.call(o,o),o},when:function(t){var e=arguments.length,i=e,n=Array(i),s=a.call(arguments),o=D.Deferred(),r=function(t){return function(i){n[t]=this,s[t]=arguments.length>1?a.call(arguments):i,--e||o.resolveWith(n,s)}};if(e<=1&&(G(t,o.done(r(i)).resolve,o.reject,!e),"pending"===o.state()||v(s[i]&&s[i].then)))return o.then();for(;i--;)G(s[i],r(i),o.reject);return o.promise()}});var J=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;D.Deferred.exceptionHook=function(t,e){n.console&&n.console.warn&&t&&J.test(t.name)&&n.console.warn("jQuery.Deferred exception: "+t.message,t.stack,e)},D.readyException=function(t){n.setTimeout((function(){throw t}))};var Z=D.Deferred();function Q(){b.removeEventListener("DOMContentLoaded",Q),n.removeEventListener("load",Q),D.ready()}D.fn.ready=function(t){return Z.then(t).catch((function(t){D.readyException(t)})),this},D.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--D.readyWait:D.isReady)||(D.isReady=!0,!0!==t&&--D.readyWait>0||Z.resolveWith(b,[D]))}}),D.ready.then=Z.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?n.setTimeout(D.ready):(b.addEventListener("DOMContentLoaded",Q),n.addEventListener("load",Q));var tt=function(t,e,i,n,s,o,r){var a=0,l=t.length,h=null==i;if("object"===x(i))for(a in s=!0,i)tt(t,e,a,i[a],!0,o,r);else if(void 0!==n&&(s=!0,v(n)||(r=!0),h&&(r?(e.call(t,n),e=null):(h=e,e=function(t,e,i){return h.call(D(t),i)})),e))for(;a<l;a++)e(t[a],i,r?n:n.call(t[a],a,e(t[a],i)));return s?t:h?e.call(t):l?e(t[0],i):o},et=/^-ms-/,it=/-([a-z])/g;function nt(t,e){return e.toUpperCase()}function st(t){return t.replace(et,"ms-").replace(it,nt)}var ot=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function rt(){this.expando=D.expando+rt.uid++}rt.uid=1,rt.prototype={cache:function(t){var e=t[this.expando];return e||(e={},ot(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,i){var n,s=this.cache(t);if("string"==typeof e)s[st(e)]=i;else for(n in e)s[st(n)]=e[n];return s},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][st(e)]},access:function(t,e,i){return void 0===e||e&&"string"==typeof e&&void 0===i?this.get(t,e):(this.set(t,e,i),void 0!==i?i:e)},remove:function(t,e){var i,n=t[this.expando];if(void 0!==n){if(void 0!==e){i=(e=Array.isArray(e)?e.map(st):(e=st(e))in n?[e]:e.match(U)||[]).length;for(;i--;)delete n[e[i]]}(void 0===e||D.isEmptyObject(n))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!D.isEmptyObject(e)}};var at=new rt,lt=new rt,ht=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ut=/[A-Z]/g;function ct(t,e,i){var n;if(void 0===i&&1===t.nodeType)if(n="data-"+e.replace(ut,"-$&").toLowerCase(),"string"==typeof(i=t.getAttribute(n))){try{i=function(t){return"true"===t||"false"!==t&&("null"===t?null:t===+t+""?+t:ht.test(t)?JSON.parse(t):t)}(i)}catch(t){}lt.set(t,e,i)}else i=void 0;return i}D.extend({hasData:function(t){return lt.hasData(t)||at.hasData(t)},data:function(t,e,i){return lt.access(t,e,i)},removeData:function(t,e){lt.remove(t,e)},_data:function(t,e,i){return at.access(t,e,i)},_removeData:function(t,e){at.remove(t,e)}}),D.fn.extend({data:function(t,e){var i,n,s,o=this[0],r=o&&o.attributes;if(void 0===t){if(this.length&&(s=lt.get(o),1===o.nodeType&&!at.get(o,"hasDataAttrs"))){for(i=r.length;i--;)r[i]&&0===(n=r[i].name).indexOf("data-")&&(n=st(n.slice(5)),ct(o,n,s[n]));at.set(o,"hasDataAttrs",!0)}return s}return"object"==typeof t?this.each((function(){lt.set(this,t)})):tt(this,(function(e){var i;if(o&&void 0===e)return void 0!==(i=lt.get(o,t))||void 0!==(i=ct(o,t))?i:void 0;this.each((function(){lt.set(this,t,e)}))}),null,e,arguments.length>1,null,!0)},removeData:function(t){return this.each((function(){lt.remove(this,t)}))}}),D.extend({queue:function(t,e,i){var n;if(t)return e=(e||"fx")+"queue",n=at.get(t,e),i&&(!n||Array.isArray(i)?n=at.access(t,e,D.makeArray(i)):n.push(i)),n||[]},dequeue:function(t,e){e=e||"fx";var i=D.queue(t,e),n=i.length,s=i.shift(),o=D._queueHooks(t,e);"inprogress"===s&&(s=i.shift(),n--),s&&("fx"===e&&i.unshift("inprogress"),delete o.stop,s.call(t,(function(){D.dequeue(t,e)}),o)),!n&&o&&o.empty.fire()},_queueHooks:function(t,e){var i=e+"queueHooks";return at.get(t,i)||at.access(t,i,{empty:D.Callbacks("once memory").add((function(){at.remove(t,[e+"queue",i])}))})}}),D.fn.extend({queue:function(t,e){var i=2;return"string"!=typeof t&&(e=t,t="fx",i--),arguments.length<i?D.queue(this[0],t):void 0===e?this:this.each((function(){var i=D.queue(this,t,e);D._queueHooks(this,t),"fx"===t&&"inprogress"!==i[0]&&D.dequeue(this,t)}))},dequeue:function(t){return this.each((function(){D.dequeue(this,t)}))},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var i,n=1,s=D.Deferred(),o=this,r=this.length,a=function(){--n||s.resolveWith(o,[o])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";r--;)(i=at.get(o[r],t+"queueHooks"))&&i.empty&&(n++,i.empty.add(a));return a(),s.promise(e)}});var dt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,pt=new RegExp("^(?:([+-])=|)("+dt+")([a-z%]*)$","i"),ft=["Top","Right","Bottom","Left"],gt=b.documentElement,mt=function(t){return D.contains(t.ownerDocument,t)},vt={composed:!0};gt.getRootNode&&(mt=function(t){return D.contains(t.ownerDocument,t)||t.getRootNode(vt)===t.ownerDocument});var _t=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&mt(t)&&"none"===D.css(t,"display")};function bt(t,e,i,n){var s,o,r=20,a=n?function(){return n.cur()}:function(){return D.css(t,e,"")},l=a(),h=i&&i[3]||(D.cssNumber[e]?"":"px"),u=t.nodeType&&(D.cssNumber[e]||"px"!==h&&+l)&&pt.exec(D.css(t,e));if(u&&u[3]!==h){for(l/=2,h=h||u[3],u=+l||1;r--;)D.style(t,e,u+h),(1-o)*(1-(o=a()/l||.5))<=0&&(r=0),u/=o;u*=2,D.style(t,e,u+h),i=i||[]}return i&&(u=+u||+l||0,s=i[1]?u+(i[1]+1)*i[2]:+i[2],n&&(n.unit=h,n.start=u,n.end=s)),s}var yt={};function wt(t){var e,i=t.ownerDocument,n=t.nodeName,s=yt[n];return s||(e=i.body.appendChild(i.createElement(n)),s=D.css(e,"display"),e.parentNode.removeChild(e),"none"===s&&(s="block"),yt[n]=s,s)}function xt(t,e){for(var i,n,s=[],o=0,r=t.length;o<r;o++)(n=t[o]).style&&(i=n.style.display,e?("none"===i&&(s[o]=at.get(n,"display")||null,s[o]||(n.style.display="")),""===n.style.display&&_t(n)&&(s[o]=wt(n))):"none"!==i&&(s[o]="none",at.set(n,"display",i)));for(o=0;o<r;o++)null!=s[o]&&(t[o].style.display=s[o]);return t}D.fn.extend({show:function(){return xt(this,!0)},hide:function(){return xt(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each((function(){_t(this)?D(this).show():D(this).hide()}))}});var Ct,kt,Dt=/^(?:checkbox|radio)$/i,Tt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,St=/^$|^module$|\/(?:java|ecma)script/i;Ct=b.createDocumentFragment().appendChild(b.createElement("div")),(kt=b.createElement("input")).setAttribute("type","radio"),kt.setAttribute("checked","checked"),kt.setAttribute("name","t"),Ct.appendChild(kt),m.checkClone=Ct.cloneNode(!0).cloneNode(!0).lastChild.checked,Ct.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!Ct.cloneNode(!0).lastChild.defaultValue,Ct.innerHTML="<option></option>",m.option=!!Ct.lastChild;var It={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Pt(t,e){var i;return i=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&S(t,e)?D.merge([t],i):i}function At(t,e){for(var i=0,n=t.length;i<n;i++)at.set(t[i],"globalEval",!e||at.get(e[i],"globalEval"))}It.tbody=It.tfoot=It.colgroup=It.caption=It.thead,It.th=It.td,m.option||(It.optgroup=It.option=[1,"<select multiple='multiple'>","</select>"]);var Mt=/<|&#?\w+;/;function Et(t,e,i,n,s){for(var o,r,a,l,h,u,c=e.createDocumentFragment(),d=[],p=0,f=t.length;p<f;p++)if((o=t[p])||0===o)if("object"===x(o))D.merge(d,o.nodeType?[o]:o);else if(Mt.test(o)){for(r=r||c.appendChild(e.createElement("div")),a=(Tt.exec(o)||["",""])[1].toLowerCase(),l=It[a]||It._default,r.innerHTML=l[1]+D.htmlPrefilter(o)+l[2],u=l[0];u--;)r=r.lastChild;D.merge(d,r.childNodes),(r=c.firstChild).textContent=""}else d.push(e.createTextNode(o));for(c.textContent="",p=0;o=d[p++];)if(n&&D.inArray(o,n)>-1)s&&s.push(o);else if(h=mt(o),r=Pt(c.appendChild(o),"script"),h&&At(r),i)for(u=0;o=r[u++];)St.test(o.type||"")&&i.push(o);return c}var Ot=/^([^.]*)(?:\.(.+)|)/;function Ht(){return!0}function Ft(){return!1}function Nt(t,e,i,n,s,o){var r,a;if("object"==typeof e){for(a in"string"!=typeof i&&(n=n||i,i=void 0),e)Nt(t,a,i,n,e[a],o);return t}if(null==n&&null==s?(s=i,n=i=void 0):null==s&&("string"==typeof i?(s=n,n=void 0):(s=n,n=i,i=void 0)),!1===s)s=Ft;else if(!s)return t;return 1===o&&(r=s,s=function(t){return D().off(t),r.apply(this,arguments)},s.guid=r.guid||(r.guid=D.guid++)),t.each((function(){D.event.add(this,e,s,n,i)}))}function zt(t,e,i){i?(at.set(t,e,!1),D.event.add(t,e,{namespace:!1,handler:function(t){var i,n=at.get(this,e);if(1&t.isTrigger&&this[e]){if(n)(D.event.special[e]||{}).delegateType&&t.stopPropagation();else if(n=a.call(arguments),at.set(this,e,n),this[e](),i=at.get(this,e),at.set(this,e,!1),n!==i)return t.stopImmediatePropagation(),t.preventDefault(),i}else n&&(at.set(this,e,D.event.trigger(n[0],n.slice(1),this)),t.stopPropagation(),t.isImmediatePropagationStopped=Ht)}})):void 0===at.get(t,e)&&D.event.add(t,e,Ht)}D.event={global:{},add:function(t,e,i,n,s){var o,r,a,l,h,u,c,d,p,f,g,m=at.get(t);if(ot(t))for(i.handler&&(i=(o=i).handler,s=o.selector),s&&D.find.matchesSelector(gt,s),i.guid||(i.guid=D.guid++),(l=m.events)||(l=m.events=Object.create(null)),(r=m.handle)||(r=m.handle=function(e){return void 0!==D&&D.event.triggered!==e.type?D.event.dispatch.apply(t,arguments):void 0}),h=(e=(e||"").match(U)||[""]).length;h--;)p=g=(a=Ot.exec(e[h])||[])[1],f=(a[2]||"").split(".").sort(),p&&(c=D.event.special[p]||{},p=(s?c.delegateType:c.bindType)||p,c=D.event.special[p]||{},u=D.extend({type:p,origType:g,data:n,handler:i,guid:i.guid,selector:s,needsContext:s&&D.expr.match.needsContext.test(s),namespace:f.join(".")},o),(d=l[p])||((d=l[p]=[]).delegateCount=0,c.setup&&!1!==c.setup.call(t,n,f,r)||t.addEventListener&&t.addEventListener(p,r)),c.add&&(c.add.call(t,u),u.handler.guid||(u.handler.guid=i.guid)),s?d.splice(d.delegateCount++,0,u):d.push(u),D.event.global[p]=!0)},remove:function(t,e,i,n,s){var o,r,a,l,h,u,c,d,p,f,g,m=at.hasData(t)&&at.get(t);if(m&&(l=m.events)){for(h=(e=(e||"").match(U)||[""]).length;h--;)if(p=g=(a=Ot.exec(e[h])||[])[1],f=(a[2]||"").split(".").sort(),p){for(c=D.event.special[p]||{},d=l[p=(n?c.delegateType:c.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),r=o=d.length;o--;)u=d[o],!s&&g!==u.origType||i&&i.guid!==u.guid||a&&!a.test(u.namespace)||n&&n!==u.selector&&("**"!==n||!u.selector)||(d.splice(o,1),u.selector&&d.delegateCount--,c.remove&&c.remove.call(t,u));r&&!d.length&&(c.teardown&&!1!==c.teardown.call(t,f,m.handle)||D.removeEvent(t,p,m.handle),delete l[p])}else for(p in l)D.event.remove(t,p+e[h],i,n,!0);D.isEmptyObject(l)&&at.remove(t,"handle events")}},dispatch:function(t){var e,i,n,s,o,r,a=new Array(arguments.length),l=D.event.fix(t),h=(at.get(this,"events")||Object.create(null))[l.type]||[],u=D.event.special[l.type]||{};for(a[0]=l,e=1;e<arguments.length;e++)a[e]=arguments[e];if(l.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,l)){for(r=D.event.handlers.call(this,l,h),e=0;(s=r[e++])&&!l.isPropagationStopped();)for(l.currentTarget=s.elem,i=0;(o=s.handlers[i++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(n=((D.event.special[o.origType]||{}).handle||o.handler).apply(s.elem,a))&&!1===(l.result=n)&&(l.preventDefault(),l.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,l),l.result}},handlers:function(t,e){var i,n,s,o,r,a=[],l=e.delegateCount,h=t.target;if(l&&h.nodeType&&!("click"===t.type&&t.button>=1))for(;h!==this;h=h.parentNode||this)if(1===h.nodeType&&("click"!==t.type||!0!==h.disabled)){for(o=[],r={},i=0;i<l;i++)void 0===r[s=(n=e[i]).selector+" "]&&(r[s]=n.needsContext?D(s,this).index(h)>-1:D.find(s,this,null,[h]).length),r[s]&&o.push(n);o.length&&a.push({elem:h,handlers:o})}return h=this,l<e.length&&a.push({elem:h,handlers:e.slice(l)}),a},addProp:function(t,e){Object.defineProperty(D.Event.prototype,t,{enumerable:!0,configurable:!0,get:v(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[D.expando]?t:new D.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return Dt.test(e.type)&&e.click&&S(e,"input")&&zt(e,"click",!0),!1},trigger:function(t){var e=this||t;return Dt.test(e.type)&&e.click&&S(e,"input")&&zt(e,"click"),!0},_default:function(t){var e=t.target;return Dt.test(e.type)&&e.click&&S(e,"input")&&at.get(e,"click")||S(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},D.removeEvent=function(t,e,i){t.removeEventListener&&t.removeEventListener(e,i)},D.Event=function(t,e){if(!(this instanceof D.Event))return new D.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?Ht:Ft,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&D.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[D.expando]=!0},D.Event.prototype={constructor:D.Event,isDefaultPrevented:Ft,isPropagationStopped:Ft,isImmediatePropagationStopped:Ft,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=Ht,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=Ht,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=Ht,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},D.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},D.event.addProp),D.each({focus:"focusin",blur:"focusout"},(function(t,e){function i(t){if(b.documentMode){var i=at.get(this,"handle"),n=D.event.fix(t);n.type="focusin"===t.type?"focus":"blur",n.isSimulated=!0,i(t),n.target===n.currentTarget&&i(n)}else D.event.simulate(e,t.target,D.event.fix(t))}D.event.special[t]={setup:function(){var n;if(zt(this,t,!0),!b.documentMode)return!1;(n=at.get(this,e))||this.addEventListener(e,i),at.set(this,e,(n||0)+1)},trigger:function(){return zt(this,t),!0},teardown:function(){var t;if(!b.documentMode)return!1;(t=at.get(this,e)-1)?at.set(this,e,t):(this.removeEventListener(e,i),at.remove(this,e))},_default:function(e){return at.get(e.target,t)},delegateType:e},D.event.special[e]={setup:function(){var n=this.ownerDocument||this.document||this,s=b.documentMode?this:n,o=at.get(s,e);o||(b.documentMode?this.addEventListener(e,i):n.addEventListener(t,i,!0)),at.set(s,e,(o||0)+1)},teardown:function(){var n=this.ownerDocument||this.document||this,s=b.documentMode?this:n,o=at.get(s,e)-1;o?at.set(s,e,o):(b.documentMode?this.removeEventListener(e,i):n.removeEventListener(t,i,!0),at.remove(s,e))}}})),D.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(t,e){D.event.special[t]={delegateType:e,bindType:e,handle:function(t){var i,n=t.relatedTarget,s=t.handleObj;return n&&(n===this||D.contains(this,n))||(t.type=s.origType,i=s.handler.apply(this,arguments),t.type=e),i}}})),D.fn.extend({on:function(t,e,i,n){return Nt(this,t,e,i,n)},one:function(t,e,i,n){return Nt(this,t,e,i,n,1)},off:function(t,e,i){var n,s;if(t&&t.preventDefault&&t.handleObj)return n=t.handleObj,D(t.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler),this;if("object"==typeof t){for(s in t)this.off(s,e,t[s]);return this}return!1!==e&&"function"!=typeof e||(i=e,e=void 0),!1===i&&(i=Ft),this.each((function(){D.event.remove(this,t,i,e)}))}});var Wt=/<script|<style|<link/i,Rt=/checked\s*(?:[^=]|=\s*.checked.)/i,Lt=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function jt(t,e){return S(t,"table")&&S(11!==e.nodeType?e:e.firstChild,"tr")&&D(t).children("tbody")[0]||t}function qt(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Bt(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function $t(t,e){var i,n,s,o,r,a;if(1===e.nodeType){if(at.hasData(t)&&(a=at.get(t).events))for(s in at.remove(e,"handle events"),a)for(i=0,n=a[s].length;i<n;i++)D.event.add(e,s,a[s][i]);lt.hasData(t)&&(o=lt.access(t),r=D.extend({},o),lt.set(e,r))}}function Vt(t,e){var i=e.nodeName.toLowerCase();"input"===i&&Dt.test(t.type)?e.checked=t.checked:"input"!==i&&"textarea"!==i||(e.defaultValue=t.defaultValue)}function Yt(t,e,i,n){e=l(e);var s,o,r,a,h,u,c=0,d=t.length,p=d-1,f=e[0],g=v(f);if(g||d>1&&"string"==typeof f&&!m.checkClone&&Rt.test(f))return t.each((function(s){var o=t.eq(s);g&&(e[0]=f.call(this,s,o.html())),Yt(o,e,i,n)}));if(d&&(o=(s=Et(e,t[0].ownerDocument,!1,t,n)).firstChild,1===s.childNodes.length&&(s=o),o||n)){for(a=(r=D.map(Pt(s,"script"),qt)).length;c<d;c++)h=s,c!==p&&(h=D.clone(h,!0,!0),a&&D.merge(r,Pt(h,"script"))),i.call(t[c],h,c);if(a)for(u=r[r.length-1].ownerDocument,D.map(r,Bt),c=0;c<a;c++)h=r[c],St.test(h.type||"")&&!at.access(h,"globalEval")&&D.contains(u,h)&&(h.src&&"module"!==(h.type||"").toLowerCase()?D._evalUrl&&!h.noModule&&D._evalUrl(h.src,{nonce:h.nonce||h.getAttribute("nonce")},u):w(h.textContent.replace(Lt,""),h,u))}return t}function Ut(t,e,i){for(var n,s=e?D.filter(e,t):t,o=0;null!=(n=s[o]);o++)i||1!==n.nodeType||D.cleanData(Pt(n)),n.parentNode&&(i&&mt(n)&&At(Pt(n,"script")),n.parentNode.removeChild(n));return t}D.extend({htmlPrefilter:function(t){return t},clone:function(t,e,i){var n,s,o,r,a=t.cloneNode(!0),l=mt(t);if(!(m.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||D.isXMLDoc(t)))for(r=Pt(a),n=0,s=(o=Pt(t)).length;n<s;n++)Vt(o[n],r[n]);if(e)if(i)for(o=o||Pt(t),r=r||Pt(a),n=0,s=o.length;n<s;n++)$t(o[n],r[n]);else $t(t,a);return(r=Pt(a,"script")).length>0&&At(r,!l&&Pt(t,"script")),a},cleanData:function(t){for(var e,i,n,s=D.event.special,o=0;void 0!==(i=t[o]);o++)if(ot(i)){if(e=i[at.expando]){if(e.events)for(n in e.events)s[n]?D.event.remove(i,n):D.removeEvent(i,n,e.handle);i[at.expando]=void 0}i[lt.expando]&&(i[lt.expando]=void 0)}}}),D.fn.extend({detach:function(t){return Ut(this,t,!0)},remove:function(t){return Ut(this,t)},text:function(t){return tt(this,(function(t){return void 0===t?D.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)}))}),null,t,arguments.length)},append:function(){return Yt(this,arguments,(function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||jt(this,t).appendChild(t)}))},prepend:function(){return Yt(this,arguments,(function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=jt(this,t);e.insertBefore(t,e.firstChild)}}))},before:function(){return Yt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this)}))},after:function(){return Yt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)}))},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(D.cleanData(Pt(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map((function(){return D.clone(this,t,e)}))},html:function(t){return tt(this,(function(t){var e=this[0]||{},i=0,n=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Wt.test(t)&&!It[(Tt.exec(t)||["",""])[1].toLowerCase()]){t=D.htmlPrefilter(t);try{for(;i<n;i++)1===(e=this[i]||{}).nodeType&&(D.cleanData(Pt(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)}),null,t,arguments.length)},replaceWith:function(){var t=[];return Yt(this,arguments,(function(e){var i=this.parentNode;D.inArray(this,t)<0&&(D.cleanData(Pt(this)),i&&i.replaceChild(e,this))}),t)}}),D.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(t,e){D.fn[t]=function(t){for(var i,n=[],s=D(t),o=s.length-1,r=0;r<=o;r++)i=r===o?this:this.clone(!0),D(s[r])[e](i),h.apply(n,i.get());return this.pushStack(n)}}));var Kt=new RegExp("^("+dt+")(?!px)[a-z%]+$","i"),Xt=/^--/,Gt=function(t){var e=t.ownerDocument.defaultView;return e&&e.opener||(e=n),e.getComputedStyle(t)},Jt=function(t,e,i){var n,s,o={};for(s in e)o[s]=t.style[s],t.style[s]=e[s];for(s in n=i.call(t),e)t.style[s]=o[s];return n},Zt=new RegExp(ft.join("|"),"i");function Qt(t,e,i){var n,s,o,r,a=Xt.test(e),l=t.style;return(i=i||Gt(t))&&(r=i.getPropertyValue(e)||i[e],a&&r&&(r=r.replace(E,"$1")||void 0),""!==r||mt(t)||(r=D.style(t,e)),!m.pixelBoxStyles()&&Kt.test(r)&&Zt.test(e)&&(n=l.width,s=l.minWidth,o=l.maxWidth,l.minWidth=l.maxWidth=l.width=r,r=i.width,l.width=n,l.minWidth=s,l.maxWidth=o)),void 0!==r?r+"":r}function te(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}!function(){function t(){if(u){h.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",gt.appendChild(h).appendChild(u);var t=n.getComputedStyle(u);i="1%"!==t.top,l=12===e(t.marginLeft),u.style.right="60%",r=36===e(t.right),s=36===e(t.width),u.style.position="absolute",o=12===e(u.offsetWidth/3),gt.removeChild(h),u=null}}function e(t){return Math.round(parseFloat(t))}var i,s,o,r,a,l,h=b.createElement("div"),u=b.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===u.style.backgroundClip,D.extend(m,{boxSizingReliable:function(){return t(),s},pixelBoxStyles:function(){return t(),r},pixelPosition:function(){return t(),i},reliableMarginLeft:function(){return t(),l},scrollboxSize:function(){return t(),o},reliableTrDimensions:function(){var t,e,i,s;return null==a&&(t=b.createElement("table"),e=b.createElement("tr"),i=b.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",e.style.cssText="box-sizing:content-box;border:1px solid",e.style.height="1px",i.style.height="9px",i.style.display="block",gt.appendChild(t).appendChild(e).appendChild(i),s=n.getComputedStyle(e),a=parseInt(s.height,10)+parseInt(s.borderTopWidth,10)+parseInt(s.borderBottomWidth,10)===e.offsetHeight,gt.removeChild(t)),a}}))}();var ee=["Webkit","Moz","ms"],ie=b.createElement("div").style,ne={};function se(t){var e=D.cssProps[t]||ne[t];return e||(t in ie?t:ne[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),i=ee.length;i--;)if((t=ee[i]+e)in ie)return t}(t)||t)}var oe=/^(none|table(?!-c[ea]).+)/,re={position:"absolute",visibility:"hidden",display:"block"},ae={letterSpacing:"0",fontWeight:"400"};function le(t,e,i){var n=pt.exec(e);return n?Math.max(0,n[2]-(i||0))+(n[3]||"px"):e}function he(t,e,i,n,s,o){var r="width"===e?1:0,a=0,l=0,h=0;if(i===(n?"border":"content"))return 0;for(;r<4;r+=2)"margin"===i&&(h+=D.css(t,i+ft[r],!0,s)),n?("content"===i&&(l-=D.css(t,"padding"+ft[r],!0,s)),"margin"!==i&&(l-=D.css(t,"border"+ft[r]+"Width",!0,s))):(l+=D.css(t,"padding"+ft[r],!0,s),"padding"!==i?l+=D.css(t,"border"+ft[r]+"Width",!0,s):a+=D.css(t,"border"+ft[r]+"Width",!0,s));return!n&&o>=0&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-o-l-a-.5))||0),l+h}function ue(t,e,i){var n=Gt(t),s=(!m.boxSizingReliable()||i)&&"border-box"===D.css(t,"boxSizing",!1,n),o=s,r=Qt(t,e,n),a="offset"+e[0].toUpperCase()+e.slice(1);if(Kt.test(r)){if(!i)return r;r="auto"}return(!m.boxSizingReliable()&&s||!m.reliableTrDimensions()&&S(t,"tr")||"auto"===r||!parseFloat(r)&&"inline"===D.css(t,"display",!1,n))&&t.getClientRects().length&&(s="border-box"===D.css(t,"boxSizing",!1,n),(o=a in t)&&(r=t[a])),(r=parseFloat(r)||0)+he(t,e,i||(s?"border":"content"),o,n,r)+"px"}function ce(t,e,i,n,s){return new ce.prototype.init(t,e,i,n,s)}D.extend({cssHooks:{opacity:{get:function(t,e){if(e){var i=Qt(t,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(t,e,i,n){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var s,o,r,a=st(e),l=Xt.test(e),h=t.style;if(l||(e=se(a)),r=D.cssHooks[e]||D.cssHooks[a],void 0===i)return r&&"get"in r&&void 0!==(s=r.get(t,!1,n))?s:h[e];"string"===(o=typeof i)&&(s=pt.exec(i))&&s[1]&&(i=bt(t,e,s),o="number"),null!=i&&i==i&&("number"!==o||l||(i+=s&&s[3]||(D.cssNumber[a]?"":"px")),m.clearCloneStyle||""!==i||0!==e.indexOf("background")||(h[e]="inherit"),r&&"set"in r&&void 0===(i=r.set(t,i,n))||(l?h.setProperty(e,i):h[e]=i))}},css:function(t,e,i,n){var s,o,r,a=st(e);return Xt.test(e)||(e=se(a)),(r=D.cssHooks[e]||D.cssHooks[a])&&"get"in r&&(s=r.get(t,!0,i)),void 0===s&&(s=Qt(t,e,n)),"normal"===s&&e in ae&&(s=ae[e]),""===i||i?(o=parseFloat(s),!0===i||isFinite(o)?o||0:s):s}}),D.each(["height","width"],(function(t,e){D.cssHooks[e]={get:function(t,i,n){if(i)return!oe.test(D.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?ue(t,e,n):Jt(t,re,(function(){return ue(t,e,n)}))},set:function(t,i,n){var s,o=Gt(t),r=!m.scrollboxSize()&&"absolute"===o.position,a=(r||n)&&"border-box"===D.css(t,"boxSizing",!1,o),l=n?he(t,e,n,a,o):0;return a&&r&&(l-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(o[e])-he(t,e,"border",!1,o)-.5)),l&&(s=pt.exec(i))&&"px"!==(s[3]||"px")&&(t.style[e]=i,i=D.css(t,e)),le(0,i,l)}}})),D.cssHooks.marginLeft=te(m.reliableMarginLeft,(function(t,e){if(e)return(parseFloat(Qt(t,"marginLeft"))||t.getBoundingClientRect().left-Jt(t,{marginLeft:0},(function(){return t.getBoundingClientRect().left})))+"px"})),D.each({margin:"",padding:"",border:"Width"},(function(t,e){D.cssHooks[t+e]={expand:function(i){for(var n=0,s={},o="string"==typeof i?i.split(" "):[i];n<4;n++)s[t+ft[n]+e]=o[n]||o[n-2]||o[0];return s}},"margin"!==t&&(D.cssHooks[t+e].set=le)})),D.fn.extend({css:function(t,e){return tt(this,(function(t,e,i){var n,s,o={},r=0;if(Array.isArray(e)){for(n=Gt(t),s=e.length;r<s;r++)o[e[r]]=D.css(t,e[r],!1,n);return o}return void 0!==i?D.style(t,e,i):D.css(t,e)}),t,e,arguments.length>1)}}),D.Tween=ce,ce.prototype={constructor:ce,init:function(t,e,i,n,s,o){this.elem=t,this.prop=i,this.easing=s||D.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=n,this.unit=o||(D.cssNumber[i]?"":"px")},cur:function(){var t=ce.propHooks[this.prop];return t&&t.get?t.get(this):ce.propHooks._default.get(this)},run:function(t){var e,i=ce.propHooks[this.prop];return this.options.duration?this.pos=e=D.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):ce.propHooks._default.set(this),this}},ce.prototype.init.prototype=ce.prototype,ce.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=D.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){D.fx.step[t.prop]?D.fx.step[t.prop](t):1!==t.elem.nodeType||!D.cssHooks[t.prop]&&null==t.elem.style[se(t.prop)]?t.elem[t.prop]=t.now:D.style(t.elem,t.prop,t.now+t.unit)}}},ce.propHooks.scrollTop=ce.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},D.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},D.fx=ce.prototype.init,D.fx.step={};var de,pe,fe=/^(?:toggle|show|hide)$/,ge=/queueHooks$/;function me(){pe&&(!1===b.hidden&&n.requestAnimationFrame?n.requestAnimationFrame(me):n.setTimeout(me,D.fx.interval),D.fx.tick())}function ve(){return n.setTimeout((function(){de=void 0})),de=Date.now()}function _e(t,e){var i,n=0,s={height:t};for(e=e?1:0;n<4;n+=2-e)s["margin"+(i=ft[n])]=s["padding"+i]=t;return e&&(s.opacity=s.width=t),s}function be(t,e,i){for(var n,s=(ye.tweeners[e]||[]).concat(ye.tweeners["*"]),o=0,r=s.length;o<r;o++)if(n=s[o].call(i,e,t))return n}function ye(t,e,i){var n,s,o=0,r=ye.prefilters.length,a=D.Deferred().always((function(){delete l.elem})),l=function(){if(s)return!1;for(var e=de||ve(),i=Math.max(0,h.startTime+h.duration-e),n=1-(i/h.duration||0),o=0,r=h.tweens.length;o<r;o++)h.tweens[o].run(n);return a.notifyWith(t,[h,n,i]),n<1&&r?i:(r||a.notifyWith(t,[h,1,0]),a.resolveWith(t,[h]),!1)},h=a.promise({elem:t,props:D.extend({},e),opts:D.extend(!0,{specialEasing:{},easing:D.easing._default},i),originalProperties:e,originalOptions:i,startTime:de||ve(),duration:i.duration,tweens:[],createTween:function(e,i){var n=D.Tween(t,h.opts,e,i,h.opts.specialEasing[e]||h.opts.easing);return h.tweens.push(n),n},stop:function(e){var i=0,n=e?h.tweens.length:0;if(s)return this;for(s=!0;i<n;i++)h.tweens[i].run(1);return e?(a.notifyWith(t,[h,1,0]),a.resolveWith(t,[h,e])):a.rejectWith(t,[h,e]),this}}),u=h.props;for(!function(t,e){var i,n,s,o,r;for(i in t)if(s=e[n=st(i)],o=t[i],Array.isArray(o)&&(s=o[1],o=t[i]=o[0]),i!==n&&(t[n]=o,delete t[i]),(r=D.cssHooks[n])&&"expand"in r)for(i in o=r.expand(o),delete t[n],o)i in t||(t[i]=o[i],e[i]=s);else e[n]=s}(u,h.opts.specialEasing);o<r;o++)if(n=ye.prefilters[o].call(h,t,u,h.opts))return v(n.stop)&&(D._queueHooks(h.elem,h.opts.queue).stop=n.stop.bind(n)),n;return D.map(u,be,h),v(h.opts.start)&&h.opts.start.call(t,h),h.progress(h.opts.progress).done(h.opts.done,h.opts.complete).fail(h.opts.fail).always(h.opts.always),D.fx.timer(D.extend(l,{elem:t,anim:h,queue:h.opts.queue})),h}D.Animation=D.extend(ye,{tweeners:{"*":[function(t,e){var i=this.createTween(t,e);return bt(i.elem,t,pt.exec(e),i),i}]},tweener:function(t,e){v(t)?(e=t,t=["*"]):t=t.match(U);for(var i,n=0,s=t.length;n<s;n++)i=t[n],ye.tweeners[i]=ye.tweeners[i]||[],ye.tweeners[i].unshift(e)},prefilters:[function(t,e,i){var n,s,o,r,a,l,h,u,c="width"in e||"height"in e,d=this,p={},f=t.style,g=t.nodeType&&_t(t),m=at.get(t,"fxshow");for(n in i.queue||(null==(r=D._queueHooks(t,"fx")).unqueued&&(r.unqueued=0,a=r.empty.fire,r.empty.fire=function(){r.unqueued||a()}),r.unqueued++,d.always((function(){d.always((function(){r.unqueued--,D.queue(t,"fx").length||r.empty.fire()}))}))),e)if(s=e[n],fe.test(s)){if(delete e[n],o=o||"toggle"===s,s===(g?"hide":"show")){if("show"!==s||!m||void 0===m[n])continue;g=!0}p[n]=m&&m[n]||D.style(t,n)}if((l=!D.isEmptyObject(e))||!D.isEmptyObject(p))for(n in c&&1===t.nodeType&&(i.overflow=[f.overflow,f.overflowX,f.overflowY],null==(h=m&&m.display)&&(h=at.get(t,"display")),"none"===(u=D.css(t,"display"))&&(h?u=h:(xt([t],!0),h=t.style.display||h,u=D.css(t,"display"),xt([t]))),("inline"===u||"inline-block"===u&&null!=h)&&"none"===D.css(t,"float")&&(l||(d.done((function(){f.display=h})),null==h&&(u=f.display,h="none"===u?"":u)),f.display="inline-block")),i.overflow&&(f.overflow="hidden",d.always((function(){f.overflow=i.overflow[0],f.overflowX=i.overflow[1],f.overflowY=i.overflow[2]}))),l=!1,p)l||(m?"hidden"in m&&(g=m.hidden):m=at.access(t,"fxshow",{display:h}),o&&(m.hidden=!g),g&&xt([t],!0),d.done((function(){for(n in g||xt([t]),at.remove(t,"fxshow"),p)D.style(t,n,p[n])}))),l=be(g?m[n]:0,n,d),n in m||(m[n]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?ye.prefilters.unshift(t):ye.prefilters.push(t)}}),D.speed=function(t,e,i){var n=t&&"object"==typeof t?D.extend({},t):{complete:i||!i&&e||v(t)&&t,duration:t,easing:i&&e||e&&!v(e)&&e};return D.fx.off?n.duration=0:"number"!=typeof n.duration&&(n.duration in D.fx.speeds?n.duration=D.fx.speeds[n.duration]:n.duration=D.fx.speeds._default),null!=n.queue&&!0!==n.queue||(n.queue="fx"),n.old=n.complete,n.complete=function(){v(n.old)&&n.old.call(this),n.queue&&D.dequeue(this,n.queue)},n},D.fn.extend({fadeTo:function(t,e,i,n){return this.filter(_t).css("opacity",0).show().end().animate({opacity:e},t,i,n)},animate:function(t,e,i,n){var s=D.isEmptyObject(t),o=D.speed(e,i,n),r=function(){var e=ye(this,D.extend({},t),o);(s||at.get(this,"finish"))&&e.stop(!0)};return r.finish=r,s||!1===o.queue?this.each(r):this.queue(o.queue,r)},stop:function(t,e,i){var n=function(t){var e=t.stop;delete t.stop,e(i)};return"string"!=typeof t&&(i=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each((function(){var e=!0,s=null!=t&&t+"queueHooks",o=D.timers,r=at.get(this);if(s)r[s]&&r[s].stop&&n(r[s]);else for(s in r)r[s]&&r[s].stop&&ge.test(s)&&n(r[s]);for(s=o.length;s--;)o[s].elem!==this||null!=t&&o[s].queue!==t||(o[s].anim.stop(i),e=!1,o.splice(s,1));!e&&i||D.dequeue(this,t)}))},finish:function(t){return!1!==t&&(t=t||"fx"),this.each((function(){var e,i=at.get(this),n=i[t+"queue"],s=i[t+"queueHooks"],o=D.timers,r=n?n.length:0;for(i.finish=!0,D.queue(this,t,[]),s&&s.stop&&s.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===t&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<r;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete i.finish}))}}),D.each(["toggle","show","hide"],(function(t,e){var i=D.fn[e];D.fn[e]=function(t,n,s){return null==t||"boolean"==typeof t?i.apply(this,arguments):this.animate(_e(e,!0),t,n,s)}})),D.each({slideDown:_e("show"),slideUp:_e("hide"),slideToggle:_e("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(t,e){D.fn[t]=function(t,i,n){return this.animate(e,t,i,n)}})),D.timers=[],D.fx.tick=function(){var t,e=0,i=D.timers;for(de=Date.now();e<i.length;e++)(t=i[e])()||i[e]!==t||i.splice(e--,1);i.length||D.fx.stop(),de=void 0},D.fx.timer=function(t){D.timers.push(t),D.fx.start()},D.fx.interval=13,D.fx.start=function(){pe||(pe=!0,me())},D.fx.stop=function(){pe=null},D.fx.speeds={slow:600,fast:200,_default:400},D.fn.delay=function(t,e){return t=D.fx&&D.fx.speeds[t]||t,e=e||"fx",this.queue(e,(function(e,i){var s=n.setTimeout(e,t);i.stop=function(){n.clearTimeout(s)}}))},function(){var t=b.createElement("input"),e=b.createElement("select").appendChild(b.createElement("option"));t.type="checkbox",m.checkOn=""!==t.value,m.optSelected=e.selected,(t=b.createElement("input")).value="t",t.type="radio",m.radioValue="t"===t.value}();var we,xe=D.expr.attrHandle;D.fn.extend({attr:function(t,e){return tt(this,D.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each((function(){D.removeAttr(this,t)}))}}),D.extend({attr:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?D.prop(t,e,i):(1===o&&D.isXMLDoc(t)||(s=D.attrHooks[e.toLowerCase()]||(D.expr.match.bool.test(e)?we:void 0)),void 0!==i?null===i?void D.removeAttr(t,e):s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:(t.setAttribute(e,i+""),i):s&&"get"in s&&null!==(n=s.get(t,e))?n:null==(n=D.find.attr(t,e))?void 0:n)},attrHooks:{type:{set:function(t,e){if(!m.radioValue&&"radio"===e&&S(t,"input")){var i=t.value;return t.setAttribute("type",e),i&&(t.value=i),e}}}},removeAttr:function(t,e){var i,n=0,s=e&&e.match(U);if(s&&1===t.nodeType)for(;i=s[n++];)t.removeAttribute(i)}}),we={set:function(t,e,i){return!1===e?D.removeAttr(t,i):t.setAttribute(i,i),i}},D.each(D.expr.match.bool.source.match(/\w+/g),(function(t,e){var i=xe[e]||D.find.attr;xe[e]=function(t,e,n){var s,o,r=e.toLowerCase();return n||(o=xe[r],xe[r]=s,s=null!=i(t,e,n)?r:null,xe[r]=o),s}}));var Ce=/^(?:input|select|textarea|button)$/i,ke=/^(?:a|area)$/i;function De(t){return(t.match(U)||[]).join(" ")}function Te(t){return t.getAttribute&&t.getAttribute("class")||""}function Se(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(U)||[]}D.fn.extend({prop:function(t,e){return tt(this,D.prop,t,e,arguments.length>1)},removeProp:function(t){return this.each((function(){delete this[D.propFix[t]||t]}))}}),D.extend({prop:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&D.isXMLDoc(t)||(e=D.propFix[e]||e,s=D.propHooks[e]),void 0!==i?s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:t[e]=i:s&&"get"in s&&null!==(n=s.get(t,e))?n:t[e]},propHooks:{tabIndex:{get:function(t){var e=D.find.attr(t,"tabindex");return e?parseInt(e,10):Ce.test(t.nodeName)||ke.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||(D.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),D.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){D.propFix[this.toLowerCase()]=this})),D.fn.extend({addClass:function(t){var e,i,n,s,o,r;return v(t)?this.each((function(e){D(this).addClass(t.call(this,e,Te(this)))})):(e=Se(t)).length?this.each((function(){if(n=Te(this),i=1===this.nodeType&&" "+De(n)+" "){for(o=0;o<e.length;o++)s=e[o],i.indexOf(" "+s+" ")<0&&(i+=s+" ");r=De(i),n!==r&&this.setAttribute("class",r)}})):this},removeClass:function(t){var e,i,n,s,o,r;return v(t)?this.each((function(e){D(this).removeClass(t.call(this,e,Te(this)))})):arguments.length?(e=Se(t)).length?this.each((function(){if(n=Te(this),i=1===this.nodeType&&" "+De(n)+" "){for(o=0;o<e.length;o++)for(s=e[o];i.indexOf(" "+s+" ")>-1;)i=i.replace(" "+s+" "," ");r=De(i),n!==r&&this.setAttribute("class",r)}})):this:this.attr("class","")},toggleClass:function(t,e){var i,n,s,o,r=typeof t,a="string"===r||Array.isArray(t);return v(t)?this.each((function(i){D(this).toggleClass(t.call(this,i,Te(this),e),e)})):"boolean"==typeof e&&a?e?this.addClass(t):this.removeClass(t):(i=Se(t),this.each((function(){if(a)for(o=D(this),s=0;s<i.length;s++)n=i[s],o.hasClass(n)?o.removeClass(n):o.addClass(n);else void 0!==t&&"boolean"!==r||((n=Te(this))&&at.set(this,"__className__",n),this.setAttribute&&this.setAttribute("class",n||!1===t?"":at.get(this,"__className__")||""))})))},hasClass:function(t){var e,i,n=0;for(e=" "+t+" ";i=this[n++];)if(1===i.nodeType&&(" "+De(Te(i))+" ").indexOf(e)>-1)return!0;return!1}});var Ie=/\r/g;D.fn.extend({val:function(t){var e,i,n,s=this[0];return arguments.length?(n=v(t),this.each((function(i){var s;1===this.nodeType&&(null==(s=n?t.call(this,i,D(this).val()):t)?s="":"number"==typeof s?s+="":Array.isArray(s)&&(s=D.map(s,(function(t){return null==t?"":t+""}))),(e=D.valHooks[this.type]||D.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,s,"value")||(this.value=s))}))):s?(e=D.valHooks[s.type]||D.valHooks[s.nodeName.toLowerCase()])&&"get"in e&&void 0!==(i=e.get(s,"value"))?i:"string"==typeof(i=s.value)?i.replace(Ie,""):null==i?"":i:void 0}}),D.extend({valHooks:{option:{get:function(t){var e=D.find.attr(t,"value");return null!=e?e:De(D.text(t))}},select:{get:function(t){var e,i,n,s=t.options,o=t.selectedIndex,r="select-one"===t.type,a=r?null:[],l=r?o+1:s.length;for(n=o<0?l:r?o:0;n<l;n++)if(((i=s[n]).selected||n===o)&&!i.disabled&&(!i.parentNode.disabled||!S(i.parentNode,"optgroup"))){if(e=D(i).val(),r)return e;a.push(e)}return a},set:function(t,e){for(var i,n,s=t.options,o=D.makeArray(e),r=s.length;r--;)((n=s[r]).selected=D.inArray(D.valHooks.option.get(n),o)>-1)&&(i=!0);return i||(t.selectedIndex=-1),o}}}}),D.each(["radio","checkbox"],(function(){D.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=D.inArray(D(t).val(),e)>-1}},m.checkOn||(D.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}));var Pe=n.location,Ae={guid:Date.now()},Me=/\?/;D.parseXML=function(t){var e,i;if(!t||"string"!=typeof t)return null;try{e=(new n.DOMParser).parseFromString(t,"text/xml")}catch(t){}return i=e&&e.getElementsByTagName("parsererror")[0],e&&!i||D.error("Invalid XML: "+(i?D.map(i.childNodes,(function(t){return t.textContent})).join("\n"):t)),e};var Ee=/^(?:focusinfocus|focusoutblur)$/,Oe=function(t){t.stopPropagation()};D.extend(D.event,{trigger:function(t,e,i,s){var o,r,a,l,h,u,c,d,f=[i||b],g=p.call(t,"type")?t.type:t,m=p.call(t,"namespace")?t.namespace.split("."):[];if(r=d=a=i=i||b,3!==i.nodeType&&8!==i.nodeType&&!Ee.test(g+D.event.triggered)&&(g.indexOf(".")>-1&&(m=g.split("."),g=m.shift(),m.sort()),h=g.indexOf(":")<0&&"on"+g,(t=t[D.expando]?t:new D.Event(g,"object"==typeof t&&t)).isTrigger=s?2:3,t.namespace=m.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=i),e=null==e?[t]:D.makeArray(e,[t]),c=D.event.special[g]||{},s||!c.trigger||!1!==c.trigger.apply(i,e))){if(!s&&!c.noBubble&&!_(i)){for(l=c.delegateType||g,Ee.test(l+g)||(r=r.parentNode);r;r=r.parentNode)f.push(r),a=r;a===(i.ownerDocument||b)&&f.push(a.defaultView||a.parentWindow||n)}for(o=0;(r=f[o++])&&!t.isPropagationStopped();)d=r,t.type=o>1?l:c.bindType||g,(u=(at.get(r,"events")||Object.create(null))[t.type]&&at.get(r,"handle"))&&u.apply(r,e),(u=h&&r[h])&&u.apply&&ot(r)&&(t.result=u.apply(r,e),!1===t.result&&t.preventDefault());return t.type=g,s||t.isDefaultPrevented()||c._default&&!1!==c._default.apply(f.pop(),e)||!ot(i)||h&&v(i[g])&&!_(i)&&((a=i[h])&&(i[h]=null),D.event.triggered=g,t.isPropagationStopped()&&d.addEventListener(g,Oe),i[g](),t.isPropagationStopped()&&d.removeEventListener(g,Oe),D.event.triggered=void 0,a&&(i[h]=a)),t.result}},simulate:function(t,e,i){var n=D.extend(new D.Event,i,{type:t,isSimulated:!0});D.event.trigger(n,null,e)}}),D.fn.extend({trigger:function(t,e){return this.each((function(){D.event.trigger(t,e,this)}))},triggerHandler:function(t,e){var i=this[0];if(i)return D.event.trigger(t,e,i,!0)}});var He=/\[\]$/,Fe=/\r?\n/g,Ne=/^(?:submit|button|image|reset|file)$/i,ze=/^(?:input|select|textarea|keygen)/i;function We(t,e,i,n){var s;if(Array.isArray(e))D.each(e,(function(e,s){i||He.test(t)?n(t,s):We(t+"["+("object"==typeof s&&null!=s?e:"")+"]",s,i,n)}));else if(i||"object"!==x(e))n(t,e);else for(s in e)We(t+"["+s+"]",e[s],i,n)}D.param=function(t,e){var i,n=[],s=function(t,e){var i=v(e)?e():e;n[n.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==i?"":i)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!D.isPlainObject(t))D.each(t,(function(){s(this.name,this.value)}));else for(i in t)We(i,t[i],e,s);return n.join("&")},D.fn.extend({serialize:function(){return D.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var t=D.prop(this,"elements");return t?D.makeArray(t):this})).filter((function(){var t=this.type;return this.name&&!D(this).is(":disabled")&&ze.test(this.nodeName)&&!Ne.test(t)&&(this.checked||!Dt.test(t))})).map((function(t,e){var i=D(this).val();return null==i?null:Array.isArray(i)?D.map(i,(function(t){return{name:e.name,value:t.replace(Fe,"\r\n")}})):{name:e.name,value:i.replace(Fe,"\r\n")}})).get()}});var Re=/%20/g,Le=/#.*$/,je=/([?&])_=[^&]*/,qe=/^(.*?):[ \t]*([^\r\n]*)$/gm,Be=/^(?:GET|HEAD)$/,$e=/^\/\//,Ve={},Ye={},Ue="*/".concat("*"),Ke=b.createElement("a");function Xe(t){return function(e,i){"string"!=typeof e&&(i=e,e="*");var n,s=0,o=e.toLowerCase().match(U)||[];if(v(i))for(;n=o[s++];)"+"===n[0]?(n=n.slice(1)||"*",(t[n]=t[n]||[]).unshift(i)):(t[n]=t[n]||[]).push(i)}}function Ge(t,e,i,n){var s={},o=t===Ye;function r(a){var l;return s[a]=!0,D.each(t[a]||[],(function(t,a){var h=a(e,i,n);return"string"!=typeof h||o||s[h]?o?!(l=h):void 0:(e.dataTypes.unshift(h),r(h),!1)})),l}return r(e.dataTypes[0])||!s["*"]&&r("*")}function Je(t,e){var i,n,s=D.ajaxSettings.flatOptions||{};for(i in e)void 0!==e[i]&&((s[i]?t:n||(n={}))[i]=e[i]);return n&&D.extend(!0,t,n),t}Ke.href=Pe.href,D.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Pe.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Pe.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ue,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":D.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Je(Je(t,D.ajaxSettings),e):Je(D.ajaxSettings,t)},ajaxPrefilter:Xe(Ve),ajaxTransport:Xe(Ye),ajax:function(t,e){"object"==typeof t&&(e=t,t=void 0),e=e||{};var i,s,o,r,a,l,h,u,c,d,p=D.ajaxSetup({},e),f=p.context||p,g=p.context&&(f.nodeType||f.jquery)?D(f):D.event,m=D.Deferred(),v=D.Callbacks("once memory"),_=p.statusCode||{},y={},w={},x="canceled",C={readyState:0,getResponseHeader:function(t){var e;if(h){if(!r)for(r={};e=qe.exec(o);)r[e[1].toLowerCase()+" "]=(r[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=r[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return h?o:null},setRequestHeader:function(t,e){return null==h&&(t=w[t.toLowerCase()]=w[t.toLowerCase()]||t,y[t]=e),this},overrideMimeType:function(t){return null==h&&(p.mimeType=t),this},statusCode:function(t){var e;if(t)if(h)C.always(t[C.status]);else for(e in t)_[e]=[_[e],t[e]];return this},abort:function(t){var e=t||x;return i&&i.abort(e),k(0,e),this}};if(m.promise(C),p.url=((t||p.url||Pe.href)+"").replace($e,Pe.protocol+"//"),p.type=e.method||e.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(U)||[""],null==p.crossDomain){l=b.createElement("a");try{l.href=p.url,l.href=l.href,p.crossDomain=Ke.protocol+"//"+Ke.host!=l.protocol+"//"+l.host}catch(t){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=D.param(p.data,p.traditional)),Ge(Ve,p,e,C),h)return C;for(c in(u=D.event&&p.global)&&0==D.active++&&D.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Be.test(p.type),s=p.url.replace(Le,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace(Re,"+")):(d=p.url.slice(s.length),p.data&&(p.processData||"string"==typeof p.data)&&(s+=(Me.test(s)?"&":"?")+p.data,delete p.data),!1===p.cache&&(s=s.replace(je,"$1"),d=(Me.test(s)?"&":"?")+"_="+Ae.guid+++d),p.url=s+d),p.ifModified&&(D.lastModified[s]&&C.setRequestHeader("If-Modified-Since",D.lastModified[s]),D.etag[s]&&C.setRequestHeader("If-None-Match",D.etag[s])),(p.data&&p.hasContent&&!1!==p.contentType||e.contentType)&&C.setRequestHeader("Content-Type",p.contentType),C.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+Ue+"; q=0.01":""):p.accepts["*"]),p.headers)C.setRequestHeader(c,p.headers[c]);if(p.beforeSend&&(!1===p.beforeSend.call(f,C,p)||h))return C.abort();if(x="abort",v.add(p.complete),C.done(p.success),C.fail(p.error),i=Ge(Ye,p,e,C)){if(C.readyState=1,u&&g.trigger("ajaxSend",[C,p]),h)return C;p.async&&p.timeout>0&&(a=n.setTimeout((function(){C.abort("timeout")}),p.timeout));try{h=!1,i.send(y,k)}catch(t){if(h)throw t;k(-1,t)}}else k(-1,"No Transport");function k(t,e,r,l){var c,d,b,y,w,x=e;h||(h=!0,a&&n.clearTimeout(a),i=void 0,o=l||"",C.readyState=t>0?4:0,c=t>=200&&t<300||304===t,r&&(y=function(t,e,i){for(var n,s,o,r,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===n&&(n=t.mimeType||e.getResponseHeader("Content-Type"));if(n)for(s in a)if(a[s]&&a[s].test(n)){l.unshift(s);break}if(l[0]in i)o=l[0];else{for(s in i){if(!l[0]||t.converters[s+" "+l[0]]){o=s;break}r||(r=s)}o=o||r}if(o)return o!==l[0]&&l.unshift(o),i[o]}(p,C,r)),!c&&D.inArray("script",p.dataTypes)>-1&&D.inArray("json",p.dataTypes)<0&&(p.converters["text script"]=function(){}),y=function(t,e,i,n){var s,o,r,a,l,h={},u=t.dataTypes.slice();if(u[1])for(r in t.converters)h[r.toLowerCase()]=t.converters[r];for(o=u.shift();o;)if(t.responseFields[o]&&(i[t.responseFields[o]]=e),!l&&n&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(r=h[l+" "+o]||h["* "+o]))for(s in h)if((a=s.split(" "))[1]===o&&(r=h[l+" "+a[0]]||h["* "+a[0]])){!0===r?r=h[s]:!0!==h[s]&&(o=a[0],u.unshift(a[1]));break}if(!0!==r)if(r&&t.throws)e=r(e);else try{e=r(e)}catch(t){return{state:"parsererror",error:r?t:"No conversion from "+l+" to "+o}}}return{state:"success",data:e}}(p,y,C,c),c?(p.ifModified&&((w=C.getResponseHeader("Last-Modified"))&&(D.lastModified[s]=w),(w=C.getResponseHeader("etag"))&&(D.etag[s]=w)),204===t||"HEAD"===p.type?x="nocontent":304===t?x="notmodified":(x=y.state,d=y.data,c=!(b=y.error))):(b=x,!t&&x||(x="error",t<0&&(t=0))),C.status=t,C.statusText=(e||x)+"",c?m.resolveWith(f,[d,x,C]):m.rejectWith(f,[C,x,b]),C.statusCode(_),_=void 0,u&&g.trigger(c?"ajaxSuccess":"ajaxError",[C,p,c?d:b]),v.fireWith(f,[C,x]),u&&(g.trigger("ajaxComplete",[C,p]),--D.active||D.event.trigger("ajaxStop")))}return C},getJSON:function(t,e,i){return D.get(t,e,i,"json")},getScript:function(t,e){return D.get(t,void 0,e,"script")}}),D.each(["get","post"],(function(t,e){D[e]=function(t,i,n,s){return v(i)&&(s=s||n,n=i,i=void 0),D.ajax(D.extend({url:t,type:e,dataType:s,data:i,success:n},D.isPlainObject(t)&&t))}})),D.ajaxPrefilter((function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")})),D._evalUrl=function(t,e,i){return D.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){D.globalEval(t,e,i)}})},D.fn.extend({wrapAll:function(t){var e;return this[0]&&(v(t)&&(t=t.call(this[0])),e=D(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map((function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t})).append(this)),this},wrapInner:function(t){return v(t)?this.each((function(e){D(this).wrapInner(t.call(this,e))})):this.each((function(){var e=D(this),i=e.contents();i.length?i.wrapAll(t):e.append(t)}))},wrap:function(t){var e=v(t);return this.each((function(i){D(this).wrapAll(e?t.call(this,i):t)}))},unwrap:function(t){return this.parent(t).not("body").each((function(){D(this).replaceWith(this.childNodes)})),this}}),D.expr.pseudos.hidden=function(t){return!D.expr.pseudos.visible(t)},D.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},D.ajaxSettings.xhr=function(){try{return new n.XMLHttpRequest}catch(t){}};var Ze={0:200,1223:204},Qe=D.ajaxSettings.xhr();m.cors=!!Qe&&"withCredentials"in Qe,m.ajax=Qe=!!Qe,D.ajaxTransport((function(t){var e,i;if(m.cors||Qe&&!t.crossDomain)return{send:function(s,o){var r,a=t.xhr();if(a.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(r in t.xhrFields)a[r]=t.xhrFields[r];for(r in t.mimeType&&a.overrideMimeType&&a.overrideMimeType(t.mimeType),t.crossDomain||s["X-Requested-With"]||(s["X-Requested-With"]="XMLHttpRequest"),s)a.setRequestHeader(r,s[r]);e=function(t){return function(){e&&(e=i=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===t?a.abort():"error"===t?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Ze[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=e(),i=a.onerror=a.ontimeout=e("error"),void 0!==a.onabort?a.onabort=i:a.onreadystatechange=function(){4===a.readyState&&n.setTimeout((function(){e&&i()}))},e=e("abort");try{a.send(t.hasContent&&t.data||null)}catch(t){if(e)throw t}},abort:function(){e&&e()}}})),D.ajaxPrefilter((function(t){t.crossDomain&&(t.contents.script=!1)})),D.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return D.globalEval(t),t}}}),D.ajaxPrefilter("script",(function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")})),D.ajaxTransport("script",(function(t){var e,i;if(t.crossDomain||t.scriptAttrs)return{send:function(n,s){e=D("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",i=function(t){e.remove(),i=null,t&&s("error"===t.type?404:200,t.type)}),b.head.appendChild(e[0])},abort:function(){i&&i()}}}));var ti,ei=[],ii=/(=)\?(?=&|$)|\?\?/;D.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=ei.pop()||D.expando+"_"+Ae.guid++;return this[t]=!0,t}}),D.ajaxPrefilter("json jsonp",(function(t,e,i){var s,o,r,a=!1!==t.jsonp&&(ii.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&ii.test(t.data)&&"data");if(a||"jsonp"===t.dataTypes[0])return s=t.jsonpCallback=v(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,a?t[a]=t[a].replace(ii,"$1"+s):!1!==t.jsonp&&(t.url+=(Me.test(t.url)?"&":"?")+t.jsonp+"="+s),t.converters["script json"]=function(){return r||D.error(s+" was not called"),r[0]},t.dataTypes[0]="json",o=n[s],n[s]=function(){r=arguments},i.always((function(){void 0===o?D(n).removeProp(s):n[s]=o,t[s]&&(t.jsonpCallback=e.jsonpCallback,ei.push(s)),r&&v(o)&&o(r[0]),r=o=void 0})),"script"})),m.createHTMLDocument=((ti=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===ti.childNodes.length),D.parseHTML=function(t,e,i){return"string"!=typeof t?[]:("boolean"==typeof e&&(i=e,e=!1),e||(m.createHTMLDocument?((n=(e=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,e.head.appendChild(n)):e=b),o=!i&&[],(s=L.exec(t))?[e.createElement(s[1])]:(s=Et([t],e,o),o&&o.length&&D(o).remove(),D.merge([],s.childNodes)));var n,s,o},D.fn.load=function(t,e,i){var n,s,o,r=this,a=t.indexOf(" ");return a>-1&&(n=De(t.slice(a)),t=t.slice(0,a)),v(e)?(i=e,e=void 0):e&&"object"==typeof e&&(s="POST"),r.length>0&&D.ajax({url:t,type:s||"GET",dataType:"html",data:e}).done((function(t){o=arguments,r.html(n?D("<div>").append(D.parseHTML(t)).find(n):t)})).always(i&&function(t,e){r.each((function(){i.apply(this,o||[t.responseText,e,t])}))}),this},D.expr.pseudos.animated=function(t){return D.grep(D.timers,(function(e){return t===e.elem})).length},D.offset={setOffset:function(t,e,i){var n,s,o,r,a,l,h=D.css(t,"position"),u=D(t),c={};"static"===h&&(t.style.position="relative"),a=u.offset(),o=D.css(t,"top"),l=D.css(t,"left"),("absolute"===h||"fixed"===h)&&(o+l).indexOf("auto")>-1?(r=(n=u.position()).top,s=n.left):(r=parseFloat(o)||0,s=parseFloat(l)||0),v(e)&&(e=e.call(t,i,D.extend({},a))),null!=e.top&&(c.top=e.top-a.top+r),null!=e.left&&(c.left=e.left-a.left+s),"using"in e?e.using.call(t,c):u.css(c)}},D.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each((function(e){D.offset.setOffset(this,t,e)}));var e,i,n=this[0];return n?n.getClientRects().length?(e=n.getBoundingClientRect(),i=n.ownerDocument.defaultView,{top:e.top+i.pageYOffset,left:e.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,i,n=this[0],s={top:0,left:0};if("fixed"===D.css(n,"position"))e=n.getBoundingClientRect();else{for(e=this.offset(),i=n.ownerDocument,t=n.offsetParent||i.documentElement;t&&(t===i.body||t===i.documentElement)&&"static"===D.css(t,"position");)t=t.parentNode;t&&t!==n&&1===t.nodeType&&((s=D(t).offset()).top+=D.css(t,"borderTopWidth",!0),s.left+=D.css(t,"borderLeftWidth",!0))}return{top:e.top-s.top-D.css(n,"marginTop",!0),left:e.left-s.left-D.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var t=this.offsetParent;t&&"static"===D.css(t,"position");)t=t.offsetParent;return t||gt}))}}),D.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(t,e){var i="pageYOffset"===e;D.fn[t]=function(n){return tt(this,(function(t,n,s){var o;if(_(t)?o=t:9===t.nodeType&&(o=t.defaultView),void 0===s)return o?o[e]:t[n];o?o.scrollTo(i?o.pageXOffset:s,i?s:o.pageYOffset):t[n]=s}),t,n,arguments.length)}})),D.each(["top","left"],(function(t,e){D.cssHooks[e]=te(m.pixelPosition,(function(t,i){if(i)return i=Qt(t,e),Kt.test(i)?D(t).position()[e]+"px":i}))})),D.each({Height:"height",Width:"width"},(function(t,e){D.each({padding:"inner"+t,content:e,"":"outer"+t},(function(i,n){D.fn[n]=function(s,o){var r=arguments.length&&(i||"boolean"!=typeof s),a=i||(!0===s||!0===o?"margin":"border");return tt(this,(function(e,i,s){var o;return _(e)?0===n.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(o=e.documentElement,Math.max(e.body["scroll"+t],o["scroll"+t],e.body["offset"+t],o["offset"+t],o["client"+t])):void 0===s?D.css(e,i,a):D.style(e,i,s,a)}),e,r?s:void 0,r)}}))})),D.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(t,e){D.fn[e]=function(t){return this.on(e,t)}})),D.fn.extend({bind:function(t,e,i){return this.on(t,null,e,i)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,i,n){return this.on(e,t,i,n)},undelegate:function(t,e,i){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",i)},hover:function(t,e){return this.on("mouseenter",t).on("mouseleave",e||t)}}),D.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(t,e){D.fn[e]=function(t,i){return arguments.length>0?this.on(e,null,t,i):this.trigger(e)}}));var ni=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;D.proxy=function(t,e){var i,n,s;if("string"==typeof e&&(i=t[e],e=t,t=i),v(t))return n=a.call(arguments,2),s=function(){return t.apply(e||this,n.concat(a.call(arguments)))},s.guid=t.guid=t.guid||D.guid++,s},D.holdReady=function(t){t?D.readyWait++:D.ready(!0)},D.isArray=Array.isArray,D.parseJSON=JSON.parse,D.nodeName=S,D.isFunction=v,D.isWindow=_,D.camelCase=st,D.type=x,D.now=Date.now,D.isNumeric=function(t){var e=D.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},D.trim=function(t){return null==t?"":(t+"").replace(ni,"$1")},void 0===(i=function(){return D}.apply(e,[]))||(t.exports=i);var si=n.jQuery,oi=n.$;return D.noConflict=function(t){return n.$===D&&(n.$=oi),t&&n.jQuery===D&&(n.jQuery=si),D},void 0===s&&(n.jQuery=n.$=D),D}))},735:()=>{},910:(t,e,i)=>{var n,s,o;!function(){"use strict";s=[i(692)],n=function(t){t.ui=t.ui||{};t.ui.version="1.14.1";var e=0,i=Array.prototype.hasOwnProperty,n=Array.prototype.slice;t.cleanData=(s=t.cleanData,function(e){var i,n,o;for(o=0;null!=(n=e[o]);o++)(i=t._data(n,"events"))&&i.remove&&t(n).triggerHandler("remove");s(e)}),t.widget=function(e,i,n){var s,o,r,a={},l=e.split(".")[0];if("__proto__"===(e=e.split(".")[1])||"constructor"===e)return t.error("Invalid widget name: "+e);var h=l+"-"+e;return n||(n=i,i=t.Widget),Array.isArray(n)&&(n=t.extend.apply(null,[{}].concat(n))),t.expr.pseudos[h.toLowerCase()]=function(e){return!!t.data(e,h)},t[l]=t[l]||{},s=t[l][e],o=t[l][e]=function(t,e){if(!this||!this._createWidget)return new o(t,e);arguments.length&&this._createWidget(t,e)},t.extend(o,s,{version:n.version,_proto:t.extend({},n),_childConstructors:[]}),(r=new i).options=t.widget.extend({},r.options),t.each(n,(function(t,e){a[t]="function"==typeof e?function(){function n(){return i.prototype[t].apply(this,arguments)}function s(e){return i.prototype[t].apply(this,e)}return function(){var t,i=this._super,o=this._superApply;return this._super=n,this._superApply=s,t=e.apply(this,arguments),this._super=i,this._superApply=o,t}}():e})),o.prototype=t.widget.extend(r,{widgetEventPrefix:s&&r.widgetEventPrefix||e},a,{constructor:o,namespace:l,widgetName:e,widgetFullName:h}),s?(t.each(s._childConstructors,(function(e,i){var n=i.prototype;t.widget(n.namespace+"."+n.widgetName,o,i._proto)})),delete s._childConstructors):i._childConstructors.push(o),t.widget.bridge(e,o),o},t.widget.extend=function(e){for(var s,o,r=n.call(arguments,1),a=0,l=r.length;a<l;a++)for(s in r[a])o=r[a][s],i.call(r[a],s)&&void 0!==o&&(t.isPlainObject(o)?e[s]=t.isPlainObject(e[s])?t.widget.extend({},e[s],o):t.widget.extend({},o):e[s]=o);return e},t.widget.bridge=function(e,i){var s=i.prototype.widgetFullName||e;t.fn[e]=function(o){var r="string"==typeof o,a=n.call(arguments,1),l=this;return r?this.length||"instance"!==o?this.each((function(){var i,n=t.data(this,s);return"instance"===o?(l=n,!1):n?"function"!=typeof n[o]||"_"===o.charAt(0)?t.error("no such method '"+o+"' for "+e+" widget instance"):(i=n[o].apply(n,a))!==n&&void 0!==i?(l=i&&i.jquery?l.pushStack(i.get()):i,!1):void 0:t.error("cannot call methods on "+e+" prior to initialization; attempted to call method '"+o+"'")})):l=void 0:(a.length&&(o=t.widget.extend.apply(null,[o].concat(a))),this.each((function(){var e=t.data(this,s);e?(e.option(o||{}),e._init&&e._init()):t.data(this,s,new i(o,this))}))),l}},t.Widget=function(){},t.Widget._childConstructors=[],t.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{classes:{},disabled:!1,create:null},_createWidget:function(i,n){n=t(n||this.defaultElement||this)[0],this.element=t(n),this.uuid=e++,this.eventNamespace="."+this.widgetName+this.uuid,this.bindings=t(),this.hoverable=t(),this.focusable=t(),this.classesElementLookup={},n!==this&&(t.data(n,this.widgetFullName,this),this._on(!0,this.element,{remove:function(t){t.target===n&&this.destroy()}}),this.document=t(n.style?n.ownerDocument:n.document||n),this.window=t(this.document[0].defaultView||this.document[0].parentWindow)),this.options=t.widget.extend({},this.options,this._getCreateOptions(),i),this._create(),this.options.disabled&&this._setOptionDisabled(this.options.disabled),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:function(){return{}},_getCreateEventData:t.noop,_create:t.noop,_init:t.noop,destroy:function(){var e=this;this._destroy(),t.each(this.classesElementLookup,(function(t,i){e._removeClass(i,t)})),this.element.off(this.eventNamespace).removeData(this.widgetFullName),this.widget().off(this.eventNamespace).removeAttr("aria-disabled"),this.bindings.off(this.eventNamespace)},_destroy:t.noop,widget:function(){return this.element},option:function(e,i){var n,s,o,r=e;if(0===arguments.length)return t.widget.extend({},this.options);if("string"==typeof e)if(r={},n=e.split("."),e=n.shift(),n.length){for(s=r[e]=t.widget.extend({},this.options[e]),o=0;o<n.length-1;o++)s[n[o]]=s[n[o]]||{},s=s[n[o]];if(e=n.pop(),1===arguments.length)return void 0===s[e]?null:s[e];s[e]=i}else{if(1===arguments.length)return void 0===this.options[e]?null:this.options[e];r[e]=i}return this._setOptions(r),this},_setOptions:function(t){var e;for(e in t)this._setOption(e,t[e]);return this},_setOption:function(t,e){return"classes"===t&&this._setOptionClasses(e),this.options[t]=e,"disabled"===t&&this._setOptionDisabled(e),this},_setOptionClasses:function(e){var i,n,s;for(i in e)s=this.classesElementLookup[i],e[i]!==this.options.classes[i]&&s&&s.length&&(n=t(s.get()),this._removeClass(s,i),n.addClass(this._classes({element:n,keys:i,classes:e,add:!0})))},_setOptionDisabled:function(t){this._toggleClass(this.widget(),this.widgetFullName+"-disabled",null,!!t),t&&(this._removeClass(this.hoverable,null,"ui-state-hover"),this._removeClass(this.focusable,null,"ui-state-focus"))},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_classes:function(e){var i=[],n=this;function s(){var i=[];e.element.each((function(e,s){t.map(n.classesElementLookup,(function(t){return t})).some((function(t){return t.is(s)}))||i.push(s)})),n._on(t(i),{remove:"_untrackClassesElement"})}function o(o,r){var a,l;for(l=0;l<o.length;l++)a=n.classesElementLookup[o[l]]||t(),e.add?(s(),a=t(t.uniqueSort(a.get().concat(e.element.get())))):a=t(a.not(e.element).get()),n.classesElementLookup[o[l]]=a,i.push(o[l]),r&&e.classes[o[l]]&&i.push(e.classes[o[l]])}return(e=t.extend({element:this.element,classes:this.options.classes||{}},e)).keys&&o(e.keys.match(/\S+/g)||[],!0),e.extra&&o(e.extra.match(/\S+/g)||[]),i.join(" ")},_untrackClassesElement:function(e){var i=this;t.each(i.classesElementLookup,(function(n,s){-1!==t.inArray(e.target,s)&&(i.classesElementLookup[n]=t(s.not(e.target).get()))})),this._off(t(e.target))},_removeClass:function(t,e,i){return this._toggleClass(t,e,i,!1)},_addClass:function(t,e,i){return this._toggleClass(t,e,i,!0)},_toggleClass:function(t,e,i,n){n="boolean"==typeof n?n:i;var s="string"==typeof t||null===t,o={extra:s?e:i,keys:s?t:e,element:s?this.element:t,add:n};return o.element.toggleClass(this._classes(o),n),this},_on:function(e,i,n){var s,o=this;"boolean"!=typeof e&&(n=i,i=e,e=!1),n?(i=s=t(i),this.bindings=this.bindings.add(i)):(n=i,i=this.element,s=this.widget()),t.each(n,(function(n,r){function a(){if(e||!0!==o.options.disabled&&!t(this).hasClass("ui-state-disabled"))return("string"==typeof r?o[r]:r).apply(o,arguments)}"string"!=typeof r&&(a.guid=r.guid=r.guid||a.guid||t.guid++);var l=n.match(/^([\w:-]*)\s*(.*)$/),h=l[1]+o.eventNamespace,u=l[2];u?s.on(h,u,a):i.on(h,a)}))},_off:function(e,i){i=(i||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,e.off(i),this.bindings=t(this.bindings.not(e).get()),this.focusable=t(this.focusable.not(e).get()),this.hoverable=t(this.hoverable.not(e).get())},_delay:function(t,e){function i(){return("string"==typeof t?n[t]:t).apply(n,arguments)}var n=this;return setTimeout(i,e||0)},_hoverable:function(e){this.hoverable=this.hoverable.add(e),this._on(e,{mouseenter:function(e){this._addClass(t(e.currentTarget),null,"ui-state-hover")},mouseleave:function(e){this._removeClass(t(e.currentTarget),null,"ui-state-hover")}})},_focusable:function(e){this.focusable=this.focusable.add(e),this._on(e,{focusin:function(e){this._addClass(t(e.currentTarget),null,"ui-state-focus")},focusout:function(e){this._removeClass(t(e.currentTarget),null,"ui-state-focus")}})},_trigger:function(e,i,n){var s,o,r=this.options[e];if(n=n||{},(i=t.Event(i)).type=(e===this.widgetEventPrefix?e:this.widgetEventPrefix+e).toLowerCase(),i.target=this.element[0],o=i.originalEvent)for(s in o)s in i||(i[s]=o[s]);return this.element.trigger(i,n),!("function"==typeof r&&!1===r.apply(this.element[0],[i].concat(n))||i.isDefaultPrevented())}},t.each({show:"fadeIn",hide:"fadeOut"},(function(e,i){t.Widget.prototype["_"+e]=function(n,s,o){var r;"string"==typeof s&&(s={effect:s});var a=s?!0===s||"number"==typeof s?i:s.effect||i:e;"number"==typeof(s=s||{})?s={duration:s}:!0===s&&(s={}),r=!t.isEmptyObject(s),s.complete=o,s.delay&&n.delay(s.delay),r&&t.effects&&t.effects.effect[a]?n[e](s):a!==e&&n[a]?n[a](s.duration,s.easing,o):n.queue((function(i){t(this)[e](),o&&o.call(n[0]),i()}))}}));var s;t.widget;!function(){var e,i=Math.max,n=Math.abs,s=/left|center|right/,o=/top|center|bottom/,r=/[\+\-]\d+(\.[\d]+)?%?/,a=/^\w+/,l=/%$/,h=t.fn.position;function u(t,e,i){return[parseFloat(t[0])*(l.test(t[0])?e/100:1),parseFloat(t[1])*(l.test(t[1])?i/100:1)]}function c(e,i){return parseInt(t.css(e,i),10)||0}function d(t){return null!=t&&t===t.window}function p(t){var e=t[0];return 9===e.nodeType?{width:t.width(),height:t.height(),offset:{top:0,left:0}}:d(e)?{width:t.width(),height:t.height(),offset:{top:t.scrollTop(),left:t.scrollLeft()}}:e.preventDefault?{width:0,height:0,offset:{top:e.pageY,left:e.pageX}}:{width:t.outerWidth(),height:t.outerHeight(),offset:t.offset()}}t.position={scrollbarWidth:function(){if(void 0!==e)return e;var i,n,s=t("<div style='display:block;position:absolute;width:200px;height:200px;overflow:hidden;'><div style='height:300px;width:auto;'></div></div>"),o=s.children()[0];return t("body").append(s),i=o.offsetWidth,s.css("overflow","scroll"),i===(n=o.offsetWidth)&&(n=s[0].clientWidth),s.remove(),e=i-n},getScrollInfo:function(e){var i=e.isWindow||e.isDocument?"":e.element.css("overflow-x"),n=e.isWindow||e.isDocument?"":e.element.css("overflow-y"),s="scroll"===i||"auto"===i&&e.width<e.element[0].scrollWidth;return{width:"scroll"===n||"auto"===n&&e.height<e.element[0].scrollHeight?t.position.scrollbarWidth():0,height:s?t.position.scrollbarWidth():0}},getWithinInfo:function(e){var i=t(e||window),n=d(i[0]),s=!!i[0]&&9===i[0].nodeType;return{element:i,isWindow:n,isDocument:s,offset:!n&&!s?t(e).offset():{left:0,top:0},scrollLeft:i.scrollLeft(),scrollTop:i.scrollTop(),width:i.outerWidth(),height:i.outerHeight()}}},t.fn.position=function(e){if(!e||!e.of)return h.apply(this,arguments);var l,d,f,g,m,v,_="string"==typeof(e=t.extend({},e)).of?t(document).find(e.of):t(e.of),b=t.position.getWithinInfo(e.within),y=t.position.getScrollInfo(b),w=(e.collision||"flip").split(" "),x={};return v=p(_),_[0].preventDefault&&(e.at="left top"),d=v.width,f=v.height,g=v.offset,m=t.extend({},g),t.each(["my","at"],(function(){var t,i,n=(e[this]||"").split(" ");1===n.length&&(n=s.test(n[0])?n.concat(["center"]):o.test(n[0])?["center"].concat(n):["center","center"]),n[0]=s.test(n[0])?n[0]:"center",n[1]=o.test(n[1])?n[1]:"center",t=r.exec(n[0]),i=r.exec(n[1]),x[this]=[t?t[0]:0,i?i[0]:0],e[this]=[a.exec(n[0])[0],a.exec(n[1])[0]]})),1===w.length&&(w[1]=w[0]),"right"===e.at[0]?m.left+=d:"center"===e.at[0]&&(m.left+=d/2),"bottom"===e.at[1]?m.top+=f:"center"===e.at[1]&&(m.top+=f/2),l=u(x.at,d,f),m.left+=l[0],m.top+=l[1],this.each((function(){var s,o,r=t(this),a=r.outerWidth(),h=r.outerHeight(),p=c(this,"marginLeft"),v=c(this,"marginTop"),C=a+p+c(this,"marginRight")+y.width,k=h+v+c(this,"marginBottom")+y.height,D=t.extend({},m),T=u(x.my,r.outerWidth(),r.outerHeight());"right"===e.my[0]?D.left-=a:"center"===e.my[0]&&(D.left-=a/2),"bottom"===e.my[1]?D.top-=h:"center"===e.my[1]&&(D.top-=h/2),D.left+=T[0],D.top+=T[1],s={marginLeft:p,marginTop:v},t.each(["left","top"],(function(i,n){t.ui.position[w[i]]&&t.ui.position[w[i]][n](D,{targetWidth:d,targetHeight:f,elemWidth:a,elemHeight:h,collisionPosition:s,collisionWidth:C,collisionHeight:k,offset:[l[0]+T[0],l[1]+T[1]],my:e.my,at:e.at,within:b,elem:r})})),e.using&&(o=function(t){var s=g.left-D.left,o=s+d-a,l=g.top-D.top,u=l+f-h,c={target:{element:_,left:g.left,top:g.top,width:d,height:f},element:{element:r,left:D.left,top:D.top,width:a,height:h},horizontal:o<0?"left":s>0?"right":"center",vertical:u<0?"top":l>0?"bottom":"middle"};d<a&&n(s+o)<d&&(c.horizontal="center"),f<h&&n(l+u)<f&&(c.vertical="middle"),i(n(s),n(o))>i(n(l),n(u))?c.important="horizontal":c.important="vertical",e.using.call(this,t,c)}),r.offset(t.extend(D,{using:o}))}))},t.ui.position={fit:{left:function(t,e){var n,s=e.within,o=s.isWindow?s.scrollLeft:s.offset.left,r=s.width,a=t.left-e.collisionPosition.marginLeft,l=o-a,h=a+e.collisionWidth-r-o;e.collisionWidth>r?l>0&&h<=0?(n=t.left+l+e.collisionWidth-r-o,t.left+=l-n):t.left=h>0&&l<=0?o:l>h?o+r-e.collisionWidth:o:l>0?t.left+=l:h>0?t.left-=h:t.left=i(t.left-a,t.left)},top:function(t,e){var n,s=e.within,o=s.isWindow?s.scrollTop:s.offset.top,r=e.within.height,a=t.top-e.collisionPosition.marginTop,l=o-a,h=a+e.collisionHeight-r-o;e.collisionHeight>r?l>0&&h<=0?(n=t.top+l+e.collisionHeight-r-o,t.top+=l-n):t.top=h>0&&l<=0?o:l>h?o+r-e.collisionHeight:o:l>0?t.top+=l:h>0?t.top-=h:t.top=i(t.top-a,t.top)}},flip:{left:function(t,e){var i,s,o=e.within,r=o.offset.left+o.scrollLeft,a=o.width,l=o.isWindow?o.scrollLeft:o.offset.left,h=t.left-e.collisionPosition.marginLeft,u=h-l,c=h+e.collisionWidth-a-l,d="left"===e.my[0]?-e.elemWidth:"right"===e.my[0]?e.elemWidth:0,p="left"===e.at[0]?e.targetWidth:"right"===e.at[0]?-e.targetWidth:0,f=-2*e.offset[0];u<0?((i=t.left+d+p+f+e.collisionWidth-a-r)<0||i<n(u))&&(t.left+=d+p+f):c>0&&((s=t.left-e.collisionPosition.marginLeft+d+p+f-l)>0||n(s)<c)&&(t.left+=d+p+f)},top:function(t,e){var i,s,o=e.within,r=o.offset.top+o.scrollTop,a=o.height,l=o.isWindow?o.scrollTop:o.offset.top,h=t.top-e.collisionPosition.marginTop,u=h-l,c=h+e.collisionHeight-a-l,d="top"===e.my[1]?-e.elemHeight:"bottom"===e.my[1]?e.elemHeight:0,p="top"===e.at[1]?e.targetHeight:"bottom"===e.at[1]?-e.targetHeight:0,f=-2*e.offset[1];u<0?((s=t.top+d+p+f+e.collisionHeight-a-r)<0||s<n(u))&&(t.top+=d+p+f):c>0&&((i=t.top-e.collisionPosition.marginTop+d+p+f-l)>0||n(i)<c)&&(t.top+=d+p+f)}},flipfit:{left:function(){t.ui.position.flip.left.apply(this,arguments),t.ui.position.fit.left.apply(this,arguments)},top:function(){t.ui.position.flip.top.apply(this,arguments),t.ui.position.fit.top.apply(this,arguments)}}}}();t.ui.position,t.extend(t.expr.pseudos,{data:t.expr.createPseudo((function(e){return function(i){return!!t.data(i,e)}}))}),t.fn.extend({disableSelection:(m="onselectstart"in document.createElement("div")?"selectstart":"mousedown",function(){return this.on(m+".ui-disableSelection",(function(t){t.preventDefault()}))}),enableSelection:function(){return this.off(".ui-disableSelection")}});var o,r=t,a="backgroundColor borderBottomColor borderLeftColor borderRightColor borderTopColor color columnRuleColor outlineColor textDecorationColor textEmphasisColor",l={},h=l.toString,u=/^([\-+])=\s*(\d+\.?\d*)/,c=[{re:/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[t[1],t[2],t[3],t[4]]}},{re:/rgba?\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[2.55*t[1],2.55*t[2],2.55*t[3],t[4]]}},{re:/#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?/,parse:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16),t[4]?(parseInt(t[4],16)/255).toFixed(2):1]}},{re:/#([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?/,parse:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16),t[4]?(parseInt(t[4]+t[4],16)/255).toFixed(2):1]}},{re:/hsla?\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,space:"hsla",parse:function(t){return[t[1],t[2]/100,t[3]/100,t[4]]}}],d=r.Color=function(t,e,i,n){return new r.Color.fn.parse(t,e,i,n)},p={rgba:{props:{red:{idx:0,type:"byte"},green:{idx:1,type:"byte"},blue:{idx:2,type:"byte"}}},hsla:{props:{hue:{idx:0,type:"degrees"},saturation:{idx:1,type:"percent"},lightness:{idx:2,type:"percent"}}}},f={byte:{floor:!0,max:255},percent:{max:1},degrees:{mod:360,floor:!0}},g=r.each;var m;function v(t){return null==t?t+"":"object"==typeof t?l[h.call(t)]||"object":typeof t}function _(t,e,i){var n=f[e.type]||{};return null==t?i||!e.def?null:e.def:(t=n.floor?~~t:parseFloat(t),n.mod?(t+n.mod)%n.mod:Math.min(n.max,Math.max(0,t)))}function b(t){var e=d(),i=e._rgba=[];return t=t.toLowerCase(),g(c,(function(n,s){var o,r=s.re.exec(t),a=r&&s.parse(r),l=s.space||"rgba";if(a)return o=e[l](a),e[p[l].cache]=o[p[l].cache],i=e._rgba=o._rgba,!1})),i.length?("0,0,0,0"===i.join()&&r.extend(i,o.transparent),e):o[t]}function y(t,e,i){return 6*(i=(i+1)%1)<1?t+(e-t)*i*6:2*i<1?e:3*i<2?t+(e-t)*(2/3-i)*6:t}g(p,(function(t,e){e.cache="_"+t,e.props.alpha={idx:3,type:"percent",def:1}})),r.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(t,e){l["[object "+e+"]"]=e.toLowerCase()})),d.fn=r.extend(d.prototype,{parse:function(t,e,i,n){if(void 0===t)return this._rgba=[null,null,null,null],this;(t.jquery||t.nodeType)&&(t=r(t).css(e),e=void 0);var s=this,a=v(t),l=this._rgba=[];return void 0!==e&&(t=[t,e,i,n],a="array"),"string"===a?this.parse(b(t)||o._default):"array"===a?(g(p.rgba.props,(function(e,i){l[i.idx]=_(t[i.idx],i)})),this):"object"===a?(g(p,t instanceof d?function(e,i){t[i.cache]&&(s[i.cache]=t[i.cache].slice())}:function(e,i){var n=i.cache;g(i.props,(function(e,o){if(!s[n]&&i.to){if("alpha"===e||null==t[e])return;s[n]=i.to(s._rgba)}s[n][o.idx]=_(t[e],o,!0)})),s[n]&&r.inArray(null,s[n].slice(0,3))<0&&(null==s[n][3]&&(s[n][3]=1),i.from&&(s._rgba=i.from(s[n])))}),this):void 0},is:function(t){var e=d(t),i=!0,n=this;return g(p,(function(t,s){var o,r=e[s.cache];return r&&(o=n[s.cache]||s.to&&s.to(n._rgba)||[],g(s.props,(function(t,e){if(null!=r[e.idx])return i=r[e.idx]===o[e.idx]}))),i})),i},_space:function(){var t=[],e=this;return g(p,(function(i,n){e[n.cache]&&t.push(i)})),t.pop()},transition:function(t,e){var i=d(t),n=i._space(),s=p[n],o=0===this.alpha()?d("transparent"):this,r=o[s.cache]||s.to(o._rgba),a=r.slice();return i=i[s.cache],g(s.props,(function(t,n){var s=n.idx,o=r[s],l=i[s],h=f[n.type]||{};null!==l&&(null===o?a[s]=l:(h.mod&&(l-o>h.mod/2?o+=h.mod:o-l>h.mod/2&&(o-=h.mod)),a[s]=_((l-o)*e+o,n)))})),this[n](a)},blend:function(t){if(1===this._rgba[3])return this;var e=this._rgba.slice(),i=e.pop(),n=d(t)._rgba;return d(r.map(e,(function(t,e){return(1-i)*n[e]+i*t})))},toRgbaString:function(){var t="rgba(",e=r.map(this._rgba,(function(t,e){return null!=t?t:e>2?1:0}));return 1===e[3]&&(e.pop(),t="rgb("),t+e.join(", ")+")"},toHslaString:function(){var t="hsla(",e=r.map(this.hsla(),(function(t,e){return null==t&&(t=e>2?1:0),e&&e<3&&(t=Math.round(100*t)+"%"),t}));return 1===e[3]&&(e.pop(),t="hsl("),t+e.join(", ")+")"},toHexString:function(t){var e=this._rgba.slice(),i=e.pop();return t&&e.push(~~(255*i)),"#"+r.map(e,(function(t){return("0"+(t||0).toString(16)).substr(-2)})).join("")},toString:function(){return this.toRgbaString()}}),d.fn.parse.prototype=d.fn,p.hsla.to=function(t){if(null==t[0]||null==t[1]||null==t[2])return[null,null,null,t[3]];var e,i,n=t[0]/255,s=t[1]/255,o=t[2]/255,r=t[3],a=Math.max(n,s,o),l=Math.min(n,s,o),h=a-l,u=a+l,c=.5*u;return e=l===a?0:n===a?60*(s-o)/h+360:s===a?60*(o-n)/h+120:60*(n-s)/h+240,i=0===h?0:c<=.5?h/u:h/(2-u),[Math.round(e)%360,i,c,null==r?1:r]},p.hsla.from=function(t){if(null==t[0]||null==t[1]||null==t[2])return[null,null,null,t[3]];var e=t[0]/360,i=t[1],n=t[2],s=t[3],o=n<=.5?n*(1+i):n+i-n*i,r=2*n-o;return[Math.round(255*y(r,o,e+1/3)),Math.round(255*y(r,o,e)),Math.round(255*y(r,o,e-1/3)),s]},g(p,(function(t,e){var i=e.props,n=e.cache,s=e.to,o=e.from;d.fn[t]=function(t){if(s&&!this[n]&&(this[n]=s(this._rgba)),void 0===t)return this[n].slice();var e,r=v(t),a="array"===r||"object"===r?t:arguments,l=this[n].slice();return g(i,(function(t,e){var i=a["object"===r?t:e.idx];null==i&&(i=l[e.idx]),l[e.idx]=_(i,e)})),o?((e=d(o(l)))[n]=l,e):d(l)},g(i,(function(e,i){d.fn[e]||(d.fn[e]=function(n){var s,o,r,a,l=v(n);return o=(s=this[a="alpha"===e?this._hsla?"hsla":"rgba":t]())[i.idx],"undefined"===l?o:("function"===l&&(l=v(n=n.call(this,o))),null==n&&i.empty?this:("string"===l&&(r=u.exec(n))&&(n=o+parseFloat(r[2])*("+"===r[1]?1:-1)),s[i.idx]=n,this[a](s)))})}))})),d.hook=function(t){var e=t.split(" ");g(e,(function(t,e){r.cssHooks[e]={set:function(t,i){var n;"transparent"===i||"string"===v(i)&&!(n=b(i))||(i=(i=d(n||i)).toRgbaString()),t.style[e]=i}},r.fx.step[e]=function(t){t.colorInit||(t.start=d(t.elem,e),t.end=d(t.end),t.colorInit=!0),r.cssHooks[e].set(t.elem,t.start.transition(t.end,t.pos))}}))},d.hook(a),r.cssHooks.borderColor={expand:function(t){var e={};return g(["Top","Right","Bottom","Left"],(function(i,n){e["border"+n+"Color"]=t})),e}},o=r.Color.names={aqua:"#00ffff",black:"#000000",blue:"#0000ff",fuchsia:"#ff00ff",gray:"#808080",green:"#008000",lime:"#00ff00",maroon:"#800000",navy:"#000080",olive:"#808000",purple:"#800080",red:"#ff0000",silver:"#c0c0c0",teal:"#008080",white:"#ffffff",yellow:"#ffff00",transparent:[null,null,null,0],_default:"#ffffff"};var w="ui-effects-",x="ui-effects-style",C="ui-effects-animated";t.effects={effect:{}},function(){var e=["add","remove","toggle"],i={border:1,borderBottom:1,borderColor:1,borderLeft:1,borderRight:1,borderTop:1,borderWidth:1,margin:1,padding:1};function n(t){return t.replace(/-([\da-z])/gi,(function(t,e){return e.toUpperCase()}))}function s(t){var e,i,s=t.ownerDocument.defaultView.getComputedStyle(t),o={};for(i=s.length;i--;)"string"==typeof s[e=s[i]]&&(o[n(e)]=s[e]);return o}function o(e,n){var s,o,r={};for(s in n)o=n[s],e[s]!==o&&(i[s]||!t.fx.step[s]&&isNaN(parseFloat(o))||(r[s]=o));return r}t.each(["borderLeftStyle","borderRightStyle","borderBottomStyle","borderTopStyle"],(function(e,i){t.fx.step[i]=function(t){("none"!==t.end&&!t.setAttr||1===t.pos&&!t.setAttr)&&(r.style(t.elem,i,t.end),t.setAttr=!0)}})),t.effects.animateClass=function(i,n,r,a){var l=t.speed(n,r,a);return this.queue((function(){var n,r=t(this),a=r.attr("class")||"",h=l.children?r.find("*").addBack():r;h=h.map((function(){return{el:t(this),start:s(this)}})),(n=function(){t.each(e,(function(t,e){i[e]&&r[e+"Class"](i[e])}))})(),h=h.map((function(){return this.end=s(this.el[0]),this.diff=o(this.start,this.end),this})),r.attr("class",a),h=h.map((function(){var e=this,i=t.Deferred(),n=t.extend({},l,{queue:!1,complete:function(){i.resolve(e)}});return this.el.animate(this.diff,n),i.promise()})),t.when.apply(t,h.get()).done((function(){n(),t.each(arguments,(function(){var e=this.el;t.each(this.diff,(function(t){e.css(t,"")}))})),l.complete.call(r[0])}))}))},t.fn.extend({addClass:function(e){return function(i,n,s,o){return n?t.effects.animateClass.call(this,{add:i},n,s,o):e.apply(this,arguments)}}(t.fn.addClass),removeClass:function(e){return function(i,n,s,o){return arguments.length>1?t.effects.animateClass.call(this,{remove:i},n,s,o):e.apply(this,arguments)}}(t.fn.removeClass),toggleClass:function(e){return function(i,n,s,o,r){return"boolean"==typeof n||void 0===n?s?t.effects.animateClass.call(this,n?{add:i}:{remove:i},s,o,r):e.apply(this,arguments):t.effects.animateClass.call(this,{toggle:i},n,s,o)}}(t.fn.toggleClass),switchClass:function(e,i,n,s,o){return t.effects.animateClass.call(this,{add:i,remove:e},n,s,o)}})}(),function(){function e(e,i,n,s){return t.isPlainObject(e)&&(i=e,e=e.effect),e={effect:e},null==i&&(i={}),"function"==typeof i&&(s=i,n=null,i={}),("number"==typeof i||t.fx.speeds[i])&&(s=n,n=i,i={}),"function"==typeof n&&(s=n,n=null),i&&t.extend(e,i),n=n||i.duration,e.duration=t.fx.off?0:"number"==typeof n?n:n in t.fx.speeds?t.fx.speeds[n]:t.fx.speeds._default,e.complete=s||i.complete,e}function i(e){return!(e&&"number"!=typeof e&&!t.fx.speeds[e])||("string"==typeof e&&!t.effects.effect[e]||("function"==typeof e||"object"==typeof e&&!e.effect))}function n(t,e){var i=e.outerWidth(),n=e.outerHeight(),s=/^rect\((-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto)\)$/.exec(t)||["",0,i,n,0];return{top:parseFloat(s[1])||0,right:"auto"===s[2]?i:parseFloat(s[2]),bottom:"auto"===s[3]?n:parseFloat(s[3]),left:parseFloat(s[4])||0}}t.expr&&t.expr.pseudos&&t.expr.pseudos.animated&&(t.expr.pseudos.animated=function(e){return function(i){return!!t(i).data(C)||e(i)}}(t.expr.pseudos.animated)),!0===t.uiBackCompat&&t.extend(t.effects,{save:function(t,e){for(var i=0,n=e.length;i<n;i++)null!==e[i]&&t.data(w+e[i],t[0].style[e[i]])},restore:function(t,e){for(var i,n=0,s=e.length;n<s;n++)null!==e[n]&&(i=t.data(w+e[n]),t.css(e[n],i))},setMode:function(t,e){return"toggle"===e&&(e=t.is(":hidden")?"show":"hide"),e},createWrapper:function(e){if(e.parent().is(".ui-effects-wrapper"))return e.parent();var i={width:e.outerWidth(!0),height:e.outerHeight(!0),float:e.css("float")},n=t("<div></div>").addClass("ui-effects-wrapper").css({fontSize:"100%",background:"transparent",border:"none",margin:0,padding:0}),s={width:e.width(),height:e.height()},o=document.activeElement;try{o.id}catch(t){o=document.body}return e.wrap(n),(e[0]===o||t.contains(e[0],o))&&t(o).trigger("focus"),n=e.parent(),"static"===e.css("position")?(n.css({position:"relative"}),e.css({position:"relative"})):(t.extend(i,{position:e.css("position"),zIndex:e.css("z-index")}),t.each(["top","left","bottom","right"],(function(t,n){i[n]=e.css(n),isNaN(parseInt(i[n],10))&&(i[n]="auto")})),e.css({position:"relative",top:0,left:0,right:"auto",bottom:"auto"})),e.css(s),n.css(i).show()},removeWrapper:function(e){var i=document.activeElement;return e.parent().is(".ui-effects-wrapper")&&(e.parent().replaceWith(e),(e[0]===i||t.contains(e[0],i))&&t(i).trigger("focus")),e}}),t.extend(t.effects,{version:"1.14.1",define:function(e,i,n){return n||(n=i,i="effect"),t.effects.effect[e]=n,t.effects.effect[e].mode=i,n},scaledDimensions:function(t,e,i){if(0===e)return{height:0,width:0,outerHeight:0,outerWidth:0};var n="horizontal"!==i?(e||100)/100:1,s="vertical"!==i?(e||100)/100:1;return{height:t.height()*s,width:t.width()*n,outerHeight:t.outerHeight()*s,outerWidth:t.outerWidth()*n}},clipToBox:function(t){return{width:t.clip.right-t.clip.left,height:t.clip.bottom-t.clip.top,left:t.clip.left,top:t.clip.top}},unshift:function(t,e,i){var n=t.queue();e>1&&n.splice.apply(n,[1,0].concat(n.splice(e,i))),t.dequeue()},saveStyle:function(t){t.data(x,t[0].style.cssText)},restoreStyle:function(t){t[0].style.cssText=t.data(x)||"",t.removeData(x)},mode:function(t,e){var i=t.is(":hidden");return"toggle"===e&&(e=i?"show":"hide"),(i?"hide"===e:"show"===e)&&(e="none"),e},getBaseline:function(t,e){var i,n;switch(t[0]){case"top":i=0;break;case"middle":i=.5;break;case"bottom":i=1;break;default:i=t[0]/e.height}switch(t[1]){case"left":n=0;break;case"center":n=.5;break;case"right":n=1;break;default:n=t[1]/e.width}return{x:n,y:i}},createPlaceholder:function(e){var i,n=e.css("position"),s=e.position();return e.css({marginTop:e.css("marginTop"),marginBottom:e.css("marginBottom"),marginLeft:e.css("marginLeft"),marginRight:e.css("marginRight")}).outerWidth(e.outerWidth()).outerHeight(e.outerHeight()),/^(static|relative)/.test(n)&&(n="absolute",i=t("<"+e[0].nodeName+">").insertAfter(e).css({display:/^(inline|ruby)/.test(e.css("display"))?"inline-block":"block",visibility:"hidden",marginTop:e.css("marginTop"),marginBottom:e.css("marginBottom"),marginLeft:e.css("marginLeft"),marginRight:e.css("marginRight"),float:e.css("float")}).outerWidth(e.outerWidth()).outerHeight(e.outerHeight()).addClass("ui-effects-placeholder"),e.data(w+"placeholder",i)),e.css({position:n,left:s.left,top:s.top}),i},removePlaceholder:function(t){var e=w+"placeholder",i=t.data(e);i&&(i.remove(),t.removeData(e))},cleanUp:function(e){t.effects.restoreStyle(e),t.effects.removePlaceholder(e)},setTransition:function(e,i,n,s){return s=s||{},t.each(i,(function(t,i){var o=e.cssUnit(i);o[0]>0&&(s[i]=o[0]*n+o[1])})),s}}),t.fn.extend({effect:function(){var i=e.apply(this,arguments),n=t.effects.effect[i.effect],s=n.mode,o=i.queue,r=o||"fx",a=i.complete,l=i.mode,h=[],u=function(e){var i=t(this),n=t.effects.mode(i,l)||s;i.data(C,!0),h.push(n),s&&("show"===n||n===s&&"hide"===n)&&i.show(),s&&"none"===n||t.effects.saveStyle(i),"function"==typeof e&&e()};if(t.fx.off||!n)return l?this[l](i.duration,a):this.each((function(){a&&a.call(this)}));function c(e){var o=t(this);function r(){o.removeData(C),t.effects.cleanUp(o),"hide"===i.mode&&o.hide(),u()}function u(){"function"==typeof a&&a.call(o[0]),"function"==typeof e&&e()}i.mode=h.shift(),!0!==t.uiBackCompat||s?"none"===i.mode?(o[l](),u()):n.call(o[0],i,r):(o.is(":hidden")?"hide"===l:"show"===l)?(o[l](),u()):n.call(o[0],i,u)}return!1===o?this.each(u).each(c):this.queue(r,u).queue(r,c)},show:function(t){return function(n){if(i(n))return t.apply(this,arguments);var s=e.apply(this,arguments);return s.mode="show",this.effect.call(this,s)}}(t.fn.show),hide:function(t){return function(n){if(i(n))return t.apply(this,arguments);var s=e.apply(this,arguments);return s.mode="hide",this.effect.call(this,s)}}(t.fn.hide),toggle:function(t){return function(n){if(i(n)||"boolean"==typeof n)return t.apply(this,arguments);var s=e.apply(this,arguments);return s.mode="toggle",this.effect.call(this,s)}}(t.fn.toggle),cssUnit:function(e){var i=this.css(e),n=[];return t.each(["em","px","%","pt"],(function(t,e){i.indexOf(e)>0&&(n=[parseFloat(i),e])})),n},cssClip:function(t){return t?this.css("clip","rect("+t.top+"px "+t.right+"px "+t.bottom+"px "+t.left+"px)"):n(this.css("clip"),this)},transfer:function(e,i){var n=t(this),s=t(e.to),o="fixed"===s.css("position"),r=t("body"),a=o?r.scrollTop():0,l=o?r.scrollLeft():0,h=s.offset(),u={top:h.top-a,left:h.left-l,height:s.innerHeight(),width:s.innerWidth()},c=n.offset(),d=t("<div class='ui-effects-transfer'></div>");d.appendTo("body").addClass(e.className).css({top:c.top-a,left:c.left-l,height:n.innerHeight(),width:n.innerWidth(),position:o?"fixed":"absolute"}).animate(u,e.duration,e.easing,(function(){d.remove(),"function"==typeof i&&i()}))}}),t.fx.step.clip=function(e){e.clipInit||(e.start=t(e.elem).cssClip(),"string"==typeof e.end&&(e.end=n(e.end,e.elem)),e.clipInit=!0),t(e.elem).cssClip({top:e.pos*(e.end.top-e.start.top)+e.start.top,right:e.pos*(e.end.right-e.start.right)+e.start.right,bottom:e.pos*(e.end.bottom-e.start.bottom)+e.start.bottom,left:e.pos*(e.end.left-e.start.left)+e.start.left})}}(),k={},t.each(["Quad","Cubic","Quart","Quint","Expo"],(function(t,e){k[e]=function(e){return Math.pow(e,t+2)}})),t.extend(k,{Sine:function(t){return 1-Math.cos(t*Math.PI/2)},Circ:function(t){return 1-Math.sqrt(1-t*t)},Elastic:function(t){return 0===t||1===t?t:-Math.pow(2,8*(t-1))*Math.sin((80*(t-1)-7.5)*Math.PI/15)},Back:function(t){return t*t*(3*t-2)},Bounce:function(t){for(var e,i=4;t<((e=Math.pow(2,--i))-1)/11;);return 1/Math.pow(4,3-i)-7.5625*Math.pow((3*e-2)/22-t,2)}}),t.each(k,(function(e,i){t.easing["easeIn"+e]=i,t.easing["easeOut"+e]=function(t){return 1-i(1-t)},t.easing["easeInOut"+e]=function(t){return t<.5?i(2*t)/2:1-i(-2*t+2)/2}}));var k;t.effects,t.effects.define("blind","hide",(function(e,i){var n={up:["bottom","top"],vertical:["bottom","top"],down:["top","bottom"],left:["right","left"],horizontal:["right","left"],right:["left","right"]},s=t(this),o=e.direction||"up",r=s.cssClip(),a={clip:t.extend({},r)},l=t.effects.createPlaceholder(s);a.clip[n[o][0]]=a.clip[n[o][1]],"show"===e.mode&&(s.cssClip(a.clip),l&&l.css(t.effects.clipToBox(a)),a.clip=r),l&&l.animate(t.effects.clipToBox(a),e.duration,e.easing),s.animate(a,{queue:!1,duration:e.duration,easing:e.easing,complete:i})})),t.effects.define("bounce",(function(e,i){var n,s,o,r=t(this),a=e.mode,l="hide"===a,h="show"===a,u=e.direction||"up",c=e.distance,d=e.times||5,p=2*d+(h||l?1:0),f=e.duration/p,g=e.easing,m="up"===u||"down"===u?"top":"left",v="up"===u||"left"===u,_=0,b=r.queue().length;for(t.effects.createPlaceholder(r),o=r.css(m),c||(c=r["top"===m?"outerHeight":"outerWidth"]()/3),h&&((s={opacity:1})[m]=o,r.css("opacity",0).css(m,v?2*-c:2*c).animate(s,f,g)),l&&(c/=Math.pow(2,d-1)),(s={})[m]=o;_<d;_++)(n={})[m]=(v?"-=":"+=")+c,r.animate(n,f,g).animate(s,f,g),c=l?2*c:c/2;l&&((n={opacity:0})[m]=(v?"-=":"+=")+c,r.animate(n,f,g)),r.queue(i),t.effects.unshift(r,b,p+1)})),t.effects.define("clip","hide",(function(e,i){var n,s={},o=t(this),r=e.direction||"vertical",a="both"===r,l=a||"horizontal"===r,h=a||"vertical"===r;n=o.cssClip(),s.clip={top:h?(n.bottom-n.top)/2:n.top,right:l?(n.right-n.left)/2:n.right,bottom:h?(n.bottom-n.top)/2:n.bottom,left:l?(n.right-n.left)/2:n.left},t.effects.createPlaceholder(o),"show"===e.mode&&(o.cssClip(s.clip),s.clip=n),o.animate(s,{queue:!1,duration:e.duration,easing:e.easing,complete:i})})),t.effects.define("drop","hide",(function(e,i){var n,s=t(this),o="show"===e.mode,r=e.direction||"left",a="up"===r||"down"===r?"top":"left",l="up"===r||"left"===r?"-=":"+=",h="+="===l?"-=":"+=",u={opacity:0};t.effects.createPlaceholder(s),n=e.distance||s["top"===a?"outerHeight":"outerWidth"](!0)/2,u[a]=l+n,o&&(s.css(u),u[a]=h+n,u.opacity=1),s.animate(u,{queue:!1,duration:e.duration,easing:e.easing,complete:i})})),t.effects.define("explode","hide",(function(e,i){var n,s,o,r,a,l,h=e.pieces?Math.round(Math.sqrt(e.pieces)):3,u=h,c=t(this),d="show"===e.mode,p=c.show().css("visibility","hidden").offset(),f=Math.ceil(c.outerWidth()/u),g=Math.ceil(c.outerHeight()/h),m=[];function v(){m.push(this),m.length===h*u&&_()}for(n=0;n<h;n++)for(r=p.top+n*g,l=n-(h-1)/2,s=0;s<u;s++)o=p.left+s*f,a=s-(u-1)/2,c.clone().appendTo("body").wrap("<div></div>").css({position:"absolute",visibility:"visible",left:-s*f,top:-n*g}).parent().addClass("ui-effects-explode").css({position:"absolute",overflow:"hidden",width:f,height:g,left:o+(d?a*f:0),top:r+(d?l*g:0),opacity:d?0:1}).animate({left:o+(d?0:a*f),top:r+(d?0:l*g),opacity:d?1:0},e.duration||500,e.easing,v);function _(){c.css({visibility:"visible"}),t(m).remove(),i()}})),t.effects.define("fade","toggle",(function(e,i){var n="show"===e.mode;t(this).css("opacity",n?0:1).animate({opacity:n?1:0},{queue:!1,duration:e.duration,easing:e.easing,complete:i})})),t.effects.define("fold","hide",(function(e,i){var n=t(this),s=e.mode,o="show"===s,r="hide"===s,a=e.size||15,l=/([0-9]+)%/.exec(a),h=!!e.horizFirst?["right","bottom"]:["bottom","right"],u=e.duration/2,c=t.effects.createPlaceholder(n),d=n.cssClip(),p={clip:t.extend({},d)},f={clip:t.extend({},d)},g=[d[h[0]],d[h[1]]],m=n.queue().length;l&&(a=parseInt(l[1],10)/100*g[r?0:1]),p.clip[h[0]]=a,f.clip[h[0]]=a,f.clip[h[1]]=0,o&&(n.cssClip(f.clip),c&&c.css(t.effects.clipToBox(f)),f.clip=d),n.queue((function(i){c&&c.animate(t.effects.clipToBox(p),u,e.easing).animate(t.effects.clipToBox(f),u,e.easing),i()})).animate(p,u,e.easing).animate(f,u,e.easing).queue(i),t.effects.unshift(n,m,4)})),t.effects.define("highlight","show",(function(e,i){var n=t(this),s={backgroundColor:n.css("backgroundColor")};"hide"===e.mode&&(s.opacity=0),t.effects.saveStyle(n),n.css({backgroundImage:"none",backgroundColor:e.color||"#ffff99"}).animate(s,{queue:!1,duration:e.duration,easing:e.easing,complete:i})})),t.effects.define("size",(function(e,i){var n,s,o,r=t(this),a=["fontSize"],l=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],h=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],u=e.mode,c="effect"!==u,d=e.scale||"both",p=e.origin||["middle","center"],f=r.css("position"),g=r.position(),m=t.effects.scaledDimensions(r),v=e.from||m,_=e.to||t.effects.scaledDimensions(r,0);t.effects.createPlaceholder(r),"show"===u&&(o=v,v=_,_=o),s={from:{y:v.height/m.height,x:v.width/m.width},to:{y:_.height/m.height,x:_.width/m.width}},"box"!==d&&"both"!==d||(s.from.y!==s.to.y&&(v=t.effects.setTransition(r,l,s.from.y,v),_=t.effects.setTransition(r,l,s.to.y,_)),s.from.x!==s.to.x&&(v=t.effects.setTransition(r,h,s.from.x,v),_=t.effects.setTransition(r,h,s.to.x,_))),"content"!==d&&"both"!==d||s.from.y!==s.to.y&&(v=t.effects.setTransition(r,a,s.from.y,v),_=t.effects.setTransition(r,a,s.to.y,_)),p&&(n=t.effects.getBaseline(p,m),v.top=(m.outerHeight-v.outerHeight)*n.y+g.top,v.left=(m.outerWidth-v.outerWidth)*n.x+g.left,_.top=(m.outerHeight-_.outerHeight)*n.y+g.top,_.left=(m.outerWidth-_.outerWidth)*n.x+g.left),delete v.outerHeight,delete v.outerWidth,r.css(v),"content"!==d&&"both"!==d||(l=l.concat(["marginTop","marginBottom"]).concat(a),h=h.concat(["marginLeft","marginRight"]),r.find("*[width]").each((function(){var i=t(this),n=t.effects.scaledDimensions(i),o={height:n.height*s.from.y,width:n.width*s.from.x,outerHeight:n.outerHeight*s.from.y,outerWidth:n.outerWidth*s.from.x},r={height:n.height*s.to.y,width:n.width*s.to.x,outerHeight:n.height*s.to.y,outerWidth:n.width*s.to.x};s.from.y!==s.to.y&&(o=t.effects.setTransition(i,l,s.from.y,o),r=t.effects.setTransition(i,l,s.to.y,r)),s.from.x!==s.to.x&&(o=t.effects.setTransition(i,h,s.from.x,o),r=t.effects.setTransition(i,h,s.to.x,r)),c&&t.effects.saveStyle(i),i.css(o),i.animate(r,e.duration,e.easing,(function(){c&&t.effects.restoreStyle(i)}))}))),r.animate(_,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){var e=r.offset();0===_.opacity&&r.css("opacity",v.opacity),c||(r.css("position","static"===f?"relative":f).offset(e),t.effects.saveStyle(r)),i()}})})),t.effects.define("scale",(function(e,i){var n=t(this),s=e.mode,o=parseInt(e.percent,10)||(0===parseInt(e.percent,10)||"effect"!==s?0:100),r=t.extend(!0,{from:t.effects.scaledDimensions(n),to:t.effects.scaledDimensions(n,o,e.direction||"both"),origin:e.origin||["middle","center"]},e);e.fade&&(r.from.opacity=1,r.to.opacity=0),t.effects.effect.size.call(this,r,i)})),t.effects.define("puff","hide",(function(e,i){var n=t.extend(!0,{},e,{fade:!0,percent:parseInt(e.percent,10)||150});t.effects.effect.scale.call(this,n,i)})),t.effects.define("pulsate","show",(function(e,i){var n=t(this),s=e.mode,o="show"===s,r=o||"hide"===s,a=2*(e.times||5)+(r?1:0),l=e.duration/a,h=0,u=1,c=n.queue().length;for(!o&&n.is(":visible")||(n.css("opacity",0).show(),h=1);u<a;u++)n.animate({opacity:h},l,e.easing),h=1-h;n.animate({opacity:h},l,e.easing),n.queue(i),t.effects.unshift(n,c,a+1)})),t.effects.define("shake",(function(e,i){var n=1,s=t(this),o=e.direction||"left",r=e.distance||20,a=e.times||3,l=2*a+1,h=Math.round(e.duration/l),u="up"===o||"down"===o?"top":"left",c="up"===o||"left"===o,d={},p={},f={},g=s.queue().length;for(t.effects.createPlaceholder(s),d[u]=(c?"-=":"+=")+r,p[u]=(c?"+=":"-=")+2*r,f[u]=(c?"-=":"+=")+2*r,s.animate(d,h,e.easing);n<a;n++)s.animate(p,h,e.easing).animate(f,h,e.easing);s.animate(p,h,e.easing).animate(d,h/2,e.easing).queue(i),t.effects.unshift(s,g,l+1)})),t.effects.define("slide","show",(function(e,i){var n,s,o=t(this),r={up:["bottom","top"],down:["top","bottom"],left:["right","left"],right:["left","right"]},a=e.mode,l=e.direction||"left",h="up"===l||"down"===l?"top":"left",u="up"===l||"left"===l,c=e.distance||o["top"===h?"outerHeight":"outerWidth"](!0),d={};t.effects.createPlaceholder(o),n=o.cssClip(),s=o.position()[h],d[h]=(u?-1:1)*c+s,d.clip=o.cssClip(),d.clip[r[l][1]]=d.clip[r[l][0]],"show"===a&&(o.cssClip(d.clip),o.css(h,d[h]),d.clip=n,d[h]=s),o.animate(d,{queue:!1,duration:e.duration,easing:e.easing,complete:i})}));!0===t.uiBackCompat&&t.effects.define("transfer",(function(e,i){t(this).transfer(e,i)}));t.ui.focusable=function(e,i){var n,s,o,r,a,l=e.nodeName.toLowerCase();return"area"===l?(s=(n=e.parentNode).name,!(!e.href||!s||"map"!==n.nodeName.toLowerCase())&&((o=t("img[usemap='#"+s+"']")).length>0&&o.is(":visible"))):(/^(input|select|textarea|button|object)$/.test(l)?(r=!e.disabled)&&(a=t(e).closest("fieldset")[0])&&(r=!a.disabled):r="a"===l&&e.href||i,r&&t(e).is(":visible")&&"visible"===t(e).css("visibility"))},t.extend(t.expr.pseudos,{focusable:function(e){return t.ui.focusable(e,null!=t.attr(e,"tabindex"))}});t.ui.focusable,t.ui.formResetMixin={_formResetHandler:function(){var e=t(this);setTimeout((function(){var i=e.data("ui-form-reset-instances");t.each(i,(function(){this.refresh()}))}))},_bindFormResetHandler:function(){if(this.form=t(this.element.prop("form")),this.form.length){var e=this.form.data("ui-form-reset-instances")||[];e.length||this.form.on("reset.ui-form-reset",this._formResetHandler),e.push(this),this.form.data("ui-form-reset-instances",e)}},_unbindFormResetHandler:function(){if(this.form.length){var e=this.form.data("ui-form-reset-instances");e.splice(t.inArray(this,e),1),e.length?this.form.data("ui-form-reset-instances",e):this.form.removeData("ui-form-reset-instances").off("reset.ui-form-reset")}}};t.escapeSelector||(t.escapeSelector=function(t){return CSS.escape(t+"")});t.fn.even&&t.fn.odd||t.fn.extend({even:function(){return this.filter((function(t){return t%2==0}))},odd:function(){return this.filter((function(t){return t%2==1}))}});t.ui.keyCode={BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38},t.fn.labels=function(){var t,e,i,n,s;return this.length?this[0].labels&&this[0].labels.length?this.pushStack(this[0].labels):(n=this.eq(0).parents("label"),(i=this.attr("id"))&&(s=(t=this.eq(0).parents().last()).add(t.length?t.siblings():this.siblings()),e="label[for='"+CSS.escape(i)+"']",n=n.add(s.find(e).addBack(e))),this.pushStack(n)):this.pushStack([])},t.fn.scrollParent=function(e){var i=this.css("position"),n="absolute"===i,s=e?/(auto|scroll|hidden)/:/(auto|scroll)/,o=this.parents().filter((function(){var e=t(this);return(!n||"static"!==e.css("position"))&&s.test(e.css("overflow")+e.css("overflow-y")+e.css("overflow-x"))})).eq(0);return"fixed"!==i&&o.length?o:t(this[0].ownerDocument||document)},t.extend(t.expr.pseudos,{tabbable:function(e){var i=t.attr(e,"tabindex"),n=null!=i;return(!n||i>=0)&&t.ui.focusable(e,n)}}),t.fn.extend({uniqueId:(D=0,function(){return this.each((function(){this.id||(this.id="ui-id-"+ ++D)}))}),removeUniqueId:function(){return this.each((function(){/^ui-id-\d+$/.test(this.id)&&t(this).removeAttr("id")}))}}),t.widget("ui.accordion",{version:"1.14.1",options:{active:0,animate:{},classes:{"ui-accordion-header":"ui-corner-top","ui-accordion-header-collapsed":"ui-corner-all","ui-accordion-content":"ui-corner-bottom"},collapsible:!1,event:"click",header:function(t){return t.find("> li > :first-child").add(t.find("> :not(li)").filter((function(t){return t%2==0})))},heightStyle:"auto",icons:{activeHeader:"ui-icon-triangle-1-s",header:"ui-icon-triangle-1-e"},activate:null,beforeActivate:null},hideProps:{borderTopWidth:"hide",borderBottomWidth:"hide",paddingTop:"hide",paddingBottom:"hide",height:"hide"},showProps:{borderTopWidth:"show",borderBottomWidth:"show",paddingTop:"show",paddingBottom:"show",height:"show"},_create:function(){var e=this.options;this.prevShow=this.prevHide=t(),this._addClass("ui-accordion","ui-widget ui-helper-reset"),this.element.attr("role","tablist"),e.collapsible||!1!==e.active&&null!=e.active||(e.active=0),this._processPanels(),e.active<0&&(e.active+=this.headers.length),this._refresh()},_getCreateEventData:function(){return{header:this.active,panel:this.active.length?this.active.next():t()}},_createIcons:function(){var e,i,n=this.options.icons;n&&(e=t("<span>"),this._addClass(e,"ui-accordion-header-icon","ui-icon "+n.header),e.prependTo(this.headers),i=this.active.children(".ui-accordion-header-icon"),this._removeClass(i,n.header)._addClass(i,null,n.activeHeader)._addClass(this.headers,"ui-accordion-icons"))},_destroyIcons:function(){this._removeClass(this.headers,"ui-accordion-icons"),this.headers.children(".ui-accordion-header-icon").remove()},_destroy:function(){var t;this.element.removeAttr("role"),this.headers.removeAttr("role aria-expanded aria-selected aria-controls tabIndex").removeUniqueId(),this._destroyIcons(),t=this.headers.next().css("display","").removeAttr("role aria-hidden aria-labelledby").removeUniqueId(),"content"!==this.options.heightStyle&&t.css("height","")},_setOption:function(t,e){"active"!==t?("event"===t&&(this.options.event&&this._off(this.headers,this.options.event),this._setupEvents(e)),this._super(t,e),"collapsible"!==t||e||!1!==this.options.active||this._activate(0),"icons"===t&&(this._destroyIcons(),e&&this._createIcons())):this._activate(e)},_setOptionDisabled:function(t){this._super(t),this.element.attr("aria-disabled",t),this._toggleClass(null,"ui-state-disabled",!!t)},_keydown:function(e){if(!e.altKey&&!e.ctrlKey){var i=t.ui.keyCode,n=this.headers.length,s=this.headers.index(e.target),o=!1;switch(e.keyCode){case i.RIGHT:case i.DOWN:o=this.headers[(s+1)%n];break;case i.LEFT:case i.UP:o=this.headers[(s-1+n)%n];break;case i.SPACE:case i.ENTER:this._eventHandler(e);break;case i.HOME:o=this.headers[0];break;case i.END:o=this.headers[n-1]}o&&(t(e.target).attr("tabIndex",-1),t(o).attr("tabIndex",0),t(o).trigger("focus"),e.preventDefault())}},_panelKeyDown:function(e){e.keyCode===t.ui.keyCode.UP&&e.ctrlKey&&t(e.currentTarget).prev().trigger("focus")},refresh:function(){var e=this.options;this._processPanels(),!1===e.active&&!0===e.collapsible||!this.headers.length?(e.active=!1,this.active=t()):!1===e.active?this._activate(0):this.active.length&&!t.contains(this.element[0],this.active[0])?this.headers.length===this.headers.find(".ui-state-disabled").length?(e.active=!1,this.active=t()):this._activate(Math.max(0,e.active-1)):e.active=this.headers.index(this.active),this._destroyIcons(),this._refresh()},_processPanels:function(){var t=this.headers,e=this.panels;"function"==typeof this.options.header?this.headers=this.options.header(this.element):this.headers=this.element.find(this.options.header),this._addClass(this.headers,"ui-accordion-header ui-accordion-header-collapsed","ui-state-default"),this.panels=this.headers.next().filter(":not(.ui-accordion-content-active)").hide(),this._addClass(this.panels,"ui-accordion-content","ui-helper-reset ui-widget-content"),e&&(this._off(t.not(this.headers)),this._off(e.not(this.panels)))},_refresh:function(){var e,i=this.options,n=i.heightStyle,s=this.element.parent();this.active=this._findActive(i.active),this._addClass(this.active,"ui-accordion-header-active","ui-state-active")._removeClass(this.active,"ui-accordion-header-collapsed"),this._addClass(this.active.next(),"ui-accordion-content-active"),this.active.next().show(),this.headers.attr("role","tab").each((function(){var e=t(this),i=e.uniqueId().attr("id"),n=e.next(),s=n.uniqueId().attr("id");e.attr("aria-controls",s),n.attr("aria-labelledby",i)})).next().attr("role","tabpanel"),this.headers.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1}).next().attr({"aria-hidden":"true"}).hide(),this.active.length?this.active.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}).next().attr({"aria-hidden":"false"}):this.headers.eq(0).attr("tabIndex",0),this._createIcons(),this._setupEvents(i.event),"fill"===n?(e=s.height(),this.element.siblings(":visible").each((function(){var i=t(this),n=i.css("position");"absolute"!==n&&"fixed"!==n&&(e-=i.outerHeight(!0))})),this.headers.each((function(){e-=t(this).outerHeight(!0)})),this.headers.next().each((function(){t(this).height(Math.max(0,e-t(this).innerHeight()+t(this).height()))})).css("overflow","auto")):"auto"===n&&(e=0,this.headers.next().each((function(){var i=t(this).is(":visible");i||t(this).show(),e=Math.max(e,t(this).css("height","").height()),i||t(this).hide()})).height(e))},_activate:function(e){var i=this._findActive(e)[0];i!==this.active[0]&&(i=i||this.active[0],this._eventHandler({target:i,currentTarget:i,preventDefault:t.noop}))},_findActive:function(e){return"number"==typeof e?this.headers.eq(e):t()},_setupEvents:function(e){var i={keydown:"_keydown"};e&&t.each(e.split(" "),(function(t,e){i[e]="_eventHandler"})),this._off(this.headers.add(this.headers.next())),this._on(this.headers,i),this._on(this.headers.next(),{keydown:"_panelKeyDown"}),this._hoverable(this.headers),this._focusable(this.headers)},_eventHandler:function(e){var i,n,s=this.options,o=this.active,r=t(e.currentTarget),a=r[0]===o[0],l=a&&s.collapsible,h=l?t():r.next(),u=o.next(),c={oldHeader:o,oldPanel:u,newHeader:l?t():r,newPanel:h};e.preventDefault(),a&&!s.collapsible||!1===this._trigger("beforeActivate",e,c)||(s.active=!l&&this.headers.index(r),this.active=a?t():r,this._toggle(c),this._removeClass(o,"ui-accordion-header-active","ui-state-active"),s.icons&&(i=o.children(".ui-accordion-header-icon"),this._removeClass(i,null,s.icons.activeHeader)._addClass(i,null,s.icons.header)),a||(this._removeClass(r,"ui-accordion-header-collapsed")._addClass(r,"ui-accordion-header-active","ui-state-active"),s.icons&&(n=r.children(".ui-accordion-header-icon"),this._removeClass(n,null,s.icons.header)._addClass(n,null,s.icons.activeHeader)),this._addClass(r.next(),"ui-accordion-content-active")))},_toggle:function(e){var i=e.newPanel,n=this.prevShow.length?this.prevShow:e.oldPanel;this.prevShow.add(this.prevHide).stop(!0,!0),this.prevShow=i,this.prevHide=n,this.options.animate?this._animate(i,n,e):(n.hide(),i.show(),this._toggleComplete(e)),n.attr({"aria-hidden":"true"}),n.prev().attr({"aria-selected":"false","aria-expanded":"false"}),i.length&&n.length?n.prev().attr({tabIndex:-1,"aria-expanded":"false"}):i.length&&this.headers.filter((function(){return 0===parseInt(t(this).attr("tabIndex"),10)})).attr("tabIndex",-1),i.attr("aria-hidden","false").prev().attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0})},_animate:function(t,e,i){var n,s,o,r=this,a=0,l=t.css("box-sizing"),h=t.length&&(!e.length||t.index()<e.index()),u=this.options.animate||{},c=h&&u.down||u,d=function(){r._toggleComplete(i)};return"number"==typeof c&&(o=c),"string"==typeof c&&(s=c),s=s||c.easing||u.easing,o=o||c.duration||u.duration,e.length?t.length?(n=t.show().outerHeight(),e.animate(this.hideProps,{duration:o,easing:s,step:function(t,e){e.now=Math.round(t)}}),void t.hide().animate(this.showProps,{duration:o,easing:s,complete:d,step:function(t,i){i.now=Math.round(t),"height"!==i.prop?"content-box"===l&&(a+=i.now):"content"!==r.options.heightStyle&&(i.now=Math.round(n-e.outerHeight()-a),a=0)}})):e.animate(this.hideProps,o,s,d):t.animate(this.showProps,o,s,d)},_toggleComplete:function(t){var e=t.oldPanel,i=e.prev();this._removeClass(e,"ui-accordion-content-active"),this._removeClass(i,"ui-accordion-header-active")._addClass(i,"ui-accordion-header-collapsed"),this._trigger("activate",null,t)}}),t.widget("ui.menu",{version:"1.14.1",defaultElement:"<ul>",delay:300,options:{icons:{submenu:"ui-icon-caret-1-e"},items:"> *",menus:"ul",position:{my:"left top",at:"right top"},role:"menu",blur:null,focus:null,select:null},_create:function(){this.activeMenu=this.element,this.mouseHandled=!1,this.lastMousePosition={x:null,y:null},this.element.uniqueId().attr({role:this.options.role,tabIndex:0}),this._addClass("ui-menu","ui-widget ui-widget-content"),this._on({"mousedown .ui-menu-item":function(t){t.preventDefault(),this._activateItem(t)},"click .ui-menu-item":function(e){var i=t(e.target),n=t(this.document[0].activeElement);!this.mouseHandled&&i.not(".ui-state-disabled").length&&(this.select(e),e.isPropagationStopped()||(this.mouseHandled=!0),i.has(".ui-menu").length?this.expand(e):!this.element.is(":focus")&&n.closest(".ui-menu").length&&(this.element.trigger("focus",[!0]),this.active&&1===this.active.parents(".ui-menu").length&&clearTimeout(this.timer)))},"mouseenter .ui-menu-item":"_activateItem","mousemove .ui-menu-item":"_activateItem",mouseleave:"collapseAll","mouseleave .ui-menu":"collapseAll",focus:function(t,e){var i=this.active||this._menuItems().first();e||this.focus(t,i)},blur:function(e){this._delay((function(){!t.contains(this.element[0],this.document[0].activeElement)&&this.collapseAll(e)}))},keydown:"_keydown"}),this.refresh(),this._on(this.document,{click:function(t){this._closeOnDocumentClick(t)&&this.collapseAll(t,!0),this.mouseHandled=!1}})},_activateItem:function(e){if(!this.previousFilter&&(e.clientX!==this.lastMousePosition.x||e.clientY!==this.lastMousePosition.y)){this.lastMousePosition={x:e.clientX,y:e.clientY};var i=t(e.target).closest(".ui-menu-item"),n=t(e.currentTarget);i[0]===n[0]&&(n.is(".ui-state-active")||(this._removeClass(n.siblings().children(".ui-state-active"),null,"ui-state-active"),this.focus(e,n)))}},_destroy:function(){var e=this.element.find(".ui-menu-item").removeAttr("role aria-disabled").children(".ui-menu-item-wrapper").removeUniqueId().removeAttr("tabIndex role aria-haspopup");this.element.removeAttr("aria-activedescendant").find(".ui-menu").addBack().removeAttr("role aria-labelledby aria-expanded aria-hidden aria-disabled tabIndex").removeUniqueId().show(),e.children().each((function(){var e=t(this);e.data("ui-menu-submenu-caret")&&e.remove()}))},_keydown:function(e){var i,n,s,o,r=!0;switch(e.keyCode){case t.ui.keyCode.PAGE_UP:this.previousPage(e);break;case t.ui.keyCode.PAGE_DOWN:this.nextPage(e);break;case t.ui.keyCode.HOME:this._move("first","first",e);break;case t.ui.keyCode.END:this._move("last","last",e);break;case t.ui.keyCode.UP:this.previous(e);break;case t.ui.keyCode.DOWN:this.next(e);break;case t.ui.keyCode.LEFT:this.collapse(e);break;case t.ui.keyCode.RIGHT:this.active&&!this.active.is(".ui-state-disabled")&&this.expand(e);break;case t.ui.keyCode.ENTER:case t.ui.keyCode.SPACE:this._activate(e);break;case t.ui.keyCode.ESCAPE:this.collapse(e);break;default:r=!1,n=this.previousFilter||"",o=!1,s=e.keyCode>=96&&e.keyCode<=105?(e.keyCode-96).toString():String.fromCharCode(e.keyCode),clearTimeout(this.filterTimer),s===n?o=!0:s=n+s,i=this._filterMenuItems(s),(i=o&&-1!==i.index(this.active.next())?this.active.nextAll(".ui-menu-item"):i).length||(s=String.fromCharCode(e.keyCode),i=this._filterMenuItems(s)),i.length?(this.focus(e,i),this.previousFilter=s,this.filterTimer=this._delay((function(){delete this.previousFilter}),1e3)):delete this.previousFilter}r&&e.preventDefault()},_activate:function(t){this.active&&!this.active.is(".ui-state-disabled")&&(this.active.children("[aria-haspopup='true']").length?this.expand(t):this.select(t))},refresh:function(){var e,i,n,s,o=this,r=this.options.icons.submenu,a=this.element.find(this.options.menus);this._toggleClass("ui-menu-icons",null,!!this.element.find(".ui-icon").length),i=a.filter(":not(.ui-menu)").hide().attr({role:this.options.role,"aria-hidden":"true","aria-expanded":"false"}).each((function(){var e=t(this),i=e.prev(),n=t("<span>").data("ui-menu-submenu-caret",!0);o._addClass(n,"ui-menu-icon","ui-icon "+r),i.attr("aria-haspopup","true").prepend(n),e.attr("aria-labelledby",i.attr("id"))})),this._addClass(i,"ui-menu","ui-widget ui-widget-content ui-front"),(e=a.add(this.element).find(this.options.items)).not(".ui-menu-item").each((function(){var e=t(this);o._isDivider(e)&&o._addClass(e,"ui-menu-divider","ui-widget-content")})),s=(n=e.not(".ui-menu-item, .ui-menu-divider")).children().not(".ui-menu").uniqueId().attr({tabIndex:-1,role:this._itemRole()}),this._addClass(n,"ui-menu-item")._addClass(s,"ui-menu-item-wrapper"),e.filter(".ui-state-disabled").attr("aria-disabled","true"),this.active&&!t.contains(this.element[0],this.active[0])&&this.blur()},_itemRole:function(){return{menu:"menuitem",listbox:"option"}[this.options.role]},_setOption:function(t,e){if("icons"===t){var i=this.element.find(".ui-menu-icon");this._removeClass(i,null,this.options.icons.submenu)._addClass(i,null,e.submenu)}this._super(t,e)},_setOptionDisabled:function(t){this._super(t),this.element.attr("aria-disabled",String(t)),this._toggleClass(null,"ui-state-disabled",!!t)},focus:function(t,e){var i,n,s;this.blur(t,t&&"focus"===t.type),this._scrollIntoView(e),this.active=e.first(),n=this.active.children(".ui-menu-item-wrapper"),this._addClass(n,null,"ui-state-active"),this.options.role&&this.element.attr("aria-activedescendant",n.attr("id")),s=this.active.parent().closest(".ui-menu-item").children(".ui-menu-item-wrapper"),this._addClass(s,null,"ui-state-active"),t&&"keydown"===t.type?this._close():this.timer=this._delay((function(){this._close()}),this.delay),(i=e.children(".ui-menu")).length&&t&&/^mouse/.test(t.type)&&this._startOpening(i),this.activeMenu=e.parent(),this._trigger("focus",t,{item:e})},_scrollIntoView:function(e){var i,n,s,o,r,a;this._hasScroll()&&(i=parseFloat(t.css(this.activeMenu[0],"borderTopWidth"))||0,n=parseFloat(t.css(this.activeMenu[0],"paddingTop"))||0,s=e.offset().top-this.activeMenu.offset().top-i-n,o=this.activeMenu.scrollTop(),r=this.activeMenu.height(),a=e.outerHeight(),s<0?this.activeMenu.scrollTop(o+s):s+a>r&&this.activeMenu.scrollTop(o+s-r+a))},blur:function(t,e){e||clearTimeout(this.timer),this.active&&(this._removeClass(this.active.children(".ui-menu-item-wrapper"),null,"ui-state-active"),this._trigger("blur",t,{item:this.active}),this.active=null)},_startOpening:function(t){clearTimeout(this.timer),"true"===t.attr("aria-hidden")&&(this.timer=this._delay((function(){this._close(),this._open(t)}),this.delay))},_open:function(e){var i=t.extend({of:this.active},this.options.position);clearTimeout(this.timer),this.element.find(".ui-menu").not(e.parents(".ui-menu")).hide().attr("aria-hidden","true"),e.show().removeAttr("aria-hidden").attr("aria-expanded","true").position(i)},collapseAll:function(e,i){clearTimeout(this.timer),this.timer=this._delay((function(){var n=i?this.element:t(e&&e.target).closest(this.element.find(".ui-menu"));n.length||(n=this.element),this._close(n),this.blur(e),this._removeClass(n.find(".ui-state-active"),null,"ui-state-active"),this.activeMenu=n}),i?0:this.delay)},_close:function(t){t||(t=this.active?this.active.parent():this.element),t.find(".ui-menu").hide().attr("aria-hidden","true").attr("aria-expanded","false")},_closeOnDocumentClick:function(e){return!t(e.target).closest(".ui-menu").length},_isDivider:function(t){return!/[^\-\u2014\u2013\s]/.test(t.text())},collapse:function(t){var e=this.active&&this.active.parent().closest(".ui-menu-item",this.element);e&&e.length&&(this._close(),this.focus(t,e))},expand:function(t){var e=this.active&&this._menuItems(this.active.children(".ui-menu")).first();e&&e.length&&(this._open(e.parent()),this._delay((function(){this.focus(t,e)})))},next:function(t){this._move("next","first",t)},previous:function(t){this._move("prev","last",t)},isFirstItem:function(){return this.active&&!this.active.prevAll(".ui-menu-item").length},isLastItem:function(){return this.active&&!this.active.nextAll(".ui-menu-item").length},_menuItems:function(t){return(t||this.element).find(this.options.items).filter(".ui-menu-item")},_move:function(t,e,i){var n;this.active&&(n="first"===t||"last"===t?this.active["first"===t?"prevAll":"nextAll"](".ui-menu-item").last():this.active[t+"All"](".ui-menu-item").first()),n&&n.length&&this.active||(n=this._menuItems(this.activeMenu)[e]()),this.focus(i,n)},nextPage:function(e){var i,n,s;this.active?this.isLastItem()||(this._hasScroll()?(n=this.active.offset().top,s=this.element.innerHeight(),0===t.fn.jquery.indexOf("3.2.")&&(s+=this.element[0].offsetHeight-this.element.outerHeight()),this.active.nextAll(".ui-menu-item").each((function(){return(i=t(this)).offset().top-n-s<0})),this.focus(e,i)):this.focus(e,this._menuItems(this.activeMenu)[this.active?"last":"first"]())):this.next(e)},previousPage:function(e){var i,n,s;this.active?this.isFirstItem()||(this._hasScroll()?(n=this.active.offset().top,s=this.element.innerHeight(),0===t.fn.jquery.indexOf("3.2.")&&(s+=this.element[0].offsetHeight-this.element.outerHeight()),this.active.prevAll(".ui-menu-item").each((function(){return(i=t(this)).offset().top-n+s>0})),this.focus(e,i)):this.focus(e,this._menuItems(this.activeMenu).first())):this.next(e)},_hasScroll:function(){return this.element.outerHeight()<this.element.prop("scrollHeight")},select:function(e){this.active=this.active||t(e.target).closest(".ui-menu-item");var i={item:this.active};this.active.has(".ui-menu").length||this.collapseAll(e,!0),this._trigger("select",e,i)},_filterMenuItems:function(e){var i=e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&"),n=new RegExp("^"+i,"i");return this.activeMenu.find(this.options.items).filter(".ui-menu-item").filter((function(){return n.test(String.prototype.trim.call(t(this).children(".ui-menu-item-wrapper").text()))}))}});var D;t.widget("ui.autocomplete",{version:"1.14.1",defaultElement:"<input>",options:{appendTo:null,autoFocus:!1,delay:300,minLength:1,position:{my:"left top",at:"left bottom",collision:"none"},source:null,change:null,close:null,focus:null,open:null,response:null,search:null,select:null},requestIndex:0,pending:0,liveRegionTimer:null,_create:function(){var e,i,n,s=this.element[0].nodeName.toLowerCase(),o="textarea"===s,r="input"===s;this.isMultiLine=o||!r&&"true"===this.element.prop("contentEditable"),this.valueMethod=this.element[o||r?"val":"text"],this.isNewMenu=!0,this._addClass("ui-autocomplete-input"),this.element.attr("autocomplete","off"),this._on(this.element,{keydown:function(s){if(this.element.prop("readOnly"))return e=!0,n=!0,void(i=!0);e=!1,n=!1,i=!1;var o=t.ui.keyCode;switch(s.keyCode){case o.PAGE_UP:e=!0,this._move("previousPage",s);break;case o.PAGE_DOWN:e=!0,this._move("nextPage",s);break;case o.UP:e=!0,this._keyEvent("previous",s);break;case o.DOWN:e=!0,this._keyEvent("next",s);break;case o.ENTER:this.menu.active&&(e=!0,s.preventDefault(),this.menu.select(s));break;case o.TAB:this.menu.active&&this.menu.select(s);break;case o.ESCAPE:this.menu.element.is(":visible")&&(this.isMultiLine||this._value(this.term),this.close(s),s.preventDefault());break;default:i=!0,this._searchTimeout(s)}},keypress:function(n){if(e)return e=!1,void(this.isMultiLine&&!this.menu.element.is(":visible")||n.preventDefault());if(!i){var s=t.ui.keyCode;switch(n.keyCode){case s.PAGE_UP:this._move("previousPage",n);break;case s.PAGE_DOWN:this._move("nextPage",n);break;case s.UP:this._keyEvent("previous",n);break;case s.DOWN:this._keyEvent("next",n)}}},input:function(t){if(n)return n=!1,void t.preventDefault();this._searchTimeout(t)},focus:function(){this.selectedItem=null,this.previous=this._value()},blur:function(t){clearTimeout(this.searching),this.close(t),this._change(t)}}),this._initSource(),this.menu=t("<ul>").appendTo(this._appendTo()).menu({role:null}).hide().menu("instance"),this._addClass(this.menu.element,"ui-autocomplete","ui-front"),this._on(this.menu.element,{mousedown:function(t){t.preventDefault()},menufocus:function(e,i){var n,s;if(this.isNewMenu&&(this.isNewMenu=!1,e.originalEvent&&/^mouse/.test(e.originalEvent.type)))return this.menu.blur(),void this.document.one("mousemove",(function(){t(e.target).trigger(e.originalEvent)}));s=i.item.data("ui-autocomplete-item"),!1!==this._trigger("focus",e,{item:s})&&e.originalEvent&&/^key/.test(e.originalEvent.type)&&this._value(s.value),(n=i.item.attr("aria-label")||s.value)&&String.prototype.trim.call(n).length&&(clearTimeout(this.liveRegionTimer),this.liveRegionTimer=this._delay((function(){this.liveRegion.html(t("<div>").text(n))}),100))},menuselect:function(t,e){var i=e.item.data("ui-autocomplete-item"),n=this.previous;this.element[0]!==this.document[0].activeElement&&(this.element.trigger("focus"),this.previous=n),!1!==this._trigger("select",t,{item:i})&&this._value(i.value),this.term=this._value(),this.close(t),this.selectedItem=i}}),this.liveRegion=t("<div>",{role:"status","aria-live":"assertive","aria-relevant":"additions"}).appendTo(this.document[0].body),this._addClass(this.liveRegion,null,"ui-helper-hidden-accessible"),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_destroy:function(){clearTimeout(this.searching),this.element.removeAttr("autocomplete"),this.menu.element.remove(),this.liveRegion.remove()},_setOption:function(t,e){this._super(t,e),"source"===t&&this._initSource(),"appendTo"===t&&this.menu.element.appendTo(this._appendTo()),"disabled"===t&&e&&this.xhr&&this.xhr.abort()},_isEventTargetInWidget:function(e){var i=this.menu.element[0];return e.target===this.element[0]||e.target===i||t.contains(i,e.target)},_closeOnClickOutside:function(t){this._isEventTargetInWidget(t)||this.close()},_appendTo:function(){var e=this.options.appendTo;return e&&(e=e.jquery||e.nodeType?t(e):this.document.find(e).eq(0)),e&&e[0]||(e=this.element.closest(".ui-front, dialog")),e.length||(e=this.document[0].body),e},_initSource:function(){var e,i,n=this;Array.isArray(this.options.source)?(e=this.options.source,this.source=function(i,n){n(t.ui.autocomplete.filter(e,i.term))}):"string"==typeof this.options.source?(i=this.options.source,this.source=function(e,s){n.xhr&&n.xhr.abort(),n.xhr=t.ajax({url:i,data:e,dataType:"json",success:function(t){s(t)},error:function(){s([])}})}):this.source=this.options.source},_searchTimeout:function(t){clearTimeout(this.searching),this.searching=this._delay((function(){var e=this.term===this._value(),i=this.menu.element.is(":visible"),n=t.altKey||t.ctrlKey||t.metaKey||t.shiftKey;e&&(!e||i||n)||(this.selectedItem=null,this.search(null,t))}),this.options.delay)},search:function(t,e){return t=null!=t?t:this._value(),this.term=this._value(),t.length<this.options.minLength?this.close(e):!1!==this._trigger("search",e)?this._search(t):void 0},_search:function(t){this.pending++,this._addClass("ui-autocomplete-loading"),this.cancelSearch=!1,this.source({term:t},this._response())},_response:function(){var t=++this.requestIndex;return function(e){t===this.requestIndex&&this.__response(e),this.pending--,this.pending||this._removeClass("ui-autocomplete-loading")}.bind(this)},__response:function(t){t&&(t=this._normalize(t)),this._trigger("response",null,{content:t}),!this.options.disabled&&t&&t.length&&!this.cancelSearch?(this._suggest(t),this._trigger("open")):this._close()},close:function(t){this.cancelSearch=!0,this._close(t)},_close:function(t){this._off(this.document,"mousedown"),this.menu.element.is(":visible")&&(this.menu.element.hide(),this.menu.blur(),this.isNewMenu=!0,this._trigger("close",t))},_change:function(t){this.previous!==this._value()&&this._trigger("change",t,{item:this.selectedItem})},_normalize:function(e){return e.length&&e[0].label&&e[0].value?e:t.map(e,(function(e){return"string"==typeof e?{label:e,value:e}:t.extend({},e,{label:e.label||e.value,value:e.value||e.label})}))},_suggest:function(e){var i=this.menu.element.empty();this._renderMenu(i,e),this.isNewMenu=!0,this.menu.refresh(),i.show(),this._resizeMenu(),i.position(t.extend({of:this.element},this.options.position)),this.options.autoFocus&&this.menu.next(),this._on(this.document,{mousedown:"_closeOnClickOutside"})},_resizeMenu:function(){var t=this.menu.element;t.outerWidth(Math.max(t.width("").outerWidth()+1,this.element.outerWidth()))},_renderMenu:function(e,i){var n=this;t.each(i,(function(t,i){n._renderItemData(e,i)}))},_renderItemData:function(t,e){return this._renderItem(t,e).data("ui-autocomplete-item",e)},_renderItem:function(e,i){return t("<li>").append(t("<div>").text(i.label)).appendTo(e)},_move:function(t,e){if(this.menu.element.is(":visible"))return this.menu.isFirstItem()&&/^previous/.test(t)||this.menu.isLastItem()&&/^next/.test(t)?(this.isMultiLine||this._value(this.term),void this.menu.blur()):void this.menu[t](e);this.search(null,e)},widget:function(){return this.menu.element},_value:function(){return this.valueMethod.apply(this.element,arguments)},_keyEvent:function(t,e){this.isMultiLine&&!this.menu.element.is(":visible")||(this._move(t,e),e.preventDefault())}}),t.extend(t.ui.autocomplete,{escapeRegex:function(t){return t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")},filter:function(e,i){var n=new RegExp(t.ui.autocomplete.escapeRegex(i),"i");return t.grep(e,(function(t){return n.test(t.label||t.value||t)}))}}),t.widget("ui.autocomplete",t.ui.autocomplete,{options:{messages:{noResults:"No search results.",results:function(t){return t+(t>1?" results are":" result is")+" available, use up and down arrow keys to navigate."}}},__response:function(e){var i;this._superApply(arguments),this.options.disabled||this.cancelSearch||(i=e&&e.length?this.options.messages.results(e.length):this.options.messages.noResults,clearTimeout(this.liveRegionTimer),this.liveRegionTimer=this._delay((function(){this.liveRegion.html(t("<div>").text(i))}),100))}});t.ui.autocomplete;var T=/ui-corner-([a-z]){2,6}/g;t.widget("ui.controlgroup",{version:"1.14.1",defaultElement:"<div>",options:{direction:"horizontal",disabled:null,onlyVisible:!0,items:{button:"input[type=button], input[type=submit], input[type=reset], button, a",controlgroupLabel:".ui-controlgroup-label",checkboxradio:"input[type='checkbox'], input[type='radio']",selectmenu:"select",spinner:".ui-spinner-input"}},_create:function(){this._enhance()},_enhance:function(){this.element.attr("role","toolbar"),this.refresh()},_destroy:function(){this._callChildMethod("destroy"),this.childWidgets.removeData("ui-controlgroup-data"),this.element.removeAttr("role"),this.options.items.controlgroupLabel&&this.element.find(this.options.items.controlgroupLabel).find(".ui-controlgroup-label-contents").contents().unwrap()},_initWidgets:function(){var e=this,i=[];t.each(this.options.items,(function(n,s){var o,r={};if(s)return"controlgroupLabel"===n?((o=e.element.find(s)).each((function(){var e=t(this);e.children(".ui-controlgroup-label-contents").length||e.contents().wrapAll("<span class='ui-controlgroup-label-contents'></span>")})),e._addClass(o,null,"ui-widget ui-widget-content ui-state-default"),void(i=i.concat(o.get()))):void(t.fn[n]&&(r=e["_"+n+"Options"]?e["_"+n+"Options"]("middle"):{classes:{}},e.element.find(s).each((function(){var s=t(this),o=s[n]("instance"),a=t.widget.extend({},r);if("button"!==n||!s.parent(".ui-spinner").length){o||(o=s[n]()[n]("instance")),o&&(a.classes=e._resolveClassesValues(a.classes,o)),s[n](a);var l=s[n]("widget");t.data(l[0],"ui-controlgroup-data",o||s[n]("instance")),i.push(l[0])}}))))})),this.childWidgets=t(t.uniqueSort(i)),this._addClass(this.childWidgets,"ui-controlgroup-item")},_callChildMethod:function(e){this.childWidgets.each((function(){var i=t(this).data("ui-controlgroup-data");i&&i[e]&&i[e]()}))},_updateCornerClass:function(t,e){var i="ui-corner-top ui-corner-bottom ui-corner-left ui-corner-right ui-corner-all",n=this._buildSimpleOptions(e,"label").classes.label;this._removeClass(t,null,i),this._addClass(t,null,n)},_buildSimpleOptions:function(t,e){var i="vertical"===this.options.direction,n={classes:{}};return n.classes[e]={middle:"",first:"ui-corner-"+(i?"top":"left"),last:"ui-corner-"+(i?"bottom":"right"),only:"ui-corner-all"}[t],n},_spinnerOptions:function(t){var e=this._buildSimpleOptions(t,"ui-spinner");return e.classes["ui-spinner-up"]="",e.classes["ui-spinner-down"]="",e},_buttonOptions:function(t){return this._buildSimpleOptions(t,"ui-button")},_checkboxradioOptions:function(t){return this._buildSimpleOptions(t,"ui-checkboxradio-label")},_selectmenuOptions:function(t){var e="vertical"===this.options.direction;return{width:!!e&&"auto",classes:{middle:{"ui-selectmenu-button-open":"","ui-selectmenu-button-closed":""},first:{"ui-selectmenu-button-open":"ui-corner-"+(e?"top":"tl"),"ui-selectmenu-button-closed":"ui-corner-"+(e?"top":"left")},last:{"ui-selectmenu-button-open":e?"":"ui-corner-tr","ui-selectmenu-button-closed":"ui-corner-"+(e?"bottom":"right")},only:{"ui-selectmenu-button-open":"ui-corner-top","ui-selectmenu-button-closed":"ui-corner-all"}}[t]}},_resolveClassesValues:function(e,i){var n={};return t.each(e,(function(t){var s=i.options.classes[t]||"";s=String.prototype.trim.call(s.replace(T,"")),n[t]=(s+" "+e[t]).replace(/\s+/g," ")})),n},_setOption:function(t,e){"direction"===t&&this._removeClass("ui-controlgroup-"+this.options.direction),this._super(t,e),"disabled"!==t?this.refresh():this._callChildMethod(e?"disable":"enable")},refresh:function(){var e,i=this;this._addClass("ui-controlgroup ui-controlgroup-"+this.options.direction),"horizontal"===this.options.direction&&this._addClass(null,"ui-helper-clearfix"),this._initWidgets(),e=this.childWidgets,this.options.onlyVisible&&(e=e.filter(":visible")),e.length&&(t.each(["first","last"],(function(t,n){var s=e[n]().data("ui-controlgroup-data");if(s&&i["_"+s.widgetName+"Options"]){var o=i["_"+s.widgetName+"Options"](1===e.length?"only":n);o.classes=i._resolveClassesValues(o.classes,s),s.element[s.widgetName](o)}else i._updateCornerClass(e[n](),n)})),this._callChildMethod("refresh"))}});t.widget("ui.checkboxradio",[t.ui.formResetMixin,{version:"1.14.1",options:{disabled:null,label:null,icon:!0,classes:{"ui-checkboxradio-label":"ui-corner-all","ui-checkboxradio-icon":"ui-corner-all"}},_getCreateOptions:function(){var e,i,n,s=this._super()||{};return this._readType(),i=this.element.labels(),this.label=t(i[i.length-1]),this.label.length||t.error("No label found for checkboxradio widget"),this.originalLabel="",(n=this.label.contents().not(this.element[0])).length&&(this.originalLabel+=n.clone().wrapAll("<div></div>").parent().html()),this.originalLabel&&(s.label=this.originalLabel),null!=(e=this.element[0].disabled)&&(s.disabled=e),s},_create:function(){var t=this.element[0].checked;this._bindFormResetHandler(),null==this.options.disabled&&(this.options.disabled=this.element[0].disabled),this._setOption("disabled",this.options.disabled),this._addClass("ui-checkboxradio","ui-helper-hidden-accessible"),this._addClass(this.label,"ui-checkboxradio-label","ui-button ui-widget"),"radio"===this.type&&this._addClass(this.label,"ui-checkboxradio-radio-label"),this.options.label&&this.options.label!==this.originalLabel?this._updateLabel():this.originalLabel&&(this.options.label=this.originalLabel),this._enhance(),t&&this._addClass(this.label,"ui-checkboxradio-checked","ui-state-active"),this._on({change:"_toggleClasses",focus:function(){this._addClass(this.label,null,"ui-state-focus ui-visual-focus")},blur:function(){this._removeClass(this.label,null,"ui-state-focus ui-visual-focus")}})},_readType:function(){var e=this.element[0].nodeName.toLowerCase();this.type=this.element[0].type,"input"===e&&/radio|checkbox/.test(this.type)||t.error("Can't create checkboxradio on element.nodeName="+e+" and element.type="+this.type)},_enhance:function(){this._updateIcon(this.element[0].checked)},widget:function(){return this.label},_getRadioGroup:function(){var e=this.element[0].name,i="input[name='"+CSS.escape(e)+"']";return e?(this.form.length?t(this.form[0].elements).filter(i):t(i).filter((function(){return 0===t(t(this).prop("form")).length}))).not(this.element):t([])},_toggleClasses:function(){var e=this.element[0].checked;this._toggleClass(this.label,"ui-checkboxradio-checked","ui-state-active",e),this.options.icon&&"checkbox"===this.type&&this._toggleClass(this.icon,null,"ui-icon-check ui-state-checked",e)._toggleClass(this.icon,null,"ui-icon-blank",!e),"radio"===this.type&&this._getRadioGroup().each((function(){var e=t(this).checkboxradio("instance");e&&e._removeClass(e.label,"ui-checkboxradio-checked","ui-state-active")}))},_destroy:function(){this._unbindFormResetHandler(),this.icon&&(this.icon.remove(),this.iconSpace.remove())},_setOption:function(t,e){if("label"!==t||e){if(this._super(t,e),"disabled"===t)return this._toggleClass(this.label,null,"ui-state-disabled",e),void(this.element[0].disabled=e);this.refresh()}},_updateIcon:function(e){var i="ui-icon ui-icon-background ";this.options.icon?(this.icon||(this.icon=t("<span>"),this.iconSpace=t("<span> </span>"),this._addClass(this.iconSpace,"ui-checkboxradio-icon-space")),"checkbox"===this.type?(i+=e?"ui-icon-check ui-state-checked":"ui-icon-blank",this._removeClass(this.icon,null,e?"ui-icon-blank":"ui-icon-check")):i+="ui-icon-blank",this._addClass(this.icon,"ui-checkboxradio-icon",i),e||this._removeClass(this.icon,null,"ui-icon-check ui-state-checked"),this.icon.prependTo(this.label).after(this.iconSpace)):void 0!==this.icon&&(this.icon.remove(),this.iconSpace.remove(),delete this.icon)},_updateLabel:function(){var t=this.label.contents().not(this.element[0]);this.icon&&(t=t.not(this.icon[0])),this.iconSpace&&(t=t.not(this.iconSpace[0])),t.remove(),this.label.append(this.options.label)},refresh:function(){var t=this.element[0].checked,e=this.element[0].disabled;this._updateIcon(t),this._toggleClass(this.label,"ui-checkboxradio-checked","ui-state-active",t),null!==this.options.label&&this._updateLabel(),e!==this.options.disabled&&this._setOptions({disabled:e})}}]);t.ui.checkboxradio;t.widget("ui.button",{version:"1.14.1",defaultElement:"<button>",options:{classes:{"ui-button":"ui-corner-all"},disabled:null,icon:null,iconPosition:"beginning",label:null,showLabel:!0},_getCreateOptions:function(){var t,e=this._super()||{};return this.isInput=this.element.is("input"),null!=(t=this.element[0].disabled)&&(e.disabled=t),this.originalLabel=this.isInput?this.element.val():this.element.html(),this.originalLabel&&(e.label=this.originalLabel),e},_create:function(){!this.option.showLabel&!this.options.icon&&(this.options.showLabel=!0),null==this.options.disabled&&(this.options.disabled=this.element[0].disabled||!1),this.hasTitle=!!this.element.attr("title"),this.options.label&&this.options.label!==this.originalLabel&&(this.isInput?this.element.val(this.options.label):this.element.html(this.options.label)),this._addClass("ui-button","ui-widget"),this._setOption("disabled",this.options.disabled),this._enhance(),this.element.is("a")&&this._on({keyup:function(e){e.keyCode===t.ui.keyCode.SPACE&&(e.preventDefault(),this.element[0].click?this.element[0].click():this.element.trigger("click"))}})},_enhance:function(){this.element.is("button")||this.element.attr("role","button"),this.options.icon&&(this._updateIcon("icon",this.options.icon),this._updateTooltip())},_updateTooltip:function(){this.title=this.element.attr("title"),this.options.showLabel||this.title||this.element.attr("title",this.options.label)},_updateIcon:function(e,i){var n="iconPosition"!==e,s=n?this.options.iconPosition:i,o="top"===s||"bottom"===s;this.icon?n&&this._removeClass(this.icon,null,this.options.icon):(this.icon=t("<span>"),this._addClass(this.icon,"ui-button-icon","ui-icon"),this.options.showLabel||this._addClass("ui-button-icon-only")),n&&this._addClass(this.icon,null,i),this._attachIcon(s),o?(this._addClass(this.icon,null,"ui-widget-icon-block"),this.iconSpace&&this.iconSpace.remove()):(this.iconSpace||(this.iconSpace=t("<span> </span>"),this._addClass(this.iconSpace,"ui-button-icon-space")),this._removeClass(this.icon,null,"ui-wiget-icon-block"),this._attachIconSpace(s))},_destroy:function(){this.element.removeAttr("role"),this.icon&&this.icon.remove(),this.iconSpace&&this.iconSpace.remove(),this.hasTitle||this.element.removeAttr("title")},_attachIconSpace:function(t){this.icon[/^(?:end|bottom)/.test(t)?"before":"after"](this.iconSpace)},_attachIcon:function(t){this.element[/^(?:end|bottom)/.test(t)?"append":"prepend"](this.icon)},_setOptions:function(t){var e=void 0===t.showLabel?this.options.showLabel:t.showLabel,i=void 0===t.icon?this.options.icon:t.icon;e||i||(t.showLabel=!0),this._super(t)},_setOption:function(t,e){"icon"===t&&(e?this._updateIcon(t,e):this.icon&&(this.icon.remove(),this.iconSpace&&this.iconSpace.remove())),"iconPosition"===t&&this._updateIcon(t,e),"showLabel"===t&&(this._toggleClass("ui-button-icon-only",null,!e),this._updateTooltip()),"label"===t&&(this.isInput?this.element.val(e):(this.element.html(e),this.icon&&(this._attachIcon(this.options.iconPosition),this._attachIconSpace(this.options.iconPosition)))),this._super(t,e),"disabled"===t&&(this._toggleClass(null,"ui-state-disabled",e),this.element[0].disabled=e,e&&this.element.trigger("blur"))},refresh:function(){var t=this.element.is("input, button")?this.element[0].disabled:this.element.hasClass("ui-button-disabled");t!==this.options.disabled&&this._setOptions({disabled:t}),this._updateTooltip()}}),!0===t.uiBackCompat&&(t.widget("ui.button",t.ui.button,{options:{text:!0,icons:{primary:null,secondary:null}},_create:function(){this.options.showLabel&&!this.options.text&&(this.options.showLabel=this.options.text),!this.options.showLabel&&this.options.text&&(this.options.text=this.options.showLabel),this.options.icon||!this.options.icons.primary&&!this.options.icons.secondary?this.options.icon&&(this.options.icons.primary=this.options.icon):this.options.icons.primary?this.options.icon=this.options.icons.primary:(this.options.icon=this.options.icons.secondary,this.options.iconPosition="end"),this._super()},_setOption:function(t,e){"text"!==t?("showLabel"===t&&(this.options.text=e),"icon"===t&&(this.options.icons.primary=e),"icons"===t&&(e.primary?(this._super("icon",e.primary),this._super("iconPosition","beginning")):e.secondary&&(this._super("icon",e.secondary),this._super("iconPosition","end"))),this._superApply(arguments)):this._super("showLabel",e)}}),t.fn.button=function(e){return function(i){var n="string"==typeof i,s=Array.prototype.slice.call(arguments,1),o=this;return n?this.length||"instance"!==i?this.each((function(){var e,n=t(this).attr("type"),r="checkbox"!==n&&"radio"!==n?"button":"checkboxradio",a=t.data(this,"ui-"+r);return"instance"===i?(o=a,!1):a?"function"!=typeof a[i]||"_"===i.charAt(0)?t.error("no such method '"+i+"' for button widget instance"):(e=a[i].apply(a,s))!==a&&void 0!==e?(o=e&&e.jquery?o.pushStack(e.get()):e,!1):void 0:t.error("cannot call methods on button prior to initialization; attempted to call method '"+i+"'")})):o=void 0:(s.length&&(i=t.widget.extend.apply(null,[i].concat(s))),this.each((function(){var n=t(this).attr("type"),s="checkbox"!==n&&"radio"!==n?"button":"checkboxradio",o=t.data(this,"ui-"+s);if(o)o.option(i||{}),o._init&&o._init();else{if("button"===s)return void e.call(t(this),i);t(this).checkboxradio(t.extend({icon:!1},i))}}))),o}}(t.fn.button),t.fn.buttonset=function(){return t.ui.controlgroup||t.error("Controlgroup widget missing"),"option"===arguments[0]&&"items"===arguments[1]&&arguments[2]?this.controlgroup.apply(this,[arguments[0],"items.button",arguments[2]]):"option"===arguments[0]&&"items"===arguments[1]?this.controlgroup.apply(this,[arguments[0],"items.button"]):("object"==typeof arguments[0]&&arguments[0].items&&(arguments[0].items={button:arguments[0].items}),this.controlgroup.apply(this,arguments))});var S;t.ui.button;function I(t){for(var e,i;t.length&&t[0]!==document;){if(("absolute"===(e=t.css("position"))||"relative"===e||"fixed"===e)&&(i=parseInt(t.css("zIndex"),10),!isNaN(i)&&0!==i))return i;t=t.parent()}return 0}function P(){this._curInst=null,this._keyEvent=!1,this._disabledInputs=[],this._datepickerShowing=!1,this._inDialog=!1,this._mainDivId="ui-datepicker-div",this._inlineClass="ui-datepicker-inline",this._appendClass="ui-datepicker-append",this._triggerClass="ui-datepicker-trigger",this._dialogClass="ui-datepicker-dialog",this._disableClass="ui-datepicker-disabled",this._unselectableClass="ui-datepicker-unselectable",this._currentClass="ui-datepicker-current-day",this._dayOverClass="ui-datepicker-days-cell-over",this.regional=[],this.regional[""]={closeText:"Done",prevText:"Prev",nextText:"Next",currentText:"Today",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],weekHeader:"Wk",dateFormat:"mm/dd/yy",firstDay:0,isRTL:!1,showMonthAfterYear:!1,yearSuffix:"",selectMonthLabel:"Select month",selectYearLabel:"Select year"},this._defaults={showOn:"focus",showAnim:"fadeIn",showOptions:{},defaultDate:null,appendText:"",buttonText:"...",buttonImage:"",buttonImageOnly:!1,hideIfNoPrevNext:!1,navigationAsDateFormat:!1,gotoCurrent:!1,changeMonth:!1,changeYear:!1,yearRange:"c-10:c+10",showOtherMonths:!1,selectOtherMonths:!1,showWeek:!1,calculateWeek:this.iso8601Week,shortYearCutoff:"+10",minDate:null,maxDate:null,duration:"fast",beforeShowDay:null,beforeShow:null,onSelect:null,onChangeMonthYear:null,onClose:null,onUpdateDatepicker:null,numberOfMonths:1,showCurrentAtPos:0,stepMonths:1,stepBigMonths:12,altField:"",altFormat:"",constrainInput:!0,showButtonPanel:!1,autoSize:!1,disabled:!1},t.extend(this._defaults,this.regional[""]),this.regional.en=t.extend(!0,{},this.regional[""]),this.regional["en-US"]=t.extend(!0,{},this.regional.en),this.dpDiv=A(t("<div id='"+this._mainDivId+"' class='ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>"))}function A(e){var i="button, .ui-datepicker-prev, .ui-datepicker-next, .ui-datepicker-calendar td a";return e.on("mouseout",i,(function(){t(this).removeClass("ui-state-hover"),-1!==this.className.indexOf("ui-datepicker-prev")&&t(this).removeClass("ui-datepicker-prev-hover"),-1!==this.className.indexOf("ui-datepicker-next")&&t(this).removeClass("ui-datepicker-next-hover")})).on("mouseover",i,M)}function M(){t.datepicker._isDisabledDatepicker(S.inline?S.dpDiv.parent()[0]:S.input[0])||(t(this).parents(".ui-datepicker-calendar").find("a").removeClass("ui-state-hover"),t(this).addClass("ui-state-hover"),-1!==this.className.indexOf("ui-datepicker-prev")&&t(this).addClass("ui-datepicker-prev-hover"),-1!==this.className.indexOf("ui-datepicker-next")&&t(this).addClass("ui-datepicker-next-hover"))}function E(e,i){for(var n in t.extend(e,i),i)null==i[n]&&(e[n]=i[n]);return e}t.extend(t.ui,{datepicker:{version:"1.14.1"}}),t.extend(P.prototype,{markerClassName:"hasDatepicker",maxRows:4,_widgetDatepicker:function(){return this.dpDiv},setDefaults:function(t){return E(this._defaults,t||{}),this},_attachDatepicker:function(e,i){var n,s,o;s="div"===(n=e.nodeName.toLowerCase())||"span"===n,e.id||(this.uuid+=1,e.id="dp"+this.uuid),(o=this._newInst(t(e),s)).settings=t.extend({},i||{}),"input"===n?this._connectDatepicker(e,o):s&&this._inlineDatepicker(e,o)},_newInst:function(e,i){return{id:e[0].id.replace(/([^A-Za-z0-9_\-])/g,"\\\\$1"),input:e,selectedDay:0,selectedMonth:0,selectedYear:0,drawMonth:0,drawYear:0,inline:i,dpDiv:i?A(t("<div class='"+this._inlineClass+" ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>")):this.dpDiv}},_connectDatepicker:function(e,i){var n=t(e);i.append=t([]),i.trigger=t([]),n.hasClass(this.markerClassName)||(this._attachments(n,i),n.addClass(this.markerClassName).on("keydown",this._doKeyDown).on("keypress",this._doKeyPress).on("keyup",this._doKeyUp),this._autoSize(i),t.data(e,"datepicker",i),i.settings.disabled&&this._disableDatepicker(e))},_attachments:function(e,i){var n,s,o,r=this._get(i,"appendText"),a=this._get(i,"isRTL");i.append&&i.append.remove(),r&&(i.append=t("<span>").addClass(this._appendClass).text(r),e[a?"before":"after"](i.append)),e.off("focus",this._showDatepicker),i.trigger&&i.trigger.remove(),"focus"!==(n=this._get(i,"showOn"))&&"both"!==n||e.on("focus",this._showDatepicker),"button"!==n&&"both"!==n||(s=this._get(i,"buttonText"),o=this._get(i,"buttonImage"),this._get(i,"buttonImageOnly")?i.trigger=t("<img>").addClass(this._triggerClass).attr({src:o,alt:s,title:s}):(i.trigger=t("<button type='button'>").addClass(this._triggerClass),o?i.trigger.html(t("<img>").attr({src:o,alt:s,title:s})):i.trigger.text(s)),e[a?"before":"after"](i.trigger),i.trigger.on("click",(function(){return t.datepicker._datepickerShowing&&t.datepicker._lastInput===e[0]?t.datepicker._hideDatepicker():t.datepicker._datepickerShowing&&t.datepicker._lastInput!==e[0]?(t.datepicker._hideDatepicker(),t.datepicker._showDatepicker(e[0])):t.datepicker._showDatepicker(e[0]),!1})))},_autoSize:function(t){if(this._get(t,"autoSize")&&!t.inline){var e,i,n,s,o=new Date(2009,11,20),r=this._get(t,"dateFormat");r.match(/[DM]/)&&(e=function(t){for(i=0,n=0,s=0;s<t.length;s++)t[s].length>i&&(i=t[s].length,n=s);return n},o.setMonth(e(this._get(t,r.match(/MM/)?"monthNames":"monthNamesShort"))),o.setDate(e(this._get(t,r.match(/DD/)?"dayNames":"dayNamesShort"))+20-o.getDay())),t.input.attr("size",this._formatDate(t,o).length)}},_inlineDatepicker:function(e,i){var n=t(e);n.hasClass(this.markerClassName)||(n.addClass(this.markerClassName).append(i.dpDiv),t.data(e,"datepicker",i),this._setDate(i,this._getDefaultDate(i),!0),this._updateDatepicker(i),this._updateAlternate(i),i.settings.disabled&&this._disableDatepicker(e),i.dpDiv.css("display","block"))},_dialogDatepicker:function(e,i,n,s,o){var r,a,l,h,u,c=this._dialogInst;return c||(this.uuid+=1,r="dp"+this.uuid,this._dialogInput=t("<input type='text' id='"+r+"' style='position: absolute; top: -100px; width: 0px;'/>"),this._dialogInput.on("keydown",this._doKeyDown),t("body").append(this._dialogInput),(c=this._dialogInst=this._newInst(this._dialogInput,!1)).settings={},t.data(this._dialogInput[0],"datepicker",c)),E(c.settings,s||{}),i=i&&i.constructor===Date?this._formatDate(c,i):i,this._dialogInput.val(i),this._pos=o?o.length?o:[o.pageX,o.pageY]:null,this._pos||(a=document.documentElement.clientWidth,l=document.documentElement.clientHeight,h=document.documentElement.scrollLeft||document.body.scrollLeft,u=document.documentElement.scrollTop||document.body.scrollTop,this._pos=[a/2-100+h,l/2-150+u]),this._dialogInput.css("left",this._pos[0]+20+"px").css("top",this._pos[1]+"px"),c.settings.onSelect=n,this._inDialog=!0,this.dpDiv.addClass(this._dialogClass),this._showDatepicker(this._dialogInput[0]),t.blockUI&&t.blockUI(this.dpDiv),t.data(this._dialogInput[0],"datepicker",c),this},_destroyDatepicker:function(e){var i,n=t(e),s=t.data(e,"datepicker");n.hasClass(this.markerClassName)&&(i=e.nodeName.toLowerCase(),t.removeData(e,"datepicker"),"input"===i?(s.append.remove(),s.trigger.remove(),n.removeClass(this.markerClassName).off("focus",this._showDatepicker).off("keydown",this._doKeyDown).off("keypress",this._doKeyPress).off("keyup",this._doKeyUp)):"div"!==i&&"span"!==i||n.removeClass(this.markerClassName).empty(),t.datepicker._hideDatepicker(),S===s&&(S=null,this._curInst=null))},_enableDatepicker:function(e){var i,n,s=t(e),o=t.data(e,"datepicker");s.hasClass(this.markerClassName)&&("input"===(i=e.nodeName.toLowerCase())?(e.disabled=!1,o.trigger.filter("button").each((function(){this.disabled=!1})).end().filter("img").css({opacity:"1.0",cursor:""})):"div"!==i&&"span"!==i||((n=s.children("."+this._inlineClass)).children().removeClass("ui-state-disabled"),n.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!1)),this._disabledInputs=t.map(this._disabledInputs,(function(t){return t===e?null:t})))},_disableDatepicker:function(e){var i,n,s=t(e),o=t.data(e,"datepicker");s.hasClass(this.markerClassName)&&("input"===(i=e.nodeName.toLowerCase())?(e.disabled=!0,o.trigger.filter("button").each((function(){this.disabled=!0})).end().filter("img").css({opacity:"0.5",cursor:"default"})):"div"!==i&&"span"!==i||((n=s.children("."+this._inlineClass)).children().addClass("ui-state-disabled"),n.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!0)),this._disabledInputs=t.map(this._disabledInputs,(function(t){return t===e?null:t})),this._disabledInputs[this._disabledInputs.length]=e)},_isDisabledDatepicker:function(t){if(!t)return!1;for(var e=0;e<this._disabledInputs.length;e++)if(this._disabledInputs[e]===t)return!0;return!1},_getInst:function(e){try{return t.data(e,"datepicker")}catch(t){throw"Missing instance data for this datepicker"}},_optionDatepicker:function(e,i,n){var s,o,r,a,l=this._getInst(e);if(2===arguments.length&&"string"==typeof i)return"defaults"===i?t.extend({},t.datepicker._defaults):l?"all"===i?t.extend({},l.settings):this._get(l,i):null;s=i||{},"string"==typeof i&&((s={})[i]=n),l&&(this._curInst===l&&this._hideDatepicker(),o=this._getDateDatepicker(e,!0),r=this._getMinMaxDate(l,"min"),a=this._getMinMaxDate(l,"max"),E(l.settings,s),null!==r&&void 0!==s.dateFormat&&void 0===s.minDate&&(l.settings.minDate=this._formatDate(l,r)),null!==a&&void 0!==s.dateFormat&&void 0===s.maxDate&&(l.settings.maxDate=this._formatDate(l,a)),"disabled"in s&&(s.disabled?this._disableDatepicker(e):this._enableDatepicker(e)),this._attachments(t(e),l),this._autoSize(l),this._setDate(l,o),this._updateAlternate(l),this._updateDatepicker(l))},_changeDatepicker:function(t,e,i){this._optionDatepicker(t,e,i)},_refreshDatepicker:function(t){var e=this._getInst(t);e&&this._updateDatepicker(e)},_setDateDatepicker:function(t,e){var i=this._getInst(t);i&&(this._setDate(i,e),this._updateDatepicker(i),this._updateAlternate(i))},_getDateDatepicker:function(t,e){var i=this._getInst(t);return i&&!i.inline&&this._setDateFromField(i,e),i?this._getDate(i):null},_doKeyDown:function(e){var i,n,s,o=t.datepicker._getInst(e.target),r=!0,a=o.dpDiv.is(".ui-datepicker-rtl");if(o._keyEvent=!0,t.datepicker._datepickerShowing)switch(e.keyCode){case 9:t.datepicker._hideDatepicker(),r=!1;break;case 13:return(s=t("td."+t.datepicker._dayOverClass+":not(."+t.datepicker._currentClass+")",o.dpDiv))[0]&&t.datepicker._selectDay(e.target,o.selectedMonth,o.selectedYear,s[0]),(i=t.datepicker._get(o,"onSelect"))?(n=t.datepicker._formatDate(o),i.apply(o.input?o.input[0]:null,[n,o])):t.datepicker._hideDatepicker(),!1;case 27:t.datepicker._hideDatepicker();break;case 33:t.datepicker._adjustDate(e.target,e.ctrlKey?-t.datepicker._get(o,"stepBigMonths"):-t.datepicker._get(o,"stepMonths"),"M");break;case 34:t.datepicker._adjustDate(e.target,e.ctrlKey?+t.datepicker._get(o,"stepBigMonths"):+t.datepicker._get(o,"stepMonths"),"M");break;case 35:(e.ctrlKey||e.metaKey)&&t.datepicker._clearDate(e.target),r=e.ctrlKey||e.metaKey;break;case 36:(e.ctrlKey||e.metaKey)&&t.datepicker._gotoToday(e.target),r=e.ctrlKey||e.metaKey;break;case 37:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,a?1:-1,"D"),r=e.ctrlKey||e.metaKey,e.originalEvent.altKey&&t.datepicker._adjustDate(e.target,e.ctrlKey?-t.datepicker._get(o,"stepBigMonths"):-t.datepicker._get(o,"stepMonths"),"M");break;case 38:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,-7,"D"),r=e.ctrlKey||e.metaKey;break;case 39:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,a?-1:1,"D"),r=e.ctrlKey||e.metaKey,e.originalEvent.altKey&&t.datepicker._adjustDate(e.target,e.ctrlKey?+t.datepicker._get(o,"stepBigMonths"):+t.datepicker._get(o,"stepMonths"),"M");break;case 40:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,7,"D"),r=e.ctrlKey||e.metaKey;break;default:r=!1}else 36===e.keyCode&&e.ctrlKey?t.datepicker._showDatepicker(this):r=!1;r&&(e.preventDefault(),e.stopPropagation())},_doKeyPress:function(e){var i,n,s=t.datepicker._getInst(e.target);if(t.datepicker._get(s,"constrainInput"))return i=t.datepicker._possibleChars(t.datepicker._get(s,"dateFormat")),n=String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),e.ctrlKey||e.metaKey||n<" "||!i||i.indexOf(n)>-1},_doKeyUp:function(e){var i=t.datepicker._getInst(e.target);if(i.input.val()!==i.lastVal)try{t.datepicker.parseDate(t.datepicker._get(i,"dateFormat"),i.input?i.input.val():null,t.datepicker._getFormatConfig(i))&&(t.datepicker._setDateFromField(i),t.datepicker._updateAlternate(i),t.datepicker._updateDatepicker(i))}catch(t){}return!0},_showDatepicker:function(e){var i,n,s,o,r,a,l;("input"!==(e=e.target||e).nodeName.toLowerCase()&&(e=t("input",e.parentNode)[0]),t.datepicker._isDisabledDatepicker(e)||t.datepicker._lastInput===e)||(i=t.datepicker._getInst(e),t.datepicker._curInst&&t.datepicker._curInst!==i&&(t.datepicker._curInst.dpDiv.stop(!0,!0),i&&t.datepicker._datepickerShowing&&t.datepicker._hideDatepicker(t.datepicker._curInst.input[0])),!1!==(s=(n=t.datepicker._get(i,"beforeShow"))?n.apply(e,[e,i]):{})&&(E(i.settings,s),i.lastVal=null,t.datepicker._lastInput=e,t.datepicker._setDateFromField(i),t.datepicker._inDialog&&(e.value=""),t.datepicker._pos||(t.datepicker._pos=t.datepicker._findPos(e),t.datepicker._pos[1]+=e.offsetHeight),o=!1,t(e).parents().each((function(){return!(o|="fixed"===t(this).css("position"))})),r={left:t.datepicker._pos[0],top:t.datepicker._pos[1]},t.datepicker._pos=null,i.dpDiv.empty(),i.dpDiv.css({position:"absolute",display:"block",top:"-1000px"}),t.datepicker._updateDatepicker(i),r=t.datepicker._checkOffset(i,r,o),i.dpDiv.css({position:t.datepicker._inDialog&&t.blockUI?"static":o?"fixed":"absolute",display:"none",left:r.left+"px",top:r.top+"px"}),i.inline||(a=t.datepicker._get(i,"showAnim"),l=t.datepicker._get(i,"duration"),i.dpDiv.css("z-index",I(t(e))+1),t.datepicker._datepickerShowing=!0,t.effects&&t.effects.effect[a]?i.dpDiv.show(a,t.datepicker._get(i,"showOptions"),l):i.dpDiv[a||"show"](a?l:null),t.datepicker._shouldFocusInput(i)&&i.input.trigger("focus"),t.datepicker._curInst=i)))},_updateDatepicker:function(e){this.maxRows=4,S=e,e.dpDiv.empty().append(this._generateHTML(e)),this._attachHandlers(e);var i,n=this._getNumberOfMonths(e),s=n[1],o=17,r=e.dpDiv.find("."+this._dayOverClass+" a"),a=t.datepicker._get(e,"onUpdateDatepicker");r.length>0&&M.apply(r.get(0)),e.dpDiv.removeClass("ui-datepicker-multi-2 ui-datepicker-multi-3 ui-datepicker-multi-4").width(""),s>1&&e.dpDiv.addClass("ui-datepicker-multi-"+s).css("width",o*s+"em"),e.dpDiv[(1!==n[0]||1!==n[1]?"add":"remove")+"Class"]("ui-datepicker-multi"),e.dpDiv[(this._get(e,"isRTL")?"add":"remove")+"Class"]("ui-datepicker-rtl"),e===t.datepicker._curInst&&t.datepicker._datepickerShowing&&t.datepicker._shouldFocusInput(e)&&e.input.trigger("focus"),e.yearshtml&&(i=e.yearshtml,setTimeout((function(){i===e.yearshtml&&e.yearshtml&&e.dpDiv.find("select.ui-datepicker-year").first().replaceWith(e.yearshtml),i=e.yearshtml=null}),0)),a&&a.apply(e.input?e.input[0]:null,[e])},_shouldFocusInput:function(t){return t.input&&t.input.is(":visible")&&!t.input.is(":disabled")},_checkOffset:function(e,i,n){var s=e.dpDiv.outerWidth(),o=e.dpDiv.outerHeight(),r=e.input?e.input.outerWidth():0,a=e.input?e.input.outerHeight():0,l=document.documentElement.clientWidth+(n?0:t(document).scrollLeft()),h=document.documentElement.clientHeight+(n?0:t(document).scrollTop());return i.left-=this._get(e,"isRTL")?s-r:0,i.left-=n&&i.left===e.input.offset().left?t(document).scrollLeft():0,i.top-=n&&i.top===e.input.offset().top+a?t(document).scrollTop():0,i.left-=Math.min(i.left,i.left+s>l&&l>s?Math.abs(i.left+s-l):0),i.top-=Math.min(i.top,i.top+o>h&&h>o?Math.abs(o+a):0),i},_findPos:function(e){for(var i,n=this._getInst(e),s=this._get(n,"isRTL");e&&("hidden"===e.type||1!==e.nodeType||t.expr.pseudos.hidden(e));)e=e[s?"previousSibling":"nextSibling"];return[(i=t(e).offset()).left,i.top]},_hideDatepicker:function(e){var i,n,s,o,r=this._curInst;!r||e&&r!==t.data(e,"datepicker")||this._datepickerShowing&&(i=this._get(r,"showAnim"),n=this._get(r,"duration"),s=function(){t.datepicker._tidyDialog(r)},t.effects&&t.effects.effect[i]?r.dpDiv.hide(i,t.datepicker._get(r,"showOptions"),n,s):r.dpDiv["slideDown"===i?"slideUp":"fadeIn"===i?"fadeOut":"hide"](i?n:null,s),i||s(),this._datepickerShowing=!1,(o=this._get(r,"onClose"))&&o.apply(r.input?r.input[0]:null,[r.input?r.input.val():"",r]),this._lastInput=null,this._inDialog&&(this._dialogInput.css({position:"absolute",left:"0",top:"-100px"}),t.blockUI&&(t.unblockUI(),t("body").append(this.dpDiv))),this._inDialog=!1)},_tidyDialog:function(t){t.dpDiv.removeClass(this._dialogClass).off(".ui-datepicker-calendar")},_checkExternalClick:function(e){if(t.datepicker._curInst){var i=t(e.target),n=t.datepicker._getInst(i[0]);(i[0].id===t.datepicker._mainDivId||0!==i.parents("#"+t.datepicker._mainDivId).length||i.hasClass(t.datepicker.markerClassName)||i.closest("."+t.datepicker._triggerClass).length||!t.datepicker._datepickerShowing||t.datepicker._inDialog&&t.blockUI)&&(!i.hasClass(t.datepicker.markerClassName)||t.datepicker._curInst===n)||t.datepicker._hideDatepicker()}},_adjustDate:function(e,i,n){var s=t(e),o=this._getInst(s[0]);this._isDisabledDatepicker(s[0])||(this._adjustInstDate(o,i,n),this._updateDatepicker(o))},_gotoToday:function(e){var i,n=t(e),s=this._getInst(n[0]);this._get(s,"gotoCurrent")&&s.currentDay?(s.selectedDay=s.currentDay,s.drawMonth=s.selectedMonth=s.currentMonth,s.drawYear=s.selectedYear=s.currentYear):(i=new Date,s.selectedDay=i.getDate(),s.drawMonth=s.selectedMonth=i.getMonth(),s.drawYear=s.selectedYear=i.getFullYear()),this._notifyChange(s),this._adjustDate(n)},_selectMonthYear:function(e,i,n){var s=t(e),o=this._getInst(s[0]);o["selected"+("M"===n?"Month":"Year")]=o["draw"+("M"===n?"Month":"Year")]=parseInt(i.options[i.selectedIndex].value,10),this._notifyChange(o),this._adjustDate(s)},_selectDay:function(e,i,n,s){var o,r=t(e);t(s).hasClass(this._unselectableClass)||this._isDisabledDatepicker(r[0])||((o=this._getInst(r[0])).selectedDay=o.currentDay=parseInt(t("a",s).attr("data-date")),o.selectedMonth=o.currentMonth=i,o.selectedYear=o.currentYear=n,this._selectDate(e,this._formatDate(o,o.currentDay,o.currentMonth,o.currentYear)))},_clearDate:function(e){var i=t(e);this._selectDate(i,"")},_selectDate:function(e,i){var n,s=t(e),o=this._getInst(s[0]);i=null!=i?i:this._formatDate(o),o.input&&o.input.val(i),this._updateAlternate(o),(n=this._get(o,"onSelect"))?n.apply(o.input?o.input[0]:null,[i,o]):o.input&&o.input.trigger("change"),o.inline?this._updateDatepicker(o):(this._hideDatepicker(),this._lastInput=o.input[0],"object"!=typeof o.input[0]&&o.input.trigger("focus"),this._lastInput=null)},_updateAlternate:function(e){var i,n,s,o=this._get(e,"altField");o&&(i=this._get(e,"altFormat")||this._get(e,"dateFormat"),n=this._getDate(e),s=this.formatDate(i,n,this._getFormatConfig(e)),t(document).find(o).val(s))},noWeekends:function(t){var e=t.getDay();return[e>0&&e<6,""]},iso8601Week:function(t){var e,i=new Date(t.getTime());return i.setDate(i.getDate()+4-(i.getDay()||7)),e=i.getTime(),i.setMonth(0),i.setDate(1),Math.floor(Math.round((e-i)/864e5)/7)+1},parseDate:function(e,i,n){if(null==e||null==i)throw"Invalid arguments";if(""===(i="object"==typeof i?i.toString():i+""))return null;var s,o,r,a,l=0,h=(n?n.shortYearCutoff:null)||this._defaults.shortYearCutoff,u="string"!=typeof h?h:(new Date).getFullYear()%100+parseInt(h,10),c=(n?n.dayNamesShort:null)||this._defaults.dayNamesShort,d=(n?n.dayNames:null)||this._defaults.dayNames,p=(n?n.monthNamesShort:null)||this._defaults.monthNamesShort,f=(n?n.monthNames:null)||this._defaults.monthNames,g=-1,m=-1,v=-1,_=-1,b=!1,y=function(t){var i=s+1<e.length&&e.charAt(s+1)===t;return i&&s++,i},w=function(t){var e=y(t),n="@"===t?14:"!"===t?20:"y"===t&&e?4:"o"===t?3:2,s=new RegExp("^\\d{"+("y"===t?n:1)+","+n+"}"),o=i.substring(l).match(s);if(!o)throw"Missing number at position "+l;return l+=o[0].length,parseInt(o[0],10)},x=function(e,n,s){var o=-1,r=t.map(y(e)?s:n,(function(t,e){return[[e,t]]})).sort((function(t,e){return-(t[1].length-e[1].length)}));if(t.each(r,(function(t,e){var n=e[1];if(i.substr(l,n.length).toLowerCase()===n.toLowerCase())return o=e[0],l+=n.length,!1})),-1!==o)return o+1;throw"Unknown name at position "+l},C=function(){if(i.charAt(l)!==e.charAt(s))throw"Unexpected literal at position "+l;l++};for(s=0;s<e.length;s++)if(b)"'"!==e.charAt(s)||y("'")?C():b=!1;else switch(e.charAt(s)){case"d":v=w("d");break;case"D":x("D",c,d);break;case"o":_=w("o");break;case"m":m=w("m");break;case"M":m=x("M",p,f);break;case"y":g=w("y");break;case"@":g=(a=new Date(w("@"))).getFullYear(),m=a.getMonth()+1,v=a.getDate();break;case"!":g=(a=new Date((w("!")-this._ticksTo1970)/1e4)).getFullYear(),m=a.getMonth()+1,v=a.getDate();break;case"'":y("'")?C():b=!0;break;default:C()}if(l<i.length&&(r=i.substr(l),!/^\s+/.test(r)))throw"Extra/unparsed characters found in date: "+r;if(-1===g?g=(new Date).getFullYear():g<100&&(g+=(new Date).getFullYear()-(new Date).getFullYear()%100+(g<=u?0:-100)),_>-1)for(m=1,v=_;;){if(v<=(o=this._getDaysInMonth(g,m-1)))break;m++,v-=o}if((a=this._daylightSavingAdjust(new Date(g,m-1,v))).getFullYear()!==g||a.getMonth()+1!==m||a.getDate()!==v)throw"Invalid date";return a},ATOM:"yy-mm-dd",COOKIE:"D, dd M yy",ISO_8601:"yy-mm-dd",RFC_822:"D, d M y",RFC_850:"DD, dd-M-y",RFC_1036:"D, d M y",RFC_1123:"D, d M yy",RFC_2822:"D, d M yy",RSS:"D, d M y",TICKS:"!",TIMESTAMP:"@",W3C:"yy-mm-dd",_ticksTo1970:24*(718685+Math.floor(492.5)-Math.floor(19.7)+Math.floor(4.925))*60*60*1e7,formatDate:function(t,e,i){if(!e)return"";var n,s=(i?i.dayNamesShort:null)||this._defaults.dayNamesShort,o=(i?i.dayNames:null)||this._defaults.dayNames,r=(i?i.monthNamesShort:null)||this._defaults.monthNamesShort,a=(i?i.monthNames:null)||this._defaults.monthNames,l=function(e){var i=n+1<t.length&&t.charAt(n+1)===e;return i&&n++,i},h=function(t,e,i){var n=""+e;if(l(t))for(;n.length<i;)n="0"+n;return n},u=function(t,e,i,n){return l(t)?n[e]:i[e]},c="",d=!1;if(e)for(n=0;n<t.length;n++)if(d)"'"!==t.charAt(n)||l("'")?c+=t.charAt(n):d=!1;else switch(t.charAt(n)){case"d":c+=h("d",e.getDate(),2);break;case"D":c+=u("D",e.getDay(),s,o);break;case"o":c+=h("o",Math.round((new Date(e.getFullYear(),e.getMonth(),e.getDate()).getTime()-new Date(e.getFullYear(),0,0).getTime())/864e5),3);break;case"m":c+=h("m",e.getMonth()+1,2);break;case"M":c+=u("M",e.getMonth(),r,a);break;case"y":c+=l("y")?e.getFullYear():(e.getFullYear()%100<10?"0":"")+e.getFullYear()%100;break;case"@":c+=e.getTime();break;case"!":c+=1e4*e.getTime()+this._ticksTo1970;break;case"'":l("'")?c+="'":d=!0;break;default:c+=t.charAt(n)}return c},_possibleChars:function(t){var e,i="",n=!1,s=function(i){var n=e+1<t.length&&t.charAt(e+1)===i;return n&&e++,n};for(e=0;e<t.length;e++)if(n)"'"!==t.charAt(e)||s("'")?i+=t.charAt(e):n=!1;else switch(t.charAt(e)){case"d":case"m":case"y":case"@":i+="0123456789";break;case"D":case"M":return null;case"'":s("'")?i+="'":n=!0;break;default:i+=t.charAt(e)}return i},_get:function(t,e){return void 0!==t.settings[e]?t.settings[e]:this._defaults[e]},_setDateFromField:function(t,e){if(t.input.val()!==t.lastVal){var i=this._get(t,"dateFormat"),n=t.lastVal=t.input?t.input.val():null,s=this._getDefaultDate(t),o=s,r=this._getFormatConfig(t);try{o=this.parseDate(i,n,r)||s}catch(t){n=e?"":n}t.selectedDay=o.getDate(),t.drawMonth=t.selectedMonth=o.getMonth(),t.drawYear=t.selectedYear=o.getFullYear(),t.currentDay=n?o.getDate():0,t.currentMonth=n?o.getMonth():0,t.currentYear=n?o.getFullYear():0,this._adjustInstDate(t)}},_getDefaultDate:function(t){return this._restrictMinMax(t,this._determineDate(t,this._get(t,"defaultDate"),new Date))},_determineDate:function(e,i,n){var s=function(t){var e=new Date;return e.setDate(e.getDate()+t),e},o=function(i){try{return t.datepicker.parseDate(t.datepicker._get(e,"dateFormat"),i,t.datepicker._getFormatConfig(e))}catch(t){}for(var n=(i.toLowerCase().match(/^c/)?t.datepicker._getDate(e):null)||new Date,s=n.getFullYear(),o=n.getMonth(),r=n.getDate(),a=/([+\-]?[0-9]+)\s*(d|D|w|W|m|M|y|Y)?/g,l=a.exec(i);l;){switch(l[2]||"d"){case"d":case"D":r+=parseInt(l[1],10);break;case"w":case"W":r+=7*parseInt(l[1],10);break;case"m":case"M":o+=parseInt(l[1],10),r=Math.min(r,t.datepicker._getDaysInMonth(s,o));break;case"y":case"Y":s+=parseInt(l[1],10),r=Math.min(r,t.datepicker._getDaysInMonth(s,o))}l=a.exec(i)}return new Date(s,o,r)},r=null==i||""===i?n:"string"==typeof i?o(i):"number"==typeof i?isNaN(i)?n:s(i):new Date(i.getTime());return(r=r&&"Invalid Date"===r.toString()?n:r)&&(r.setHours(0),r.setMinutes(0),r.setSeconds(0),r.setMilliseconds(0)),this._daylightSavingAdjust(r)},_daylightSavingAdjust:function(t){return t?(t.setHours(t.getHours()>12?t.getHours()+2:0),t):null},_setDate:function(t,e,i){var n=!e,s=t.selectedMonth,o=t.selectedYear,r=this._restrictMinMax(t,this._determineDate(t,e,new Date));t.selectedDay=t.currentDay=r.getDate(),t.drawMonth=t.selectedMonth=t.currentMonth=r.getMonth(),t.drawYear=t.selectedYear=t.currentYear=r.getFullYear(),s===t.selectedMonth&&o===t.selectedYear||i||this._notifyChange(t),this._adjustInstDate(t),t.input&&t.input.val(n?"":this._formatDate(t))},_getDate:function(t){return!t.currentYear||t.input&&""===t.input.val()?null:this._daylightSavingAdjust(new Date(t.currentYear,t.currentMonth,t.currentDay))},_attachHandlers:function(e){var i=this._get(e,"stepMonths"),n="#"+e.id.replace(/\\\\/g,"\\");e.dpDiv.find("[data-handler]").map((function(){var e={prev:function(){t.datepicker._adjustDate(n,-i,"M")},next:function(){t.datepicker._adjustDate(n,+i,"M")},hide:function(){t.datepicker._hideDatepicker()},today:function(){t.datepicker._gotoToday(n)},selectDay:function(){return t.datepicker._selectDay(n,+this.getAttribute("data-month"),+this.getAttribute("data-year"),this),!1},selectMonth:function(){return t.datepicker._selectMonthYear(n,this,"M"),!1},selectYear:function(){return t.datepicker._selectMonthYear(n,this,"Y"),!1}};t(this).on(this.getAttribute("data-event"),e[this.getAttribute("data-handler")])}))},_generateHTML:function(e){var i,n,s,o,r,a,l,h,u,c,d,p,f,g,m,v,_,b,y,w,x,C,k,D,T,S,I,P,A,M,E,O,H,F,N,z,W,R,L,j=new Date,q=this._daylightSavingAdjust(new Date(j.getFullYear(),j.getMonth(),j.getDate())),B=this._get(e,"isRTL"),$=this._get(e,"showButtonPanel"),V=this._get(e,"hideIfNoPrevNext"),Y=this._get(e,"navigationAsDateFormat"),U=this._getNumberOfMonths(e),K=this._get(e,"showCurrentAtPos"),X=this._get(e,"stepMonths"),G=1!==U[0]||1!==U[1],J=this._daylightSavingAdjust(e.currentDay?new Date(e.currentYear,e.currentMonth,e.currentDay):new Date(9999,9,9)),Z=this._getMinMaxDate(e,"min"),Q=this._getMinMaxDate(e,"max"),tt=e.drawMonth-K,et=e.drawYear;if(tt<0&&(tt+=12,et--),Q)for(i=this._daylightSavingAdjust(new Date(Q.getFullYear(),Q.getMonth()-U[0]*U[1]+1,Q.getDate())),i=Z&&i<Z?Z:i;this._daylightSavingAdjust(new Date(et,tt,1))>i;)--tt<0&&(tt=11,et--);for(e.drawMonth=tt,e.drawYear=et,n=this._get(e,"prevText"),n=Y?this.formatDate(n,this._daylightSavingAdjust(new Date(et,tt-X,1)),this._getFormatConfig(e)):n,s=this._canAdjustMonth(e,-1,et,tt)?t("<a>").attr({class:"ui-datepicker-prev ui-corner-all","data-handler":"prev","data-event":"click",title:n}).append(t("<span>").addClass("ui-icon ui-icon-circle-triangle-"+(B?"e":"w")).text(n))[0].outerHTML:V?"":t("<a>").attr({class:"ui-datepicker-prev ui-corner-all ui-state-disabled",title:n}).append(t("<span>").addClass("ui-icon ui-icon-circle-triangle-"+(B?"e":"w")).text(n))[0].outerHTML,o=this._get(e,"nextText"),o=Y?this.formatDate(o,this._daylightSavingAdjust(new Date(et,tt+X,1)),this._getFormatConfig(e)):o,r=this._canAdjustMonth(e,1,et,tt)?t("<a>").attr({class:"ui-datepicker-next ui-corner-all","data-handler":"next","data-event":"click",title:o}).append(t("<span>").addClass("ui-icon ui-icon-circle-triangle-"+(B?"w":"e")).text(o))[0].outerHTML:V?"":t("<a>").attr({class:"ui-datepicker-next ui-corner-all ui-state-disabled",title:o}).append(t("<span>").attr("class","ui-icon ui-icon-circle-triangle-"+(B?"w":"e")).text(o))[0].outerHTML,a=this._get(e,"currentText"),l=this._get(e,"gotoCurrent")&&e.currentDay?J:q,a=Y?this.formatDate(a,l,this._getFormatConfig(e)):a,h="",e.inline||(h=t("<button>").attr({type:"button",class:"ui-datepicker-close ui-state-default ui-priority-primary ui-corner-all","data-handler":"hide","data-event":"click"}).text(this._get(e,"closeText"))[0].outerHTML),u="",$&&(u=t("<div class='ui-datepicker-buttonpane ui-widget-content'>").append(B?h:"").append(this._isInRange(e,l)?t("<button>").attr({type:"button",class:"ui-datepicker-current ui-state-default ui-priority-secondary ui-corner-all","data-handler":"today","data-event":"click"}).text(a):"").append(B?"":h)[0].outerHTML),c=parseInt(this._get(e,"firstDay"),10),c=isNaN(c)?0:c,d=this._get(e,"showWeek"),p=this._get(e,"dayNames"),f=this._get(e,"dayNamesMin"),g=this._get(e,"monthNames"),m=this._get(e,"monthNamesShort"),v=this._get(e,"beforeShowDay"),_=this._get(e,"showOtherMonths"),b=this._get(e,"selectOtherMonths"),y=this._getDefaultDate(e),w="",C=0;C<U[0];C++){for(k="",this.maxRows=4,D=0;D<U[1];D++){if(T=this._daylightSavingAdjust(new Date(et,tt,e.selectedDay)),S=" ui-corner-all",I="",G){if(I+="<div class='ui-datepicker-group",U[1]>1)switch(D){case 0:I+=" ui-datepicker-group-first",S=" ui-corner-"+(B?"right":"left");break;case U[1]-1:I+=" ui-datepicker-group-last",S=" ui-corner-"+(B?"left":"right");break;default:I+=" ui-datepicker-group-middle",S=""}I+="'>"}for(I+="<div class='ui-datepicker-header ui-widget-header ui-helper-clearfix"+S+"'>"+(/all|left/.test(S)&&0===C?B?r:s:"")+(/all|right/.test(S)&&0===C?B?s:r:"")+this._generateMonthYearHeader(e,tt,et,Z,Q,C>0||D>0,g,m)+"</div><table class='ui-datepicker-calendar'><thead><tr>",P=d?"<th class='ui-datepicker-week-col'>"+this._get(e,"weekHeader")+"</th>":"",x=0;x<7;x++)P+="<th scope='col'"+((x+c+6)%7>=5?" class='ui-datepicker-week-end'":"")+"><span title='"+p[A=(x+c)%7]+"'>"+f[A]+"</span></th>";for(I+=P+"</tr></thead><tbody>",M=this._getDaysInMonth(et,tt),et===e.selectedYear&&tt===e.selectedMonth&&(e.selectedDay=Math.min(e.selectedDay,M)),E=(this._getFirstDayOfMonth(et,tt)-c+7)%7,O=Math.ceil((E+M)/7),H=G&&this.maxRows>O?this.maxRows:O,this.maxRows=H,F=this._daylightSavingAdjust(new Date(et,tt,1-E)),N=0;N<H;N++){for(I+="<tr>",z=d?"<td class='ui-datepicker-week-col'>"+this._get(e,"calculateWeek")(F)+"</td>":"",x=0;x<7;x++)W=v?v.apply(e.input?e.input[0]:null,[F]):[!0,""],L=(R=F.getMonth()!==tt)&&!b||!W[0]||Z&&F<Z||Q&&F>Q,z+="<td class='"+((x+c+6)%7>=5?" ui-datepicker-week-end":"")+(R?" ui-datepicker-other-month":"")+(F.getTime()===T.getTime()&&tt===e.selectedMonth&&e._keyEvent||y.getTime()===F.getTime()&&y.getTime()===T.getTime()?" "+this._dayOverClass:"")+(L?" "+this._unselectableClass+" ui-state-disabled":"")+(R&&!_?"":" "+W[1]+(F.getTime()===J.getTime()?" "+this._currentClass:"")+(F.getTime()===q.getTime()?" ui-datepicker-today":""))+"'"+(R&&!_||!W[2]?"":" title='"+W[2].replace(/'/g,"&#39;")+"'")+(L?"":" data-handler='selectDay' data-event='click' data-month='"+F.getMonth()+"' data-year='"+F.getFullYear()+"'")+">"+(R&&!_?"&#xa0;":L?"<span class='ui-state-default'>"+F.getDate()+"</span>":"<a class='ui-state-default"+(F.getTime()===q.getTime()?" ui-state-highlight":"")+(F.getTime()===J.getTime()?" ui-state-active":"")+(R?" ui-priority-secondary":"")+"' href='#' aria-current='"+(F.getTime()===J.getTime()?"true":"false")+"' data-date='"+F.getDate()+"'>"+F.getDate()+"</a>")+"</td>",F.setDate(F.getDate()+1),F=this._daylightSavingAdjust(F);I+=z+"</tr>"}++tt>11&&(tt=0,et++),k+=I+="</tbody></table>"+(G?"</div>"+(U[0]>0&&D===U[1]-1?"<div class='ui-datepicker-row-break'></div>":""):"")}w+=k}return w+=u,e._keyEvent=!1,w},_generateMonthYearHeader:function(t,e,i,n,s,o,r,a){var l,h,u,c,d,p,f,g,m=this._get(t,"changeMonth"),v=this._get(t,"changeYear"),_=this._get(t,"showMonthAfterYear"),b=this._get(t,"selectMonthLabel"),y=this._get(t,"selectYearLabel"),w="<div class='ui-datepicker-title'>",x="";if(o||!m)x+="<span class='ui-datepicker-month'>"+r[e]+"</span>";else{for(l=n&&n.getFullYear()===i,h=s&&s.getFullYear()===i,x+="<select class='ui-datepicker-month' aria-label='"+b+"' data-handler='selectMonth' data-event='change'>",u=0;u<12;u++)(!l||u>=n.getMonth())&&(!h||u<=s.getMonth())&&(x+="<option value='"+u+"'"+(u===e?" selected='selected'":"")+">"+a[u]+"</option>");x+="</select>"}if(_||(w+=x+(!o&&m&&v?"":"&#xa0;")),!t.yearshtml)if(t.yearshtml="",o||!v)w+="<span class='ui-datepicker-year'>"+i+"</span>";else{for(c=this._get(t,"yearRange").split(":"),d=(new Date).getFullYear(),p=function(t){var e=t.match(/c[+\-].*/)?i+parseInt(t.substring(1),10):t.match(/[+\-].*/)?d+parseInt(t,10):parseInt(t,10);return isNaN(e)?d:e},f=p(c[0]),g=Math.max(f,p(c[1]||"")),f=n?Math.max(f,n.getFullYear()):f,g=s?Math.min(g,s.getFullYear()):g,t.yearshtml+="<select class='ui-datepicker-year' aria-label='"+y+"' data-handler='selectYear' data-event='change'>";f<=g;f++)t.yearshtml+="<option value='"+f+"'"+(f===i?" selected='selected'":"")+">"+f+"</option>";t.yearshtml+="</select>",w+=t.yearshtml,t.yearshtml=null}return w+=this._get(t,"yearSuffix"),_&&(w+=(!o&&m&&v?"":"&#xa0;")+x),w+="</div>"},_adjustInstDate:function(t,e,i){var n=t.selectedYear+("Y"===i?e:0),s=t.selectedMonth+("M"===i?e:0),o=Math.min(t.selectedDay,this._getDaysInMonth(n,s))+("D"===i?e:0),r=this._restrictMinMax(t,this._daylightSavingAdjust(new Date(n,s,o)));t.selectedDay=r.getDate(),t.drawMonth=t.selectedMonth=r.getMonth(),t.drawYear=t.selectedYear=r.getFullYear(),"M"!==i&&"Y"!==i||this._notifyChange(t)},_restrictMinMax:function(t,e){var i=this._getMinMaxDate(t,"min"),n=this._getMinMaxDate(t,"max"),s=i&&e<i?i:e;return n&&s>n?n:s},_notifyChange:function(t){var e=this._get(t,"onChangeMonthYear");e&&e.apply(t.input?t.input[0]:null,[t.selectedYear,t.selectedMonth+1,t])},_getNumberOfMonths:function(t){var e=this._get(t,"numberOfMonths");return null==e?[1,1]:"number"==typeof e?[1,e]:e},_getMinMaxDate:function(t,e){return this._determineDate(t,this._get(t,e+"Date"),null)},_getDaysInMonth:function(t,e){return 32-this._daylightSavingAdjust(new Date(t,e,32)).getDate()},_getFirstDayOfMonth:function(t,e){return new Date(t,e,1).getDay()},_canAdjustMonth:function(t,e,i,n){var s=this._getNumberOfMonths(t),o=this._daylightSavingAdjust(new Date(i,n+(e<0?e:s[0]*s[1]),1));return e<0&&o.setDate(this._getDaysInMonth(o.getFullYear(),o.getMonth())),this._isInRange(t,o)},_isInRange:function(t,e){var i,n,s=this._getMinMaxDate(t,"min"),o=this._getMinMaxDate(t,"max"),r=null,a=null,l=this._get(t,"yearRange");return l&&(i=l.split(":"),n=(new Date).getFullYear(),r=parseInt(i[0],10),a=parseInt(i[1],10),i[0].match(/[+\-].*/)&&(r+=n),i[1].match(/[+\-].*/)&&(a+=n)),(!s||e.getTime()>=s.getTime())&&(!o||e.getTime()<=o.getTime())&&(!r||e.getFullYear()>=r)&&(!a||e.getFullYear()<=a)},_getFormatConfig:function(t){var e=this._get(t,"shortYearCutoff");return{shortYearCutoff:e="string"!=typeof e?e:(new Date).getFullYear()%100+parseInt(e,10),dayNamesShort:this._get(t,"dayNamesShort"),dayNames:this._get(t,"dayNames"),monthNamesShort:this._get(t,"monthNamesShort"),monthNames:this._get(t,"monthNames")}},_formatDate:function(t,e,i,n){e||(t.currentDay=t.selectedDay,t.currentMonth=t.selectedMonth,t.currentYear=t.selectedYear);var s=e?"object"==typeof e?e:this._daylightSavingAdjust(new Date(n,i,e)):this._daylightSavingAdjust(new Date(t.currentYear,t.currentMonth,t.currentDay));return this.formatDate(this._get(t,"dateFormat"),s,this._getFormatConfig(t))}}),t.fn.datepicker=function(e){if(!this.length)return this;t.datepicker.initialized||(t(document).on("mousedown",t.datepicker._checkExternalClick),t.datepicker.initialized=!0),0===t("#"+t.datepicker._mainDivId).length&&t("body").append(t.datepicker.dpDiv);var i=Array.prototype.slice.call(arguments,1);return"string"!=typeof e||"isDisabled"!==e&&"getDate"!==e&&"widget"!==e?"option"===e&&2===arguments.length&&"string"==typeof arguments[1]?t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this[0]].concat(i)):this.each((function(){"string"==typeof e?t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this].concat(i)):t.datepicker._attachDatepicker(this,e)})):t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this[0]].concat(i))},t.datepicker=new P,t.datepicker.initialized=!1,t.datepicker.uuid=(new Date).getTime(),t.datepicker.version="1.14.1";t.datepicker;var O=!1;t(document).on("mouseup",(function(){O=!1}));t.widget("ui.mouse",{version:"1.14.1",options:{cancel:"input, textarea, button, select, option",distance:1,delay:0},_mouseInit:function(){var e=this;this.element.on("mousedown."+this.widgetName,(function(t){return e._mouseDown(t)})).on("click."+this.widgetName,(function(i){if(!0===t.data(i.target,e.widgetName+".preventClickEvent"))return t.removeData(i.target,e.widgetName+".preventClickEvent"),i.stopImmediatePropagation(),!1})),this.started=!1},_mouseDestroy:function(){this.element.off("."+this.widgetName),this._mouseMoveDelegate&&this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(e){if(!O){this._mouseMoved=!1,this._mouseStarted&&this._mouseUp(e),this._mouseDownEvent=e;var i=this,n=1===e.which,s="string"==typeof this.options.cancel&&t(e.target).closest(this.options.cancel).length;return!(n&&!s&&this._mouseCapture(e))||(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout((function(){i.mouseDelayMet=!0}),this.options.delay)),this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(e),!this._mouseStarted)?(e.preventDefault(),!0):(!0===t.data(e.target,this.widgetName+".preventClickEvent")&&t.removeData(e.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(t){return i._mouseMove(t)},this._mouseUpDelegate=function(t){return i._mouseUp(t)},this.document.on("mousemove."+this.widgetName,this._mouseMoveDelegate).on("mouseup."+this.widgetName,this._mouseUpDelegate),e.preventDefault(),O=!0,!0))}},_mouseMove:function(t){if(this._mouseMoved&&!t.which)if(t.originalEvent.altKey||t.originalEvent.ctrlKey||t.originalEvent.metaKey||t.originalEvent.shiftKey)this.ignoreMissingWhich=!0;else if(!this.ignoreMissingWhich)return this._mouseUp(t);return(t.which||t.button)&&(this._mouseMoved=!0),this._mouseStarted?(this._mouseDrag(t),t.preventDefault()):(this._mouseDistanceMet(t)&&this._mouseDelayMet(t)&&(this._mouseStarted=!1!==this._mouseStart(this._mouseDownEvent,t),this._mouseStarted?this._mouseDrag(t):this._mouseUp(t)),!this._mouseStarted)},_mouseUp:function(e){this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,e.target===this._mouseDownEvent.target&&t.data(e.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(e)),this._mouseDelayTimer&&(clearTimeout(this._mouseDelayTimer),delete this._mouseDelayTimer),this.ignoreMissingWhich=!1,O=!1,e.preventDefault()},_mouseDistanceMet:function(t){return Math.max(Math.abs(this._mouseDownEvent.pageX-t.pageX),Math.abs(this._mouseDownEvent.pageY-t.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}}),t.ui.plugin={add:function(e,i,n){var s,o=t.ui[e].prototype;for(s in n)o.plugins[s]=o.plugins[s]||[],o.plugins[s].push([i,n[s]])},call:function(t,e,i,n){var s,o=t.plugins[e];if(o&&(n||t.element[0].parentNode&&11!==t.element[0].parentNode.nodeType))for(s=0;s<o.length;s++)t.options[o[s][0]]&&o[s][1].apply(t.element,i)}};t.widget("ui.draggable",t.ui.mouse,{version:"1.14.1",widgetEventPrefix:"drag",options:{addClasses:!0,appendTo:"parent",axis:!1,connectToSortable:!1,containment:!1,cursor:"auto",cursorAt:!1,grid:!1,handle:!1,helper:"original",iframeFix:!1,opacity:!1,refreshPositions:!1,revert:!1,revertDuration:500,scope:"default",scroll:!0,scrollSensitivity:20,scrollSpeed:20,snap:!1,snapMode:"both",snapTolerance:20,stack:!1,zIndex:!1,drag:null,start:null,stop:null},_create:function(){"original"===this.options.helper&&this._setPositionRelative(),this.options.addClasses&&this._addClass("ui-draggable"),this._setHandleClassName(),this._mouseInit()},_setOption:function(t,e){this._super(t,e),"handle"===t&&(this._removeHandleClassName(),this._setHandleClassName())},_destroy:function(){(this.helper||this.element).is(".ui-draggable-dragging")?this.destroyOnClear=!0:(this._removeHandleClassName(),this._mouseDestroy())},_mouseCapture:function(e){var i=this.options;return!(this.helper||i.disabled||t(e.target).closest(".ui-resizable-handle").length>0)&&(this.handle=this._getHandle(e),!!this.handle&&(this._blurActiveElement(e),this._blockFrames(!0===i.iframeFix?"iframe":i.iframeFix),!0))},_blockFrames:function(e){this.iframeBlocks=this.document.find(e).map((function(){var e=t(this);return t("<div>").css("position","absolute").appendTo(e.parent()).outerWidth(e.outerWidth()).outerHeight(e.outerHeight()).offset(e.offset())[0]}))},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_blurActiveElement:function(e){var i=this.document[0].activeElement;t(e.target).closest(i).length||t(i).trigger("blur")},_mouseStart:function(e){var i=this.options;return this.helper=this._createHelper(e),this._addClass(this.helper,"ui-draggable-dragging"),this._cacheHelperProportions(),t.ui.ddmanager&&(t.ui.ddmanager.current=this),this._cacheMargins(),this.cssPosition=this.helper.css("position"),this.scrollParent=this.helper.scrollParent(!0),this.offsetParent=this.helper.offsetParent(),this.hasFixedAncestor=this.helper.parents().filter((function(){return"fixed"===t(this).css("position")})).length>0,this.positionAbs=this.element.offset(),this._refreshOffsets(e),this.originalPosition=this.position=this._generatePosition(e,!1),this.originalPageX=e.pageX,this.originalPageY=e.pageY,i.cursorAt&&this._adjustOffsetFromHelper(i.cursorAt),this._setContainment(),!1===this._trigger("start",e)?(this._clear(),!1):(this._cacheHelperProportions(),t.ui.ddmanager&&!i.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e),this._mouseDrag(e,!0),t.ui.ddmanager&&t.ui.ddmanager.dragStart(this,e),!0)},_refreshOffsets:function(t){this.offset={top:this.positionAbs.top-this.margins.top,left:this.positionAbs.left-this.margins.left,scroll:!1,parent:this._getParentOffset(),relative:this._getRelativeOffset()},this.offset.click={left:t.pageX-this.offset.left,top:t.pageY-this.offset.top}},_mouseDrag:function(e,i){if(this.hasFixedAncestor&&(this.offset.parent=this._getParentOffset()),this.position=this._generatePosition(e,!0),this.positionAbs=this._convertPositionTo("absolute"),!i){var n=this._uiHash();if(!1===this._trigger("drag",e,n))return this._mouseUp(new t.Event("mouseup",e)),!1;this.position=n.position}return this.helper[0].style.left=this.position.left+"px",this.helper[0].style.top=this.position.top+"px",t.ui.ddmanager&&t.ui.ddmanager.drag(this,e),!1},_mouseStop:function(e){var i=this,n=!1;return t.ui.ddmanager&&!this.options.dropBehaviour&&(n=t.ui.ddmanager.drop(this,e)),this.dropped&&(n=this.dropped,this.dropped=!1),"invalid"===this.options.revert&&!n||"valid"===this.options.revert&&n||!0===this.options.revert||"function"==typeof this.options.revert&&this.options.revert.call(this.element,n)?t(this.helper).animate(this.originalPosition,parseInt(this.options.revertDuration,10),(function(){!1!==i._trigger("stop",e)&&i._clear()})):!1!==this._trigger("stop",e)&&this._clear(),!1},_mouseUp:function(e){return this._unblockFrames(),t.ui.ddmanager&&t.ui.ddmanager.dragStop(this,e),this.handleElement.is(e.target)&&this.element.trigger("focus"),t.ui.mouse.prototype._mouseUp.call(this,e)},cancel:function(){return this.helper.is(".ui-draggable-dragging")?this._mouseUp(new t.Event("mouseup",{target:this.element[0]})):this._clear(),this},_getHandle:function(e){return!this.options.handle||!!t(e.target).closest(this.element.find(this.options.handle)).length},_setHandleClassName:function(){this.handleElement=this.options.handle?this.element.find(this.options.handle):this.element,this._addClass(this.handleElement,"ui-draggable-handle")},_removeHandleClassName:function(){this._removeClass(this.handleElement,"ui-draggable-handle")},_createHelper:function(e){var i=this.options,n="function"==typeof i.helper,s=n?t(i.helper.apply(this.element[0],[e])):"clone"===i.helper?this.element.clone().removeAttr("id"):this.element;return s.parents("body").length||s.appendTo("parent"===i.appendTo?this.element[0].parentNode:i.appendTo),n&&s[0]===this.element[0]&&this._setPositionRelative(),s[0]===this.element[0]||/(fixed|absolute)/.test(s.css("position"))||s.css("position","absolute"),s},_setPositionRelative:function(){/^(?:r|a|f)/.test(this.element.css("position"))||(this.element[0].style.position="relative")},_adjustOffsetFromHelper:function(t){"string"==typeof t&&(t=t.split(" ")),Array.isArray(t)&&(t={left:+t[0],top:+t[1]||0}),"left"in t&&(this.offset.click.left=t.left+this.margins.left),"right"in t&&(this.offset.click.left=this.helperProportions.width-t.right+this.margins.left),"top"in t&&(this.offset.click.top=t.top+this.margins.top),"bottom"in t&&(this.offset.click.top=this.helperProportions.height-t.bottom+this.margins.top)},_isRootNode:function(t){return/(html|body)/i.test(t.tagName)||t===this.document[0]},_getParentOffset:function(){var e=this.offsetParent.offset(),i=this.document[0];return"absolute"===this.cssPosition&&this.scrollParent[0]!==i&&t.contains(this.scrollParent[0],this.offsetParent[0])&&(e.left+=this.scrollParent.scrollLeft(),e.top+=this.scrollParent.scrollTop()),this._isRootNode(this.offsetParent[0])&&(e={top:0,left:0}),{top:e.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:e.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"!==this.cssPosition)return{top:0,left:0};var t=this.element.position(),e=this._isRootNode(this.scrollParent[0]);return{top:t.top-(parseInt(this.helper.css("top"),10)||0)+(e?0:this.scrollParent.scrollTop()),left:t.left-(parseInt(this.helper.css("left"),10)||0)+(e?0:this.scrollParent.scrollLeft())}},_cacheMargins:function(){this.margins={left:parseInt(this.element.css("marginLeft"),10)||0,top:parseInt(this.element.css("marginTop"),10)||0,right:parseInt(this.element.css("marginRight"),10)||0,bottom:parseInt(this.element.css("marginBottom"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var e,i,n,s=this.options,o=this.document[0];this.relativeContainer=null,s.containment?"window"!==s.containment?"document"!==s.containment?s.containment.constructor!==Array?("parent"===s.containment&&(s.containment=this.helper[0].parentNode),(n=(i=t(s.containment))[0])&&(e=/(scroll|auto)/.test(i.css("overflow")),this.containment=[(parseInt(i.css("borderLeftWidth"),10)||0)+(parseInt(i.css("paddingLeft"),10)||0),(parseInt(i.css("borderTopWidth"),10)||0)+(parseInt(i.css("paddingTop"),10)||0),(e?Math.max(n.scrollWidth,n.offsetWidth):n.offsetWidth)-(parseInt(i.css("borderRightWidth"),10)||0)-(parseInt(i.css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left-this.margins.right,(e?Math.max(n.scrollHeight,n.offsetHeight):n.offsetHeight)-(parseInt(i.css("borderBottomWidth"),10)||0)-(parseInt(i.css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top-this.margins.bottom],this.relativeContainer=i)):this.containment=s.containment:this.containment=[0,0,t(o).width()-this.helperProportions.width-this.margins.left,(t(o).height()||o.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]:this.containment=[t(window).scrollLeft()-this.offset.relative.left-this.offset.parent.left,t(window).scrollTop()-this.offset.relative.top-this.offset.parent.top,t(window).scrollLeft()+t(window).width()-this.helperProportions.width-this.margins.left,t(window).scrollTop()+(t(window).height()||o.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]:this.containment=null},_convertPositionTo:function(t,e){e||(e=this.position);var i="absolute"===t?1:-1,n=this._isRootNode(this.scrollParent[0]);return{top:e.top+this.offset.relative.top*i+this.offset.parent.top*i-("fixed"===this.cssPosition?-this.offset.scroll.top:n?0:this.offset.scroll.top)*i,left:e.left+this.offset.relative.left*i+this.offset.parent.left*i-("fixed"===this.cssPosition?-this.offset.scroll.left:n?0:this.offset.scroll.left)*i}},_generatePosition:function(t,e){var i,n,s,o,r=this.options,a=this._isRootNode(this.scrollParent[0]),l=t.pageX,h=t.pageY;return a&&this.offset.scroll||(this.offset.scroll={top:this.scrollParent.scrollTop(),left:this.scrollParent.scrollLeft()}),e&&(this.containment&&(this.relativeContainer?(n=this.relativeContainer.offset(),i=[this.containment[0]+n.left,this.containment[1]+n.top,this.containment[2]+n.left,this.containment[3]+n.top]):i=this.containment,t.pageX-this.offset.click.left<i[0]&&(l=i[0]+this.offset.click.left),t.pageY-this.offset.click.top<i[1]&&(h=i[1]+this.offset.click.top),t.pageX-this.offset.click.left>i[2]&&(l=i[2]+this.offset.click.left),t.pageY-this.offset.click.top>i[3]&&(h=i[3]+this.offset.click.top)),r.grid&&(s=r.grid[1]?this.originalPageY+Math.round((h-this.originalPageY)/r.grid[1])*r.grid[1]:this.originalPageY,h=i?s-this.offset.click.top>=i[1]||s-this.offset.click.top>i[3]?s:s-this.offset.click.top>=i[1]?s-r.grid[1]:s+r.grid[1]:s,o=r.grid[0]?this.originalPageX+Math.round((l-this.originalPageX)/r.grid[0])*r.grid[0]:this.originalPageX,l=i?o-this.offset.click.left>=i[0]||o-this.offset.click.left>i[2]?o:o-this.offset.click.left>=i[0]?o-r.grid[0]:o+r.grid[0]:o),"y"===r.axis&&(l=this.originalPageX),"x"===r.axis&&(h=this.originalPageY)),{top:h-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.offset.scroll.top:a?0:this.offset.scroll.top),left:l-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.offset.scroll.left:a?0:this.offset.scroll.left)}},_clear:function(){this._removeClass(this.helper,"ui-draggable-dragging"),this.helper[0]===this.element[0]||this.cancelHelperRemoval||this.helper.remove(),this.helper=null,this.cancelHelperRemoval=!1,this.destroyOnClear&&this.destroy()},_trigger:function(e,i,n){return n=n||this._uiHash(),t.ui.plugin.call(this,e,[i,n,this],!0),/^(drag|start|stop)/.test(e)&&(this.positionAbs=this._convertPositionTo("absolute"),n.offset=this.positionAbs),t.Widget.prototype._trigger.call(this,e,i,n)},plugins:{},_uiHash:function(){return{helper:this.helper,position:this.position,originalPosition:this.originalPosition,offset:this.positionAbs}}}),t.ui.plugin.add("draggable","connectToSortable",{start:function(e,i,n){var s=t.extend({},i,{item:n.element});n.sortables=[],t(n.options.connectToSortable).each((function(){var i=t(this).sortable("instance");i&&!i.options.disabled&&(n.sortables.push(i),i.refreshPositions(),i._trigger("activate",e,s))}))},stop:function(e,i,n){var s=t.extend({},i,{item:n.element});n.cancelHelperRemoval=!1,t.each(n.sortables,(function(){var t=this;t.isOver?(t.isOver=0,n.cancelHelperRemoval=!0,t.cancelHelperRemoval=!1,t._storedCSS={position:t.placeholder.css("position"),top:t.placeholder.css("top"),left:t.placeholder.css("left")},t._mouseStop(e),t.options.helper=t.options._helper):(t.cancelHelperRemoval=!0,t._trigger("deactivate",e,s))}))},drag:function(e,i,n){t.each(n.sortables,(function(){var s=!1,o=this;o.positionAbs=n.positionAbs,o.helperProportions=n.helperProportions,o.offset.click=n.offset.click,o._intersectsWith(o.containerCache)&&(s=!0,t.each(n.sortables,(function(){return this.positionAbs=n.positionAbs,this.helperProportions=n.helperProportions,this.offset.click=n.offset.click,this!==o&&this._intersectsWith(this.containerCache)&&t.contains(o.element[0],this.element[0])&&(s=!1),s}))),s?(o.isOver||(o.isOver=1,n._parent=i.helper.parent(),o.currentItem=i.helper.appendTo(o.element).data("ui-sortable-item",!0),o.options._helper=o.options.helper,o.options.helper=function(){return i.helper[0]},e.target=o.currentItem[0],o._mouseCapture(e,!0),o._mouseStart(e,!0,!0),o.offset.click.top=n.offset.click.top,o.offset.click.left=n.offset.click.left,o.offset.parent.left-=n.offset.parent.left-o.offset.parent.left,o.offset.parent.top-=n.offset.parent.top-o.offset.parent.top,n._trigger("toSortable",e),n.dropped=o.element,t.each(n.sortables,(function(){this.refreshPositions()})),n.currentItem=n.element,o.fromOutside=n),o.currentItem&&(o._mouseDrag(e),i.position=o.position)):o.isOver&&(o.isOver=0,o.cancelHelperRemoval=!0,o.options._revert=o.options.revert,o.options.revert=!1,o._trigger("out",e,o._uiHash(o)),o._mouseStop(e,!0),o.options.revert=o.options._revert,o.options.helper=o.options._helper,o.placeholder&&o.placeholder.remove(),i.helper.appendTo(n._parent),n._refreshOffsets(e),i.position=n._generatePosition(e,!0),n._trigger("fromSortable",e),n.dropped=!1,t.each(n.sortables,(function(){this.refreshPositions()})))}))}}),t.ui.plugin.add("draggable","cursor",{start:function(e,i,n){var s=t("body"),o=n.options;s.css("cursor")&&(o._cursor=s.css("cursor")),s.css("cursor",o.cursor)},stop:function(e,i,n){var s=n.options;s._cursor&&t("body").css("cursor",s._cursor)}}),t.ui.plugin.add("draggable","opacity",{start:function(e,i,n){var s=t(i.helper),o=n.options;s.css("opacity")&&(o._opacity=s.css("opacity")),s.css("opacity",o.opacity)},stop:function(e,i,n){var s=n.options;s._opacity&&t(i.helper).css("opacity",s._opacity)}}),t.ui.plugin.add("draggable","scroll",{start:function(t,e,i){i.scrollParentNotHidden||(i.scrollParentNotHidden=i.helper.scrollParent(!1)),i.scrollParentNotHidden[0]!==i.document[0]&&"HTML"!==i.scrollParentNotHidden[0].tagName&&(i.overflowOffset=i.scrollParentNotHidden.offset())},drag:function(e,i,n){var s=n.options,o=!1,r=n.scrollParentNotHidden[0],a=n.document[0];r!==a&&"HTML"!==r.tagName?(s.axis&&"x"===s.axis||(n.overflowOffset.top+r.offsetHeight-e.pageY<s.scrollSensitivity?r.scrollTop=o=r.scrollTop+s.scrollSpeed:e.pageY-n.overflowOffset.top<s.scrollSensitivity&&(r.scrollTop=o=r.scrollTop-s.scrollSpeed)),s.axis&&"y"===s.axis||(n.overflowOffset.left+r.offsetWidth-e.pageX<s.scrollSensitivity?r.scrollLeft=o=r.scrollLeft+s.scrollSpeed:e.pageX-n.overflowOffset.left<s.scrollSensitivity&&(r.scrollLeft=o=r.scrollLeft-s.scrollSpeed))):(s.axis&&"x"===s.axis||(e.pageY-t(a).scrollTop()<s.scrollSensitivity?o=t(a).scrollTop(t(a).scrollTop()-s.scrollSpeed):t(window).height()-(e.pageY-t(a).scrollTop())<s.scrollSensitivity&&(o=t(a).scrollTop(t(a).scrollTop()+s.scrollSpeed))),s.axis&&"y"===s.axis||(e.pageX-t(a).scrollLeft()<s.scrollSensitivity?o=t(a).scrollLeft(t(a).scrollLeft()-s.scrollSpeed):t(window).width()-(e.pageX-t(a).scrollLeft())<s.scrollSensitivity&&(o=t(a).scrollLeft(t(a).scrollLeft()+s.scrollSpeed)))),!1!==o&&t.ui.ddmanager&&!s.dropBehaviour&&t.ui.ddmanager.prepareOffsets(n,e)}}),t.ui.plugin.add("draggable","snap",{start:function(e,i,n){var s=n.options;n.snapElements=[],t(s.snap.constructor!==String?s.snap.items||":data(ui-draggable)":s.snap).each((function(){var e=t(this),i=e.offset();this!==n.element[0]&&n.snapElements.push({item:this,width:e.outerWidth(),height:e.outerHeight(),top:i.top,left:i.left})}))},drag:function(e,i,n){var s,o,r,a,l,h,u,c,d,p,f=n.options,g=f.snapTolerance,m=i.offset.left,v=m+n.helperProportions.width,_=i.offset.top,b=_+n.helperProportions.height;for(d=n.snapElements.length-1;d>=0;d--)h=(l=n.snapElements[d].left-n.margins.left)+n.snapElements[d].width,c=(u=n.snapElements[d].top-n.margins.top)+n.snapElements[d].height,v<l-g||m>h+g||b<u-g||_>c+g||!t.contains(n.snapElements[d].item.ownerDocument,n.snapElements[d].item)?(n.snapElements[d].snapping&&n.options.snap.release&&n.options.snap.release.call(n.element,e,t.extend(n._uiHash(),{snapItem:n.snapElements[d].item})),n.snapElements[d].snapping=!1):("inner"!==f.snapMode&&(s=Math.abs(u-b)<=g,o=Math.abs(c-_)<=g,r=Math.abs(l-v)<=g,a=Math.abs(h-m)<=g,s&&(i.position.top=n._convertPositionTo("relative",{top:u-n.helperProportions.height,left:0}).top),o&&(i.position.top=n._convertPositionTo("relative",{top:c,left:0}).top),r&&(i.position.left=n._convertPositionTo("relative",{top:0,left:l-n.helperProportions.width}).left),a&&(i.position.left=n._convertPositionTo("relative",{top:0,left:h}).left)),p=s||o||r||a,"outer"!==f.snapMode&&(s=Math.abs(u-_)<=g,o=Math.abs(c-b)<=g,r=Math.abs(l-m)<=g,a=Math.abs(h-v)<=g,s&&(i.position.top=n._convertPositionTo("relative",{top:u,left:0}).top),o&&(i.position.top=n._convertPositionTo("relative",{top:c-n.helperProportions.height,left:0}).top),r&&(i.position.left=n._convertPositionTo("relative",{top:0,left:l}).left),a&&(i.position.left=n._convertPositionTo("relative",{top:0,left:h-n.helperProportions.width}).left)),!n.snapElements[d].snapping&&(s||o||r||a||p)&&n.options.snap.snap&&n.options.snap.snap.call(n.element,e,t.extend(n._uiHash(),{snapItem:n.snapElements[d].item})),n.snapElements[d].snapping=s||o||r||a||p)}}),t.ui.plugin.add("draggable","stack",{start:function(e,i,n){var s,o=n.options,r=t.makeArray(t(o.stack)).sort((function(e,i){return(parseInt(t(e).css("zIndex"),10)||0)-(parseInt(t(i).css("zIndex"),10)||0)}));r.length&&(s=parseInt(t(r[0]).css("zIndex"),10)||0,t(r).each((function(e){t(this).css("zIndex",s+e)})),this.css("zIndex",s+r.length))}}),t.ui.plugin.add("draggable","zIndex",{start:function(e,i,n){var s=t(i.helper),o=n.options;s.css("zIndex")&&(o._zIndex=s.css("zIndex")),s.css("zIndex",o.zIndex)},stop:function(e,i,n){var s=n.options;s._zIndex&&t(i.helper).css("zIndex",s._zIndex)}});t.ui.draggable;t.widget("ui.resizable",t.ui.mouse,{version:"1.14.1",widgetEventPrefix:"resize",options:{alsoResize:!1,animate:!1,animateDuration:"slow",animateEasing:"swing",aspectRatio:!1,autoHide:!1,classes:{"ui-resizable-se":"ui-icon ui-icon-gripsmall-diagonal-se"},containment:!1,ghost:!1,grid:!1,handles:"e,s,se",helper:!1,maxHeight:null,maxWidth:null,minHeight:10,minWidth:10,zIndex:90,resize:null,start:null,stop:null},_num:function(t){return parseFloat(t)||0},_isNumber:function(t){return!isNaN(parseFloat(t))},_hasScroll:function(e,i){var n,s=!1,o=t(e).css("overflow");if("hidden"===o)return!1;if("scroll"===o)return!0;if(e[n=i&&"left"===i?"scrollLeft":"scrollTop"]>0)return!0;try{e[n]=1,s=e[n]>0,e[n]=0}catch(t){}return s},_create:function(){var e,i=this.options,n=this;this._addClass("ui-resizable"),t.extend(this,{_aspectRatio:!!i.aspectRatio,aspectRatio:i.aspectRatio,originalElement:this.element,_proportionallyResizeElements:[],_helper:i.helper||i.ghost||i.animate?i.helper||"ui-resizable-helper":null}),this.element[0].nodeName.match(/^(canvas|textarea|input|select|button|img)$/i)&&(this.element.wrap(t("<div class='ui-wrapper'></div>").css({overflow:"hidden",position:this.element.css("position"),width:this.element.outerWidth(),height:this.element.outerHeight(),top:this.element.css("top"),left:this.element.css("left")})),this.element=this.element.parent().data("ui-resizable",this.element.resizable("instance")),this.elementIsWrapper=!0,e={marginTop:this.originalElement.css("marginTop"),marginRight:this.originalElement.css("marginRight"),marginBottom:this.originalElement.css("marginBottom"),marginLeft:this.originalElement.css("marginLeft")},this.element.css(e),this.originalResizeStyle=this.originalElement.css("resize"),this.originalElement.css("resize","none"),this._proportionallyResizeElements.push(this.originalElement.css({position:"static",zoom:1,display:"block"})),this._proportionallyResize()),this._setupHandles(),i.autoHide&&t(this.element).on("mouseenter",(function(){i.disabled||(n._removeClass("ui-resizable-autohide"),n._handles.show())})).on("mouseleave",(function(){i.disabled||n.resizing||(n._addClass("ui-resizable-autohide"),n._handles.hide())})),this._mouseInit()},_destroy:function(){this._mouseDestroy(),this._addedHandles.remove();var e,i=function(e){t(e).removeData("resizable").removeData("ui-resizable").off(".resizable")};return this.elementIsWrapper&&(i(this.element),e=this.element,this.originalElement.css({position:e.css("position"),width:e.outerWidth(),height:e.outerHeight(),top:e.css("top"),left:e.css("left")}).insertAfter(e),e.remove()),this.originalElement.css("resize",this.originalResizeStyle),i(this.originalElement),this},_setOption:function(t,e){switch(this._super(t,e),t){case"handles":this._removeHandles(),this._setupHandles();break;case"aspectRatio":this._aspectRatio=!!e}},_setupHandles:function(){var e,i,n,s,o,r=this.options,a=this;if(this.handles=r.handles||(t(".ui-resizable-handle",this.element).length?{n:".ui-resizable-n",e:".ui-resizable-e",s:".ui-resizable-s",w:".ui-resizable-w",se:".ui-resizable-se",sw:".ui-resizable-sw",ne:".ui-resizable-ne",nw:".ui-resizable-nw"}:"e,s,se"),this._handles=t(),this._addedHandles=t(),this.handles.constructor===String)for("all"===this.handles&&(this.handles="n,e,s,w,se,sw,ne,nw"),n=this.handles.split(","),this.handles={},i=0;i<n.length;i++)s="ui-resizable-"+(e=String.prototype.trim.call(n[i])),o=t("<div>"),this._addClass(o,"ui-resizable-handle "+s),o.css({zIndex:r.zIndex}),this.handles[e]=".ui-resizable-"+e,this.element.children(this.handles[e]).length||(this.element.append(o),this._addedHandles=this._addedHandles.add(o));this._renderAxis=function(e){var i,n,s,o;for(i in e=e||this.element,this.handles)this.handles[i].constructor===String?this.handles[i]=this.element.children(this.handles[i]).first().show():(this.handles[i].jquery||this.handles[i].nodeType)&&(this.handles[i]=t(this.handles[i]),this._on(this.handles[i],{mousedown:a._mouseDown})),this.elementIsWrapper&&this.originalElement[0].nodeName.match(/^(textarea|input|select|button)$/i)&&(n=t(this.handles[i],this.element),o=/sw|ne|nw|se|n|s/.test(i)?n.outerHeight():n.outerWidth(),s=["padding",/ne|nw|n/.test(i)?"Top":/se|sw|s/.test(i)?"Bottom":/^e$/.test(i)?"Right":"Left"].join(""),e.css(s,o),this._proportionallyResize()),this._handles=this._handles.add(this.handles[i])},this._renderAxis(this.element),this._handles=this._handles.add(this.element.find(".ui-resizable-handle")),this._handles.disableSelection(),this._handles.on("mouseover",(function(){a.resizing||(this.className&&(o=this.className.match(/ui-resizable-(se|sw|ne|nw|n|e|s|w)/i)),a.axis=o&&o[1]?o[1]:"se")})),r.autoHide&&(this._handles.hide(),this._addClass("ui-resizable-autohide"))},_removeHandles:function(){this._addedHandles.remove()},_mouseCapture:function(e){var i,n,s=!1;for(i in this.handles)((n=t(this.handles[i])[0])===e.target||t.contains(n,e.target))&&(s=!0);return!this.options.disabled&&s},_mouseStart:function(e){var i,n,s,o,r=this.options,a=this.element;return this.resizing=!0,this._renderProxy(),i=this._num(this.helper.css("left")),n=this._num(this.helper.css("top")),r.containment&&(i+=t(r.containment).scrollLeft()||0,n+=t(r.containment).scrollTop()||0),this.offset=this.helper.offset(),this.position={left:i,top:n},this._helper||(o=this._calculateAdjustedElementDimensions(a)),this.size=this._helper?{width:this.helper.width(),height:this.helper.height()}:{width:o.width,height:o.height},this.originalSize=this._helper?{width:a.outerWidth(),height:a.outerHeight()}:{width:o.width,height:o.height},this.sizeDiff={width:a.outerWidth()-a.width(),height:a.outerHeight()-a.height()},this.originalPosition={left:i,top:n},this.originalMousePosition={left:e.pageX,top:e.pageY},this.aspectRatio="number"==typeof r.aspectRatio?r.aspectRatio:this.originalSize.width/this.originalSize.height||1,s=t(".ui-resizable-"+this.axis).css("cursor"),t("body").css("cursor","auto"===s?this.axis+"-resize":s),this._addClass("ui-resizable-resizing"),this._propagate("start",e),!0},_mouseDrag:function(e){var i,n,s=this.originalMousePosition,o=this.axis,r=e.pageX-s.left||0,a=e.pageY-s.top||0,l=this._change[o];return this._updatePrevProperties(),!!l&&(i=l.apply(this,[e,r,a]),this._updateVirtualBoundaries(e.shiftKey),(this._aspectRatio||e.shiftKey)&&(i=this._updateRatio(i,e)),i=this._respectSize(i,e),this._updateCache(i),this._propagate("resize",e),n=this._applyChanges(),!this._helper&&this._proportionallyResizeElements.length&&this._proportionallyResize(),t.isEmptyObject(n)||(this._updatePrevProperties(),this._trigger("resize",e,this.ui()),this._applyChanges()),!1)},_mouseStop:function(e){this.resizing=!1;var i,n,s,o,r,a,l,h=this.options,u=this;return this._helper&&(s=(n=(i=this._proportionallyResizeElements).length&&/textarea/i.test(i[0].nodeName))&&this._hasScroll(i[0],"left")?0:u.sizeDiff.height,o=n?0:u.sizeDiff.width,r={width:u.helper.width()-o,height:u.helper.height()-s},a=parseFloat(u.element.css("left"))+(u.position.left-u.originalPosition.left)||null,l=parseFloat(u.element.css("top"))+(u.position.top-u.originalPosition.top)||null,h.animate||this.element.css(t.extend(r,{top:l,left:a})),u.helper.height(u.size.height),u.helper.width(u.size.width),this._helper&&!h.animate&&this._proportionallyResize()),t("body").css("cursor","auto"),this._removeClass("ui-resizable-resizing"),this._propagate("stop",e),this._helper&&this.helper.remove(),!1},_updatePrevProperties:function(){this.prevPosition={top:this.position.top,left:this.position.left},this.prevSize={width:this.size.width,height:this.size.height}},_applyChanges:function(){var t={};return this.position.top!==this.prevPosition.top&&(t.top=this.position.top+"px"),this.position.left!==this.prevPosition.left&&(t.left=this.position.left+"px"),this.helper.css(t),this.size.width!==this.prevSize.width&&(t.width=this.size.width+"px",this.helper.width(t.width)),this.size.height!==this.prevSize.height&&(t.height=this.size.height+"px",this.helper.height(t.height)),t},_updateVirtualBoundaries:function(t){var e,i,n,s,o,r=this.options;o={minWidth:this._isNumber(r.minWidth)?r.minWidth:0,maxWidth:this._isNumber(r.maxWidth)?r.maxWidth:1/0,minHeight:this._isNumber(r.minHeight)?r.minHeight:0,maxHeight:this._isNumber(r.maxHeight)?r.maxHeight:1/0},(this._aspectRatio||t)&&(e=o.minHeight*this.aspectRatio,n=o.minWidth/this.aspectRatio,i=o.maxHeight*this.aspectRatio,s=o.maxWidth/this.aspectRatio,e>o.minWidth&&(o.minWidth=e),n>o.minHeight&&(o.minHeight=n),i<o.maxWidth&&(o.maxWidth=i),s<o.maxHeight&&(o.maxHeight=s)),this._vBoundaries=o},_updateCache:function(t){this.offset=this.helper.offset(),this._isNumber(t.left)&&(this.position.left=t.left),this._isNumber(t.top)&&(this.position.top=t.top),this._isNumber(t.height)&&(this.size.height=t.height),this._isNumber(t.width)&&(this.size.width=t.width)},_updateRatio:function(t){var e=this.position,i=this.size,n=this.axis;return this._isNumber(t.height)?t.width=t.height*this.aspectRatio:this._isNumber(t.width)&&(t.height=t.width/this.aspectRatio),"sw"===n&&(t.left=e.left+(i.width-t.width),t.top=null),"nw"===n&&(t.top=e.top+(i.height-t.height),t.left=e.left+(i.width-t.width)),t},_respectSize:function(t){var e=this._vBoundaries,i=this.axis,n=this._isNumber(t.width)&&e.maxWidth&&e.maxWidth<t.width,s=this._isNumber(t.height)&&e.maxHeight&&e.maxHeight<t.height,o=this._isNumber(t.width)&&e.minWidth&&e.minWidth>t.width,r=this._isNumber(t.height)&&e.minHeight&&e.minHeight>t.height,a=this.originalPosition.left+this.originalSize.width,l=this.originalPosition.top+this.originalSize.height,h=/sw|nw|w/.test(i),u=/nw|ne|n/.test(i);return o&&(t.width=e.minWidth),r&&(t.height=e.minHeight),n&&(t.width=e.maxWidth),s&&(t.height=e.maxHeight),o&&h&&(t.left=a-e.minWidth),n&&h&&(t.left=a-e.maxWidth),r&&u&&(t.top=l-e.minHeight),s&&u&&(t.top=l-e.maxHeight),t.width||t.height||t.left||!t.top?t.width||t.height||t.top||!t.left||(t.left=null):t.top=null,t},_getPaddingPlusBorderDimensions:function(t){for(var e=0,i=[],n=[t.css("borderTopWidth"),t.css("borderRightWidth"),t.css("borderBottomWidth"),t.css("borderLeftWidth")],s=[t.css("paddingTop"),t.css("paddingRight"),t.css("paddingBottom"),t.css("paddingLeft")];e<4;e++)i[e]=parseFloat(n[e])||0,i[e]+=parseFloat(s[e])||0;return{height:i[0]+i[2],width:i[1]+i[3]}},_calculateAdjustedElementDimensions:function(t){var e,i,n,s=t.get(0);return"content-box"!==t.css("box-sizing")||!this._hasScroll(s)&&!this._hasScroll(s,"left")?{height:parseFloat(t.css("height")),width:parseFloat(t.css("width"))}:(e=parseFloat(s.style.width),i=parseFloat(s.style.height),n=this._getPaddingPlusBorderDimensions(t),e=isNaN(e)?this._getElementTheoreticalSize(t,n,"width"):e,{height:i=isNaN(i)?this._getElementTheoreticalSize(t,n,"height"):i,width:e})},_getElementTheoreticalSize:function(t,e,i){return Math.max(0,Math.ceil(t.get(0)["offset"+i[0].toUpperCase()+i.slice(1)]-e[i]-.5))||0},_proportionallyResize:function(){if(this._proportionallyResizeElements.length)for(var t,e=0,i=this.helper||this.element;e<this._proportionallyResizeElements.length;e++)t=this._proportionallyResizeElements[e],this.outerDimensions||(this.outerDimensions=this._getPaddingPlusBorderDimensions(t)),t.css({height:i.height()-this.outerDimensions.height||0,width:i.width()-this.outerDimensions.width||0})},_renderProxy:function(){var e=this.element,i=this.options;this.elementOffset=e.offset(),this._helper?(this.helper=this.helper||t("<div></div>").css({overflow:"hidden"}),this._addClass(this.helper,this._helper),this.helper.css({width:this.element.outerWidth(),height:this.element.outerHeight(),position:"absolute",left:this.elementOffset.left+"px",top:this.elementOffset.top+"px",zIndex:++i.zIndex}),this.helper.appendTo("body").disableSelection()):this.helper=this.element},_change:{e:function(t,e){return{width:this.originalSize.width+e}},w:function(t,e){var i=this.originalSize;return{left:this.originalPosition.left+e,width:i.width-e}},n:function(t,e,i){var n=this.originalSize;return{top:this.originalPosition.top+i,height:n.height-i}},s:function(t,e,i){return{height:this.originalSize.height+i}},se:function(e,i,n){return t.extend(this._change.s.apply(this,arguments),this._change.e.apply(this,[e,i,n]))},sw:function(e,i,n){return t.extend(this._change.s.apply(this,arguments),this._change.w.apply(this,[e,i,n]))},ne:function(e,i,n){return t.extend(this._change.n.apply(this,arguments),this._change.e.apply(this,[e,i,n]))},nw:function(e,i,n){return t.extend(this._change.n.apply(this,arguments),this._change.w.apply(this,[e,i,n]))}},_propagate:function(e,i){t.ui.plugin.call(this,e,[i,this.ui()]),"resize"!==e&&this._trigger(e,i,this.ui())},plugins:{},ui:function(){return{originalElement:this.originalElement,element:this.element,helper:this.helper,position:this.position,size:this.size,originalSize:this.originalSize,originalPosition:this.originalPosition}}}),t.ui.plugin.add("resizable","animate",{stop:function(e){var i=t(this).resizable("instance"),n=i.options,s=i._proportionallyResizeElements,o=s.length&&/textarea/i.test(s[0].nodeName),r=o&&i._hasScroll(s[0],"left")?0:i.sizeDiff.height,a=o?0:i.sizeDiff.width,l={width:i.size.width-a,height:i.size.height-r},h=parseFloat(i.element.css("left"))+(i.position.left-i.originalPosition.left)||null,u=parseFloat(i.element.css("top"))+(i.position.top-i.originalPosition.top)||null;i.element.animate(t.extend(l,u&&h?{top:u,left:h}:{}),{duration:n.animateDuration,easing:n.animateEasing,step:function(){var n={width:parseFloat(i.element.css("width")),height:parseFloat(i.element.css("height")),top:parseFloat(i.element.css("top")),left:parseFloat(i.element.css("left"))};s&&s.length&&t(s[0]).css({width:n.width,height:n.height}),i._updateCache(n),i._propagate("resize",e)}})}}),t.ui.plugin.add("resizable","containment",{start:function(){var e,i,n,s,o,r,a,l=t(this).resizable("instance"),h=l.options,u=l.element,c=h.containment,d=c instanceof t?c.get(0):/parent/.test(c)?u.parent().get(0):c;d&&(l.containerElement=t(d),/document/.test(c)||c===document?(l.containerOffset={left:0,top:0},l.containerPosition={left:0,top:0},l.parentData={element:t(document),left:0,top:0,width:t(document).width(),height:t(document).height()||document.body.parentNode.scrollHeight}):(e=t(d),i=[],t(["Top","Right","Left","Bottom"]).each((function(t,n){i[t]=l._num(e.css("padding"+n))})),l.containerOffset=e.offset(),l.containerPosition=e.position(),l.containerSize={height:e.innerHeight()-i[3],width:e.innerWidth()-i[1]},n=l.containerOffset,s=l.containerSize.height,o=l.containerSize.width,r=l._hasScroll(d,"left")?d.scrollWidth:o,a=l._hasScroll(d)?d.scrollHeight:s,l.parentData={element:d,left:n.left,top:n.top,width:r,height:a}))},resize:function(e){var i,n,s,o,r=t(this).resizable("instance"),a=r.options,l=r.containerOffset,h=r.position,u=r._aspectRatio||e.shiftKey,c={top:0,left:0},d=r.containerElement,p=!0;d[0]!==document&&/static/.test(d.css("position"))&&(c=l),h.left<(r._helper?l.left:0)&&(r.size.width=r.size.width+(r._helper?r.position.left-l.left:r.position.left-c.left),u&&(r.size.height=r.size.width/r.aspectRatio,p=!1),r.position.left=a.helper?l.left:0),h.top<(r._helper?l.top:0)&&(r.size.height=r.size.height+(r._helper?r.position.top-l.top:r.position.top),u&&(r.size.width=r.size.height*r.aspectRatio,p=!1),r.position.top=r._helper?l.top:0),s=r.containerElement.get(0)===r.element.parent().get(0),o=/relative|absolute/.test(r.containerElement.css("position")),s&&o?(r.offset.left=r.parentData.left+r.position.left,r.offset.top=r.parentData.top+r.position.top):(r.offset.left=r.element.offset().left,r.offset.top=r.element.offset().top),i=Math.abs(r.sizeDiff.width+(r._helper?r.offset.left-c.left:r.offset.left-l.left)),n=Math.abs(r.sizeDiff.height+(r._helper?r.offset.top-c.top:r.offset.top-l.top)),i+r.size.width>=r.parentData.width&&(r.size.width=r.parentData.width-i,u&&(r.size.height=r.size.width/r.aspectRatio,p=!1)),n+r.size.height>=r.parentData.height&&(r.size.height=r.parentData.height-n,u&&(r.size.width=r.size.height*r.aspectRatio,p=!1)),p||(r.position.left=r.prevPosition.left,r.position.top=r.prevPosition.top,r.size.width=r.prevSize.width,r.size.height=r.prevSize.height)},stop:function(){var e=t(this).resizable("instance"),i=e.options,n=e.containerOffset,s=e.containerPosition,o=e.containerElement,r=t(e.helper),a=r.offset(),l=r.outerWidth()-e.sizeDiff.width,h=r.outerHeight()-e.sizeDiff.height;e._helper&&!i.animate&&/relative/.test(o.css("position"))&&t(this).css({left:a.left-s.left-n.left,width:l,height:h}),e._helper&&!i.animate&&/static/.test(o.css("position"))&&t(this).css({left:a.left-s.left-n.left,width:l,height:h})}}),t.ui.plugin.add("resizable","alsoResize",{start:function(){var e=t(this).resizable("instance"),i=e.options;t(i.alsoResize).each((function(){var i=t(this),n=e._calculateAdjustedElementDimensions(i);i.data("ui-resizable-alsoresize",{width:n.width,height:n.height,left:parseFloat(i.css("left")),top:parseFloat(i.css("top"))})}))},resize:function(e,i){var n=t(this).resizable("instance"),s=n.options,o=n.originalSize,r=n.originalPosition,a={height:n.size.height-o.height||0,width:n.size.width-o.width||0,top:n.position.top-r.top||0,left:n.position.left-r.left||0};t(s.alsoResize).each((function(){var e=t(this),n=t(this).data("ui-resizable-alsoresize"),s={},o=e.parents(i.originalElement[0]).length?["width","height"]:["width","height","top","left"];t.each(o,(function(t,e){var i=(n[e]||0)+(a[e]||0);i&&i>=0&&(s[e]=i||null)})),e.css(s)}))},stop:function(){t(this).removeData("ui-resizable-alsoresize")}}),t.ui.plugin.add("resizable","ghost",{start:function(){var e=t(this).resizable("instance"),i=e.size;e.ghost=e.originalElement.clone(),e.ghost.css({opacity:.25,display:"block",position:"relative",height:i.height,width:i.width,margin:0,left:0,top:0}),e._addClass(e.ghost,"ui-resizable-ghost"),!0===t.uiBackCompat&&"string"==typeof e.options.ghost&&e.ghost.addClass(this.options.ghost),e.ghost.appendTo(e.helper)},resize:function(){var e=t(this).resizable("instance");e.ghost&&e.ghost.css({position:"relative",height:e.size.height,width:e.size.width})},stop:function(){var e=t(this).resizable("instance");e.ghost&&e.helper&&e.helper.get(0).removeChild(e.ghost.get(0))}}),t.ui.plugin.add("resizable","grid",{resize:function(){var e,i=t(this).resizable("instance"),n=i.options,s=i.size,o=i.originalSize,r=i.originalPosition,a=i.axis,l="number"==typeof n.grid?[n.grid,n.grid]:n.grid,h=l[0]||1,u=l[1]||1,c=Math.round((s.width-o.width)/h)*h,d=Math.round((s.height-o.height)/u)*u,p=o.width+c,f=o.height+d,g=n.maxWidth&&n.maxWidth<p,m=n.maxHeight&&n.maxHeight<f,v=n.minWidth&&n.minWidth>p,_=n.minHeight&&n.minHeight>f;n.grid=l,v&&(p+=h),_&&(f+=u),g&&(p-=h),m&&(f-=u),/^(se|s|e)$/.test(a)?(i.size.width=p,i.size.height=f):/^(ne)$/.test(a)?(i.size.width=p,i.size.height=f,i.position.top=r.top-d):/^(sw)$/.test(a)?(i.size.width=p,i.size.height=f,i.position.left=r.left-c):((f-u<=0||p-h<=0)&&(e=i._getPaddingPlusBorderDimensions(this)),f-u>0?(i.size.height=f,i.position.top=r.top-d):(f=u-e.height,i.size.height=f,i.position.top=r.top+o.height-f),p-h>0?(i.size.width=p,i.position.left=r.left-c):(p=h-e.width,i.size.width=p,i.position.left=r.left+o.width-p))}});t.ui.resizable;t.widget("ui.dialog",{version:"1.14.1",options:{appendTo:"body",autoOpen:!0,buttons:[],classes:{"ui-dialog":"ui-corner-all","ui-dialog-titlebar":"ui-corner-all"},closeOnEscape:!0,closeText:"Close",draggable:!0,hide:null,height:"auto",maxHeight:null,maxWidth:null,minHeight:150,minWidth:150,modal:!1,position:{my:"center",at:"center",of:window,collision:"fit",using:function(e){var i=t(this).css(e).offset().top;i<0&&t(this).css("top",e.top-i)}},resizable:!0,show:null,title:null,uiDialogTitleHeadingLevel:0,width:300,beforeClose:null,close:null,drag:null,dragStart:null,dragStop:null,focus:null,open:null,resize:null,resizeStart:null,resizeStop:null},sizeRelatedOptions:{buttons:!0,height:!0,maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0,width:!0},resizableRelatedOptions:{maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0},_create:function(){this.originalCss={display:this.element[0].style.display,width:this.element[0].style.width,minHeight:this.element[0].style.minHeight,maxHeight:this.element[0].style.maxHeight,height:this.element[0].style.height},this.originalPosition={parent:this.element.parent(),index:this.element.parent().children().index(this.element)},this.originalTitle=this.element.attr("title"),null==this.options.title&&null!=this.originalTitle&&(this.options.title=this.originalTitle),this.options.disabled&&(this.options.disabled=!1),this._createWrapper(),this.element.show().removeAttr("title").appendTo(this.uiDialog),this._addClass("ui-dialog-content","ui-widget-content"),this._createTitlebar(),this._createButtonPane(),this.options.draggable&&t.fn.draggable&&this._makeDraggable(),this.options.resizable&&t.fn.resizable&&this._makeResizable(),this._isOpen=!1,this._trackFocus()},_init:function(){this.options.autoOpen&&this.open()},_appendTo:function(){var e=this.options.appendTo;return e&&(e.jquery||e.nodeType)?t(e):this.document.find(e||"body").eq(0)},_destroy:function(){var t,e=this.originalPosition;this._untrackInstance(),this._destroyOverlay(),this.element.removeUniqueId().css(this.originalCss).detach(),this.uiDialog.remove(),this.originalTitle&&this.element.attr("title",this.originalTitle),(t=e.parent.children().eq(e.index)).length&&t[0]!==this.element[0]?t.before(this.element):e.parent.append(this.element)},widget:function(){return this.uiDialog},disable:t.noop,enable:t.noop,close:function(e){var i=this;this._isOpen&&!1!==this._trigger("beforeClose",e)&&(this._isOpen=!1,this._focusedElement=null,this._destroyOverlay(),this._untrackInstance(),this.opener.filter(":focusable").trigger("focus").length||t(this.document[0].activeElement).trigger("blur"),this._hide(this.uiDialog,this.options.hide,(function(){i._trigger("close",e)})))},isOpen:function(){return this._isOpen},moveToTop:function(){this._moveToTop()},_moveToTop:function(e,i){var n=!1,s=this.uiDialog.siblings(".ui-front:visible").map((function(){return+t(this).css("z-index")})).get(),o=Math.max.apply(null,s);return o>=+this.uiDialog.css("z-index")&&(this.uiDialog.css("z-index",o+1),n=!0),n&&!i&&this._trigger("focus",e),n},open:function(){var e=this;this._isOpen?this._moveToTop()&&this._focusTabbable():(this._isOpen=!0,this.opener=t(this.document[0].activeElement),this._size(),this._position(),this._createOverlay(),this._moveToTop(null,!0),this.overlay&&this.overlay.css("z-index",this.uiDialog.css("z-index")-1),this._show(this.uiDialog,this.options.show,(function(){e._focusTabbable(),e._trigger("focus")})),this._makeFocusTarget(),this._trigger("open"))},_focusTabbable:function(){var t=this._focusedElement;t||(t=this.element.find("[autofocus]")),t.length||(t=this.element.find(":tabbable")),t.length||(t=this.uiDialogButtonPane.find(":tabbable")),t.length||(t=this.uiDialogTitlebarClose.filter(":tabbable")),t.length||(t=this.uiDialog),t.eq(0).trigger("focus")},_restoreTabbableFocus:function(){var e=this.document[0].activeElement;this.uiDialog[0]===e||t.contains(this.uiDialog[0],e)||this._focusTabbable()},_keepFocus:function(t){t.preventDefault(),this._restoreTabbableFocus()},_createWrapper:function(){this.uiDialog=t("<div>").hide().attr({tabIndex:-1,role:"dialog","aria-modal":this.options.modal?"true":null}).appendTo(this._appendTo()),this._addClass(this.uiDialog,"ui-dialog","ui-widget ui-widget-content ui-front"),this._on(this.uiDialog,{keydown:function(e){if(this.options.closeOnEscape&&!e.isDefaultPrevented()&&e.keyCode&&e.keyCode===t.ui.keyCode.ESCAPE)return e.preventDefault(),void this.close(e);if(e.keyCode===t.ui.keyCode.TAB&&!e.isDefaultPrevented()){var i=this.uiDialog.find(":tabbable"),n=i.first(),s=i.last();e.target!==s[0]&&e.target!==this.uiDialog[0]||e.shiftKey?e.target!==n[0]&&e.target!==this.uiDialog[0]||!e.shiftKey||(this._delay((function(){s.trigger("focus")})),e.preventDefault()):(this._delay((function(){n.trigger("focus")})),e.preventDefault())}},mousedown:function(t){this._moveToTop(t)&&this._focusTabbable()}}),this.element.find("[aria-describedby]").length||this.uiDialog.attr({"aria-describedby":this.element.uniqueId().attr("id")})},_createTitlebar:function(){var e;this.uiDialogTitlebar=t("<div>"),this._addClass(this.uiDialogTitlebar,"ui-dialog-titlebar","ui-widget-header ui-helper-clearfix"),this._on(this.uiDialogTitlebar,{mousedown:function(e){t(e.target).closest(".ui-dialog-titlebar-close")||this.uiDialog.trigger("focus")}}),this.uiDialogTitlebarClose=t("<button type='button'></button>").button({label:t("<a>").text(this.options.closeText).html(),icon:"ui-icon-closethick",showLabel:!1}).appendTo(this.uiDialogTitlebar),this._addClass(this.uiDialogTitlebarClose,"ui-dialog-titlebar-close"),this._on(this.uiDialogTitlebarClose,{click:function(t){t.preventDefault(),this.close(t)}});var i=Number.isInteger(this.options.uiDialogTitleHeadingLevel)&&this.options.uiDialogTitleHeadingLevel>0&&this.options.uiDialogTitleHeadingLevel<=6?"h"+this.options.uiDialogTitleHeadingLevel:"span";e=t("<"+i+">").uniqueId().prependTo(this.uiDialogTitlebar),this._addClass(e,"ui-dialog-title"),this._title(e),this.uiDialogTitlebar.prependTo(this.uiDialog),this.uiDialog.attr({"aria-labelledby":e.attr("id")})},_title:function(t){this.options.title?t.text(this.options.title):t.html("&#160;")},_createButtonPane:function(){this.uiDialogButtonPane=t("<div>"),this._addClass(this.uiDialogButtonPane,"ui-dialog-buttonpane","ui-widget-content ui-helper-clearfix"),this.uiButtonSet=t("<div>").appendTo(this.uiDialogButtonPane),this._addClass(this.uiButtonSet,"ui-dialog-buttonset"),this._createButtons()},_createButtons:function(){var e=this,i=this.options.buttons;this.uiDialogButtonPane.remove(),this.uiButtonSet.empty(),t.isEmptyObject(i)||Array.isArray(i)&&!i.length?this._removeClass(this.uiDialog,"ui-dialog-buttons"):(t.each(i,(function(i,n){var s,o;n="function"==typeof n?{click:n,text:i}:n,n=t.extend({type:"button"},n),s=n.click,o={icon:n.icon,iconPosition:n.iconPosition,showLabel:n.showLabel,icons:n.icons,text:n.text},delete n.click,delete n.icon,delete n.iconPosition,delete n.showLabel,delete n.icons,"boolean"==typeof n.text&&delete n.text,t("<button></button>",n).button(o).appendTo(e.uiButtonSet).on("click",(function(){s.apply(e.element[0],arguments)}))})),this._addClass(this.uiDialog,"ui-dialog-buttons"),this.uiDialogButtonPane.appendTo(this.uiDialog))},_makeDraggable:function(){var e=this,i=this.options;function n(t){return{position:t.position,offset:t.offset}}this.uiDialog.draggable({cancel:".ui-dialog-content, .ui-dialog-titlebar-close",handle:".ui-dialog-titlebar",containment:"document",start:function(i,s){e._addClass(t(this),"ui-dialog-dragging"),e._blockFrames(),e._trigger("dragStart",i,n(s))},drag:function(t,i){e._trigger("drag",t,n(i))},stop:function(s,o){var r=o.offset.left-e.document.scrollLeft(),a=o.offset.top-e.document.scrollTop();i.position={my:"left top",at:"left"+(r>=0?"+":"")+r+" top"+(a>=0?"+":"")+a,of:e.window},e._removeClass(t(this),"ui-dialog-dragging"),e._unblockFrames(),e._trigger("dragStop",s,n(o))}})},_makeResizable:function(){var e=this,i=this.options,n=i.resizable,s=this.uiDialog.css("position"),o="string"==typeof n?n:"n,e,s,w,se,sw,ne,nw";function r(t){return{originalPosition:t.originalPosition,originalSize:t.originalSize,position:t.position,size:t.size}}this.uiDialog.resizable({cancel:".ui-dialog-content",containment:"document",alsoResize:this.element,maxWidth:i.maxWidth,maxHeight:i.maxHeight,minWidth:i.minWidth,minHeight:this._minHeight(),handles:o,start:function(i,n){e._addClass(t(this),"ui-dialog-resizing"),e._blockFrames(),e._trigger("resizeStart",i,r(n))},resize:function(t,i){e._trigger("resize",t,r(i))},stop:function(n,s){var o=e.uiDialog.offset(),a=o.left-e.document.scrollLeft(),l=o.top-e.document.scrollTop();i.height=e.uiDialog.height(),i.width=e.uiDialog.width(),i.position={my:"left top",at:"left"+(a>=0?"+":"")+a+" top"+(l>=0?"+":"")+l,of:e.window},e._removeClass(t(this),"ui-dialog-resizing"),e._unblockFrames(),e._trigger("resizeStop",n,r(s))}}).css("position",s)},_trackFocus:function(){this._on(this.widget(),{focusin:function(e){this._makeFocusTarget(),this._focusedElement=t(e.target)}})},_makeFocusTarget:function(){this._untrackInstance(),this._trackingInstances().unshift(this)},_untrackInstance:function(){var e=this._trackingInstances(),i=t.inArray(this,e);-1!==i&&e.splice(i,1)},_trackingInstances:function(){var t=this.document.data("ui-dialog-instances");return t||(t=[],this.document.data("ui-dialog-instances",t)),t},_minHeight:function(){var t=this.options;return"auto"===t.height?t.minHeight:Math.min(t.minHeight,t.height)},_position:function(){var t=this.uiDialog.is(":visible");t||this.uiDialog.show(),this.uiDialog.position(this.options.position),t||this.uiDialog.hide()},_setOptions:function(e){var i=this,n=!1,s={};t.each(e,(function(t,e){i._setOption(t,e),t in i.sizeRelatedOptions&&(n=!0),t in i.resizableRelatedOptions&&(s[t]=e)})),n&&(this._size(),this._position()),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option",s)},_setOption:function(e,i){var n,s,o=this.uiDialog;"disabled"!==e&&(this._super(e,i),"appendTo"===e&&this.uiDialog.appendTo(this._appendTo()),"buttons"===e&&this._createButtons(),"closeText"===e&&this.uiDialogTitlebarClose.button({label:t("<a>").text(""+this.options.closeText).html()}),"draggable"===e&&((n=o.is(":data(ui-draggable)"))&&!i&&o.draggable("destroy"),!n&&i&&this._makeDraggable()),"position"===e&&this._position(),"resizable"===e&&((s=o.is(":data(ui-resizable)"))&&!i&&o.resizable("destroy"),s&&"string"==typeof i&&o.resizable("option","handles",i),s||!1===i||this._makeResizable()),"title"===e&&this._title(this.uiDialogTitlebar.find(".ui-dialog-title")),"modal"===e&&o.attr("aria-modal",i?"true":null))},_size:function(){var t,e,i,n=this.options;this.element.show().css({width:"auto",minHeight:0,maxHeight:"none",height:0}),n.minWidth>n.width&&(n.width=n.minWidth),t=this.uiDialog.css({height:"auto",width:n.width}).outerHeight(),e=Math.max(0,n.minHeight-t),i="number"==typeof n.maxHeight?Math.max(0,n.maxHeight-t):"none","auto"===n.height?this.element.css({minHeight:e,maxHeight:i,height:"auto"}):this.element.height(Math.max(0,n.height-t)),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option","minHeight",this._minHeight())},_blockFrames:function(){this.iframeBlocks=this.document.find("iframe").map((function(){var e=t(this);return t("<div>").css({position:"absolute",width:e.outerWidth(),height:e.outerHeight()}).appendTo(e.parent()).offset(e.offset())[0]}))},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_allowInteraction:function(e){return!!t(e.target).closest(".ui-dialog").length||!!t(e.target).closest(".ui-datepicker").length},_createOverlay:function(){if(this.options.modal){var e=!0;this._delay((function(){e=!1})),this.document.data("ui-dialog-overlays")||this.document.on("focusin.ui-dialog",function(t){if(!e){var i=this._trackingInstances()[0];i._allowInteraction(t)||(t.preventDefault(),i._focusTabbable())}}.bind(this)),this.overlay=t("<div>").appendTo(this._appendTo()),this._addClass(this.overlay,null,"ui-widget-overlay ui-front"),this._on(this.overlay,{mousedown:"_keepFocus"}),this.document.data("ui-dialog-overlays",(this.document.data("ui-dialog-overlays")||0)+1)}},_destroyOverlay:function(){if(this.options.modal&&this.overlay){var t=this.document.data("ui-dialog-overlays")-1;t?this.document.data("ui-dialog-overlays",t):(this.document.off("focusin.ui-dialog"),this.document.removeData("ui-dialog-overlays")),this.overlay.remove(),this.overlay=null}}}),!0===t.uiBackCompat&&t.widget("ui.dialog",t.ui.dialog,{options:{dialogClass:""},_createWrapper:function(){this._super(),this.uiDialog.addClass(this.options.dialogClass)},_setOption:function(t,e){"dialogClass"===t&&this.uiDialog.removeClass(this.options.dialogClass).addClass(e),this._superApply(arguments)}});t.ui.dialog;t.widget("ui.droppable",{version:"1.14.1",widgetEventPrefix:"drop",options:{accept:"*",addClasses:!0,greedy:!1,scope:"default",tolerance:"intersect",activate:null,deactivate:null,drop:null,out:null,over:null},_create:function(){var t,e=this.options,i=e.accept;this.isover=!1,this.isout=!0,this.accept="function"==typeof i?i:function(t){return t.is(i)},this.proportions=function(){if(!arguments.length)return t||(t={width:this.element[0].offsetWidth,height:this.element[0].offsetHeight});t=arguments[0]},this._addToManager(e.scope),e.addClasses&&this._addClass("ui-droppable")},_addToManager:function(e){t.ui.ddmanager.droppables[e]=t.ui.ddmanager.droppables[e]||[],t.ui.ddmanager.droppables[e].push(this)},_splice:function(t){for(var e=0;e<t.length;e++)t[e]===this&&t.splice(e,1)},_destroy:function(){var e=t.ui.ddmanager.droppables[this.options.scope];this._splice(e)},_setOption:function(e,i){if("accept"===e)this.accept="function"==typeof i?i:function(t){return t.is(i)};else if("scope"===e){var n=t.ui.ddmanager.droppables[this.options.scope];this._splice(n),this._addToManager(i)}this._super(e,i)},_activate:function(e){var i=t.ui.ddmanager.current;this._addActiveClass(),i&&this._trigger("activate",e,this.ui(i))},_deactivate:function(e){var i=t.ui.ddmanager.current;this._removeActiveClass(),i&&this._trigger("deactivate",e,this.ui(i))},_over:function(e){var i=t.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this._addHoverClass(),this._trigger("over",e,this.ui(i)))},_out:function(e){var i=t.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this._removeHoverClass(),this._trigger("out",e,this.ui(i)))},_drop:function(e,i){var n=i||t.ui.ddmanager.current,s=!1;return!(!n||(n.currentItem||n.element)[0]===this.element[0])&&(this.element.find(":data(ui-droppable)").not(".ui-draggable-dragging").each((function(){var i=t(this).droppable("instance");if(i.options.greedy&&!i.options.disabled&&i.options.scope===n.options.scope&&i.accept.call(i.element[0],n.currentItem||n.element)&&t.ui.intersect(n,t.extend(i,{offset:i.element.offset()}),i.options.tolerance,e))return s=!0,!1})),!s&&(!!this.accept.call(this.element[0],n.currentItem||n.element)&&(this._removeActiveClass(),this._removeHoverClass(),this._trigger("drop",e,this.ui(n)),this.element)))},ui:function(t){return{draggable:t.currentItem||t.element,helper:t.helper,position:t.position,offset:t.positionAbs}},_addHoverClass:function(){this._addClass("ui-droppable-hover")},_removeHoverClass:function(){this._removeClass("ui-droppable-hover")},_addActiveClass:function(){this._addClass("ui-droppable-active")},_removeActiveClass:function(){this._removeClass("ui-droppable-active")}}),t.ui.intersect=function(){function t(t,e,i){return t>=e&&t<e+i}return function(e,i,n,s){if(!i.offset)return!1;var o=(e.positionAbs||e.position.absolute).left+e.margins.left,r=(e.positionAbs||e.position.absolute).top+e.margins.top,a=o+e.helperProportions.width,l=r+e.helperProportions.height,h=i.offset.left,u=i.offset.top,c=h+i.proportions().width,d=u+i.proportions().height;switch(n){case"fit":return h<=o&&a<=c&&u<=r&&l<=d;case"intersect":return h<o+e.helperProportions.width/2&&a-e.helperProportions.width/2<c&&u<r+e.helperProportions.height/2&&l-e.helperProportions.height/2<d;case"pointer":return t(s.pageY,u,i.proportions().height)&&t(s.pageX,h,i.proportions().width);case"touch":return(r>=u&&r<=d||l>=u&&l<=d||r<u&&l>d)&&(o>=h&&o<=c||a>=h&&a<=c||o<h&&a>c);default:return!1}}}(),t.ui.ddmanager={current:null,droppables:{default:[]},prepareOffsets:function(e,i){var n,s,o=t.ui.ddmanager.droppables[e.options.scope]||[],r=i?i.type:null,a=(e.currentItem||e.element).find(":data(ui-droppable)").addBack();t:for(n=0;n<o.length;n++)if(!(o[n].options.disabled||e&&!o[n].accept.call(o[n].element[0],e.currentItem||e.element))){for(s=0;s<a.length;s++)if(a[s]===o[n].element[0]){o[n].proportions().height=0;continue t}o[n].visible="none"!==o[n].element.css("display"),o[n].visible&&("mousedown"===r&&o[n]._activate.call(o[n],i),o[n].offset=o[n].element.offset(),o[n].proportions({width:o[n].element[0].offsetWidth,height:o[n].element[0].offsetHeight}))}},drop:function(e,i){var n=!1;return t.each((t.ui.ddmanager.droppables[e.options.scope]||[]).slice(),(function(){this.options&&(!this.options.disabled&&this.visible&&t.ui.intersect(e,this,this.options.tolerance,i)&&(n=this._drop.call(this,i)||n),!this.options.disabled&&this.visible&&this.accept.call(this.element[0],e.currentItem||e.element)&&(this.isout=!0,this.isover=!1,this._deactivate.call(this,i)))})),n},dragStart:function(e,i){e.element.parentsUntil("body").on("scroll.droppable",(function(){e.options.refreshPositions||t.ui.ddmanager.prepareOffsets(e,i)}))},drag:function(e,i){e.options.refreshPositions&&t.ui.ddmanager.prepareOffsets(e,i),t.each(t.ui.ddmanager.droppables[e.options.scope]||[],(function(){if(!this.options.disabled&&!this.greedyChild&&this.visible){var n,s,o,r=t.ui.intersect(e,this,this.options.tolerance,i),a=!r&&this.isover?"isout":r&&!this.isover?"isover":null;a&&(this.options.greedy&&(s=this.options.scope,(o=this.element.parents(":data(ui-droppable)").filter((function(){return t(this).droppable("instance").options.scope===s}))).length&&((n=t(o[0]).droppable("instance")).greedyChild="isover"===a)),n&&"isover"===a&&(n.isover=!1,n.isout=!0,n._out.call(n,i)),this[a]=!0,this["isout"===a?"isover":"isout"]=!1,this["isover"===a?"_over":"_out"].call(this,i),n&&"isout"===a&&(n.isout=!1,n.isover=!0,n._over.call(n,i)))}}))},dragStop:function(e,i){e.element.parentsUntil("body").off("scroll.droppable"),e.options.refreshPositions||t.ui.ddmanager.prepareOffsets(e,i)}},!0===t.uiBackCompat&&t.widget("ui.droppable",t.ui.droppable,{options:{hoverClass:!1,activeClass:!1},_addActiveClass:function(){this._super(),this.options.activeClass&&this.element.addClass(this.options.activeClass)},_removeActiveClass:function(){this._super(),this.options.activeClass&&this.element.removeClass(this.options.activeClass)},_addHoverClass:function(){this._super(),this.options.hoverClass&&this.element.addClass(this.options.hoverClass)},_removeHoverClass:function(){this._super(),this.options.hoverClass&&this.element.removeClass(this.options.hoverClass)}});t.ui.droppable,t.widget("ui.progressbar",{version:"1.14.1",options:{classes:{"ui-progressbar":"ui-corner-all","ui-progressbar-value":"ui-corner-left","ui-progressbar-complete":"ui-corner-right"},max:100,value:0,change:null,complete:null},min:0,_create:function(){this.oldValue=this.options.value=this._constrainedValue(),this.element.attr({role:"progressbar","aria-valuemin":this.min}),this._addClass("ui-progressbar","ui-widget ui-widget-content"),this.valueDiv=t("<div>").appendTo(this.element),this._addClass(this.valueDiv,"ui-progressbar-value","ui-widget-header"),this._refreshValue()},_destroy:function(){this.element.removeAttr("role aria-valuemin aria-valuemax aria-valuenow"),this.valueDiv.remove()},value:function(t){if(void 0===t)return this.options.value;this.options.value=this._constrainedValue(t),this._refreshValue()},_constrainedValue:function(t){return void 0===t&&(t=this.options.value),this.indeterminate=!1===t,"number"!=typeof t&&(t=0),!this.indeterminate&&Math.min(this.options.max,Math.max(this.min,t))},_setOptions:function(t){var e=t.value;delete t.value,this._super(t),this.options.value=this._constrainedValue(e),this._refreshValue()},_setOption:function(t,e){"max"===t&&(e=Math.max(this.min,e)),this._super(t,e)},_setOptionDisabled:function(t){this._super(t),this.element.attr("aria-disabled",t),this._toggleClass(null,"ui-state-disabled",!!t)},_percentage:function(){return this.indeterminate?100:100*(this.options.value-this.min)/(this.options.max-this.min)},_refreshValue:function(){var e=this.options.value,i=this._percentage();this.valueDiv.toggle(this.indeterminate||e>this.min).width(i.toFixed(0)+"%"),this._toggleClass(this.valueDiv,"ui-progressbar-complete",null,e===this.options.max)._toggleClass("ui-progressbar-indeterminate",null,this.indeterminate),this.indeterminate?(this.element.removeAttr("aria-valuenow"),this.overlayDiv||(this.overlayDiv=t("<div>").appendTo(this.valueDiv),this._addClass(this.overlayDiv,"ui-progressbar-overlay"))):(this.element.attr({"aria-valuemax":this.options.max,"aria-valuenow":e}),this.overlayDiv&&(this.overlayDiv.remove(),this.overlayDiv=null)),this.oldValue!==e&&(this.oldValue=e,this._trigger("change")),e===this.options.max&&this._trigger("complete")}}),t.widget("ui.selectable",t.ui.mouse,{version:"1.14.1",options:{appendTo:"body",autoRefresh:!0,distance:0,filter:"*",tolerance:"touch",selected:null,selecting:null,start:null,stop:null,unselected:null,unselecting:null},_create:function(){var e=this;this._addClass("ui-selectable"),this.dragged=!1,this.refresh=function(){e.elementPos=t(e.element[0]).offset(),e.selectees=t(e.options.filter,e.element[0]),e._addClass(e.selectees,"ui-selectee"),e.selectees.each((function(){var i=t(this),n=i.offset(),s={left:n.left-e.elementPos.left,top:n.top-e.elementPos.top};t.data(this,"selectable-item",{element:this,$element:i,left:s.left,top:s.top,right:s.left+i.outerWidth(),bottom:s.top+i.outerHeight(),startselected:!1,selected:i.hasClass("ui-selected"),selecting:i.hasClass("ui-selecting"),unselecting:i.hasClass("ui-unselecting")})}))},this.refresh(),this._mouseInit(),this.helper=t("<div>"),this._addClass(this.helper,"ui-selectable-helper")},_destroy:function(){this.selectees.removeData("selectable-item"),this._mouseDestroy()},_mouseStart:function(e){var i=this,n=this.options;this.opos=[e.pageX,e.pageY],this.elementPos=t(this.element[0]).offset(),this.options.disabled||(this.selectees=t(n.filter,this.element[0]),this._trigger("start",e),t(n.appendTo).append(this.helper),this.helper.css({left:e.pageX,top:e.pageY,width:0,height:0}),n.autoRefresh&&this.refresh(),this.selectees.filter(".ui-selected").each((function(){var n=t.data(this,"selectable-item");n.startselected=!0,e.metaKey||e.ctrlKey||(i._removeClass(n.$element,"ui-selected"),n.selected=!1,i._addClass(n.$element,"ui-unselecting"),n.unselecting=!0,i._trigger("unselecting",e,{unselecting:n.element}))})),t(e.target).parents().addBack().each((function(){var n,s=t.data(this,"selectable-item");if(s)return n=!e.metaKey&&!e.ctrlKey||!s.$element.hasClass("ui-selected"),i._removeClass(s.$element,n?"ui-unselecting":"ui-selected")._addClass(s.$element,n?"ui-selecting":"ui-unselecting"),s.unselecting=!n,s.selecting=n,s.selected=n,n?i._trigger("selecting",e,{selecting:s.element}):i._trigger("unselecting",e,{unselecting:s.element}),!1})))},_mouseDrag:function(e){if(this.dragged=!0,!this.options.disabled){var i,n=this,s=this.options,o=this.opos[0],r=this.opos[1],a=e.pageX,l=e.pageY;return o>a&&(i=a,a=o,o=i),r>l&&(i=l,l=r,r=i),this.helper.css({left:o,top:r,width:a-o,height:l-r}),this.selectees.each((function(){var i=t.data(this,"selectable-item"),h=!1,u={};i&&i.element!==n.element[0]&&(u.left=i.left+n.elementPos.left,u.right=i.right+n.elementPos.left,u.top=i.top+n.elementPos.top,u.bottom=i.bottom+n.elementPos.top,"touch"===s.tolerance?h=!(u.left>a||u.right<o||u.top>l||u.bottom<r):"fit"===s.tolerance&&(h=u.left>o&&u.right<a&&u.top>r&&u.bottom<l),h?(i.selected&&(n._removeClass(i.$element,"ui-selected"),i.selected=!1),i.unselecting&&(n._removeClass(i.$element,"ui-unselecting"),i.unselecting=!1),i.selecting||(n._addClass(i.$element,"ui-selecting"),i.selecting=!0,n._trigger("selecting",e,{selecting:i.element}))):(i.selecting&&((e.metaKey||e.ctrlKey)&&i.startselected?(n._removeClass(i.$element,"ui-selecting"),i.selecting=!1,n._addClass(i.$element,"ui-selected"),i.selected=!0):(n._removeClass(i.$element,"ui-selecting"),i.selecting=!1,i.startselected&&(n._addClass(i.$element,"ui-unselecting"),i.unselecting=!0),n._trigger("unselecting",e,{unselecting:i.element}))),i.selected&&(e.metaKey||e.ctrlKey||i.startselected||(n._removeClass(i.$element,"ui-selected"),i.selected=!1,n._addClass(i.$element,"ui-unselecting"),i.unselecting=!0,n._trigger("unselecting",e,{unselecting:i.element})))))})),!1}},_mouseStop:function(e){var i=this;return this.dragged=!1,t(".ui-unselecting",this.element[0]).each((function(){var n=t.data(this,"selectable-item");i._removeClass(n.$element,"ui-unselecting"),n.unselecting=!1,n.startselected=!1,i._trigger("unselected",e,{unselected:n.element})})),t(".ui-selecting",this.element[0]).each((function(){var n=t.data(this,"selectable-item");i._removeClass(n.$element,"ui-selecting")._addClass(n.$element,"ui-selected"),n.selecting=!1,n.selected=!0,n.startselected=!0,i._trigger("selected",e,{selected:n.element})})),this._trigger("stop",e),this.helper.remove(),!1}}),t.widget("ui.selectmenu",[t.ui.formResetMixin,{version:"1.14.1",defaultElement:"<select>",options:{appendTo:null,classes:{"ui-selectmenu-button-open":"ui-corner-top","ui-selectmenu-button-closed":"ui-corner-all"},disabled:null,icons:{button:"ui-icon-triangle-1-s"},position:{my:"left top",at:"left bottom",collision:"none"},width:!1,change:null,close:null,focus:null,open:null,select:null},_create:function(){var e=this.element.uniqueId().attr("id");this.ids={element:e,button:e+"-button",menu:e+"-menu"},this._drawButton(),this._drawMenu(),this._bindFormResetHandler(),this._rendered=!1,this.menuItems=t()},_drawButton:function(){var e,i=this,n=this._parseOption(this.element.find("option:selected"),this.element[0].selectedIndex);this.labels=this.element.labels().attr("for",this.ids.button),this._on(this.labels,{click:function(t){this.button.trigger("focus"),t.preventDefault()}}),this.element.hide(),this.button=t("<span>",{tabindex:this.options.disabled?-1:0,id:this.ids.button,role:"combobox","aria-expanded":"false","aria-autocomplete":"list","aria-owns":this.ids.menu,"aria-haspopup":"true",title:this.element.attr("title")}).insertAfter(this.element),this._addClass(this.button,"ui-selectmenu-button ui-selectmenu-button-closed","ui-button ui-widget"),e=t("<span>").appendTo(this.button),this._addClass(e,"ui-selectmenu-icon","ui-icon "+this.options.icons.button),this.buttonItem=this._renderButtonItem(n).appendTo(this.button),!1!==this.options.width&&this._resizeButton(),this._on(this.button,this._buttonEvents),this.button.one("focusin",(function(){i._rendered||i._refreshMenu()}))},_drawMenu:function(){var e=this;this.menu=t("<ul>",{"aria-hidden":"true","aria-labelledby":this.ids.button,id:this.ids.menu}),this.menuWrap=t("<div>").append(this.menu),this._addClass(this.menuWrap,"ui-selectmenu-menu","ui-front"),this.menuWrap.appendTo(this._appendTo()),this.menuInstance=this.menu.menu({classes:{"ui-menu":"ui-corner-bottom"},role:"listbox",select:function(t,i){t.preventDefault(),e._select(i.item.data("ui-selectmenu-item"),t)},focus:function(t,i){var n=i.item.data("ui-selectmenu-item");null!=e.focusIndex&&n.index!==e.focusIndex&&(e._trigger("focus",t,{item:n}),e.isOpen||e._select(n,t)),e.focusIndex=n.index,e.button.attr("aria-activedescendant",e.menuItems.eq(n.index).attr("id"))}}).menu("instance"),this.menuInstance._off(this.menu,"mouseleave"),this.menuInstance._closeOnDocumentClick=function(){return!1},this.menuInstance._isDivider=function(){return!1}},refresh:function(){this._refreshMenu(),this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(this._getSelectedItem().data("ui-selectmenu-item")||{})),null===this.options.width&&this._resizeButton()},_refreshMenu:function(){var t,e=this.element.find("option");this.menu.empty(),this._parseOptions(e),this._renderMenu(this.menu,this.items),this.menuInstance.refresh(),this.menuItems=this.menu.find("li").not(".ui-selectmenu-optgroup").find(".ui-menu-item-wrapper"),this._rendered=!0,e.length&&(t=this._getSelectedItem(),this.menuInstance.focus(null,t),this._setAria(t.data("ui-selectmenu-item")),this._setOption("disabled",this.element.prop("disabled")))},open:function(t){this.options.disabled||(this._rendered?(this._removeClass(this.menu.find(".ui-state-active"),null,"ui-state-active"),this.menuInstance.focus(null,this._getSelectedItem())):this._refreshMenu(),this.menuItems.length&&(this.isOpen=!0,this._toggleAttr(),this._resizeMenu(),this._position(),this._on(this.document,this._documentClick),this._trigger("open",t)))},_position:function(){this.menuWrap.position(t.extend({of:this.button},this.options.position))},close:function(t){this.isOpen&&(this.isOpen=!1,this._toggleAttr(),this.range=null,this._off(this.document),this._trigger("close",t))},widget:function(){return this.button},menuWidget:function(){return this.menu},_renderButtonItem:function(e){var i=t("<span>");return this._setText(i,e.label),this._addClass(i,"ui-selectmenu-text"),i},_renderMenu:function(e,i){var n=this,s="";t.each(i,(function(i,o){var r;o.optgroup!==s&&(r=t("<li>",{text:o.optgroup}),n._addClass(r,"ui-selectmenu-optgroup","ui-menu-divider"+(o.element.parent("optgroup").prop("disabled")?" ui-state-disabled":"")),r.appendTo(e),s=o.optgroup),n._renderItemData(e,o)}))},_renderItemData:function(t,e){return this._renderItem(t,e).data("ui-selectmenu-item",e)},_renderItem:function(e,i){var n=t("<li>"),s=t("<div>",{title:i.element.attr("title")});return i.disabled&&this._addClass(n,null,"ui-state-disabled"),i.hidden?n.prop("hidden",!0):this._setText(s,i.label),n.append(s).appendTo(e)},_setText:function(t,e){e?t.text(e):t.html("&#160;")},_move:function(t,e){var i,n,s=".ui-menu-item";this.isOpen?i=this.menuItems.eq(this.focusIndex).parent("li"):(i=this.menuItems.eq(this.element[0].selectedIndex).parent("li"),s+=":not(.ui-state-disabled)"),(n="first"===t||"last"===t?i["first"===t?"prevAll":"nextAll"](s).eq(-1):i[t+"All"](s).eq(0)).length&&this.menuInstance.focus(e,n)},_getSelectedItem:function(){return this.menuItems.eq(this.element[0].selectedIndex).parent("li")},_toggle:function(t){this[this.isOpen?"close":"open"](t)},_setSelection:function(){var t;this.range&&((t=window.getSelection()).removeAllRanges(),t.addRange(this.range))},_documentClick:{mousedown:function(e){this.isOpen&&(t(e.target).closest(".ui-selectmenu-menu, #"+CSS.escape(this.ids.button)).length||this.close(e))}},_buttonEvents:{mousedown:function(){var t=window.getSelection();t.rangeCount&&(this.range=t.getRangeAt(0))},click:function(t){this._setSelection(),this._toggle(t)},keydown:function(e){var i=!0;switch(e.keyCode){case t.ui.keyCode.TAB:case t.ui.keyCode.ESCAPE:this.close(e),i=!1;break;case t.ui.keyCode.ENTER:this.isOpen&&this._selectFocusedItem(e);break;case t.ui.keyCode.UP:e.altKey?this._toggle(e):this._move("prev",e);break;case t.ui.keyCode.DOWN:e.altKey?this._toggle(e):this._move("next",e);break;case t.ui.keyCode.SPACE:this.isOpen?this._selectFocusedItem(e):this._toggle(e);break;case t.ui.keyCode.LEFT:this._move("prev",e);break;case t.ui.keyCode.RIGHT:this._move("next",e);break;case t.ui.keyCode.HOME:case t.ui.keyCode.PAGE_UP:this._move("first",e);break;case t.ui.keyCode.END:case t.ui.keyCode.PAGE_DOWN:this._move("last",e);break;default:this.menu.trigger(e),i=!1}i&&e.preventDefault()}},_selectFocusedItem:function(t){var e=this.menuItems.eq(this.focusIndex).parent("li");e.hasClass("ui-state-disabled")||this._select(e.data("ui-selectmenu-item"),t)},_select:function(t,e){var i=this.element[0].selectedIndex;this.element[0].selectedIndex=t.index,this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(t)),this._setAria(t),this._trigger("select",e,{item:t}),t.index!==i&&this._trigger("change",e,{item:t}),this.close(e)},_setAria:function(t){var e=this.menuItems.eq(t.index).attr("id");this.button.attr({"aria-labelledby":e,"aria-activedescendant":e}),this.menu.attr("aria-activedescendant",e)},_setOption:function(t,e){if("icons"===t){var i=this.button.find("span.ui-icon");this._removeClass(i,null,this.options.icons.button)._addClass(i,null,e.button)}this._super(t,e),"appendTo"===t&&this.menuWrap.appendTo(this._appendTo()),"width"===t&&this._resizeButton()},_setOptionDisabled:function(t){this._super(t),this.menuInstance.option("disabled",t),this.button.attr("aria-disabled",t),this._toggleClass(this.button,null,"ui-state-disabled",t),this.element.prop("disabled",t),t?(this.button.attr("tabindex",-1),this.close()):this.button.attr("tabindex",0)},_appendTo:function(){var e=this.options.appendTo;return e&&(e=e.jquery||e.nodeType?t(e):this.document.find(e).eq(0)),e&&e[0]||(e=this.element.closest(".ui-front, dialog")),e.length||(e=this.document[0].body),e},_toggleAttr:function(){this.button.attr("aria-expanded",this.isOpen),this._removeClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"closed":"open"))._addClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"open":"closed"))._toggleClass(this.menuWrap,"ui-selectmenu-open",null,this.isOpen),this.menu.attr("aria-hidden",!this.isOpen)},_resizeButton:function(){var t=this.options.width;!1!==t?(null===t&&(t=this.element.show().outerWidth(),this.element.hide()),this.button.outerWidth(t)):this.button.css("width","")},_resizeMenu:function(){this.menu.outerWidth(Math.max(this.button.outerWidth(),this.menu.width("").outerWidth()))},_getCreateOptions:function(){var t=this._super();return t.disabled=this.element.prop("disabled"),t},_parseOptions:function(e){var i=this,n=[];e.each((function(e,s){n.push(i._parseOption(t(s),e))})),this.items=n},_parseOption:function(t,e){var i=t.parent("optgroup");return{element:t,index:e,value:t.val(),label:t.text(),hidden:i.prop("hidden")||t.prop("hidden"),optgroup:i.attr("label")||"",disabled:i.prop("disabled")||t.prop("disabled")}},_destroy:function(){this._unbindFormResetHandler(),this.menuWrap.remove(),this.button.remove(),this.element.show(),this.element.removeUniqueId(),this.labels.attr("for",this.ids.element)}}]),t.widget("ui.slider",t.ui.mouse,{version:"1.14.1",widgetEventPrefix:"slide",options:{animate:!1,classes:{"ui-slider":"ui-corner-all","ui-slider-handle":"ui-corner-all","ui-slider-range":"ui-corner-all ui-widget-header"},distance:0,max:100,min:0,orientation:"horizontal",range:!1,step:1,value:0,values:null,change:null,slide:null,start:null,stop:null},numPages:5,_create:function(){this._keySliding=!1,this._mouseSliding=!1,this._animateOff=!0,this._handleIndex=null,this._detectOrientation(),this._mouseInit(),this._calculateNewMax(),this._addClass("ui-slider ui-slider-"+this.orientation,"ui-widget ui-widget-content"),this._refresh(),this._animateOff=!1},_refresh:function(){this._createRange(),this._createHandles(),this._setupEvents(),this._refreshValue()},_createHandles:function(){var e,i,n=this.options,s=this.element.find(".ui-slider-handle"),o="<span tabindex='0'></span>",r=[];for(i=n.values&&n.values.length||1,s.length>i&&(s.slice(i).remove(),s=s.slice(0,i)),e=s.length;e<i;e++)r.push(o);this.handles=s.add(t(r.join("")).appendTo(this.element)),this._addClass(this.handles,"ui-slider-handle","ui-state-default"),this.handle=this.handles.eq(0),this.handles.each((function(e){t(this).data("ui-slider-handle-index",e).attr("tabIndex",0)}))},_createRange:function(){var e=this.options;e.range?(!0===e.range&&(e.values?e.values.length&&2!==e.values.length?e.values=[e.values[0],e.values[0]]:Array.isArray(e.values)&&(e.values=e.values.slice(0)):e.values=[this._valueMin(),this._valueMin()]),this.range&&this.range.length?(this._removeClass(this.range,"ui-slider-range-min ui-slider-range-max"),this.range.css({left:"",bottom:""})):(this.range=t("<div>").appendTo(this.element),this._addClass(this.range,"ui-slider-range")),"min"!==e.range&&"max"!==e.range||this._addClass(this.range,"ui-slider-range-"+e.range)):(this.range&&this.range.remove(),this.range=null)},_setupEvents:function(){this._off(this.handles),this._on(this.handles,this._handleEvents),this._hoverable(this.handles),this._focusable(this.handles)},_destroy:function(){this.handles.remove(),this.range&&this.range.remove(),this._mouseDestroy()},_mouseCapture:function(e){var i,n,s,o,r,a,l,h=this,u=this.options;return!u.disabled&&(this.elementSize={width:this.element.outerWidth(),height:this.element.outerHeight()},this.elementOffset=this.element.offset(),i={x:e.pageX,y:e.pageY},n=this._normValueFromMouse(i),s=this._valueMax()-this._valueMin()+1,this.handles.each((function(e){var i=Math.abs(n-h.values(e));(s>i||s===i&&(e===h._lastChangedValue||h.values(e)===u.min))&&(s=i,o=t(this),r=e)})),!1!==this._start(e,r)&&(this._mouseSliding=!0,this._handleIndex=r,this._addClass(o,null,"ui-state-active"),o.trigger("focus"),a=o.offset(),l=!t(e.target).parents().addBack().is(".ui-slider-handle"),this._clickOffset=l?{left:0,top:0}:{left:e.pageX-a.left-o.width()/2,top:e.pageY-a.top-o.height()/2-(parseInt(o.css("borderTopWidth"),10)||0)-(parseInt(o.css("borderBottomWidth"),10)||0)+(parseInt(o.css("marginTop"),10)||0)},this.handles.hasClass("ui-state-hover")||this._slide(e,r,n),this._animateOff=!0,!0))},_mouseStart:function(){return!0},_mouseDrag:function(t){var e={x:t.pageX,y:t.pageY},i=this._normValueFromMouse(e);return this._slide(t,this._handleIndex,i),!1},_mouseStop:function(t){return this._removeClass(this.handles,null,"ui-state-active"),this._mouseSliding=!1,this._stop(t,this._handleIndex),this._change(t,this._handleIndex),this._handleIndex=null,this._clickOffset=null,this._animateOff=!1,!1},_detectOrientation:function(){this.orientation="vertical"===this.options.orientation?"vertical":"horizontal"},_normValueFromMouse:function(t){var e,i,n,s,o;return"horizontal"===this.orientation?(e=this.elementSize.width,i=t.x-this.elementOffset.left-(this._clickOffset?this._clickOffset.left:0)):(e=this.elementSize.height,i=t.y-this.elementOffset.top-(this._clickOffset?this._clickOffset.top:0)),(n=i/e)>1&&(n=1),n<0&&(n=0),"vertical"===this.orientation&&(n=1-n),s=this._valueMax()-this._valueMin(),o=this._valueMin()+n*s,this._trimAlignValue(o)},_uiHash:function(t,e,i){var n={handle:this.handles[t],handleIndex:t,value:void 0!==e?e:this.value()};return this._hasMultipleValues()&&(n.value=void 0!==e?e:this.values(t),n.values=i||this.values()),n},_hasMultipleValues:function(){return this.options.values&&this.options.values.length},_start:function(t,e){return this._trigger("start",t,this._uiHash(e))},_slide:function(t,e,i){var n,s=this.value(),o=this.values();this._hasMultipleValues()&&(n=this.values(e?0:1),s=this.values(e),2===this.options.values.length&&!0===this.options.range&&(i=0===e?Math.min(n,i):Math.max(n,i)),o[e]=i),i!==s&&!1!==this._trigger("slide",t,this._uiHash(e,i,o))&&(this._hasMultipleValues()?this.values(e,i):this.value(i))},_stop:function(t,e){this._trigger("stop",t,this._uiHash(e))},_change:function(t,e){this._keySliding||this._mouseSliding||(this._lastChangedValue=e,this._trigger("change",t,this._uiHash(e)))},value:function(t){return arguments.length?(this.options.value=this._trimAlignValue(t),this._refreshValue(),void this._change(null,0)):this._value()},values:function(t,e){var i,n,s;if(arguments.length>1)return this.options.values[t]=this._trimAlignValue(e),this._refreshValue(),void this._change(null,t);if(!arguments.length)return this._values();if(!Array.isArray(arguments[0]))return this._hasMultipleValues()?this._values(t):this.value();for(i=this.options.values,n=arguments[0],s=0;s<i.length;s+=1)i[s]=this._trimAlignValue(n[s]),this._change(null,s);this._refreshValue()},_setOption:function(t,e){var i,n=0;switch("range"===t&&!0===this.options.range&&("min"===e?(this.options.value=this._values(0),this.options.values=null):"max"===e&&(this.options.value=this._values(this.options.values.length-1),this.options.values=null)),Array.isArray(this.options.values)&&(n=this.options.values.length),this._super(t,e),t){case"orientation":this._detectOrientation(),this._removeClass("ui-slider-horizontal ui-slider-vertical")._addClass("ui-slider-"+this.orientation),this._refreshValue(),this.options.range&&this._refreshRange(e),this.handles.css("horizontal"===e?"bottom":"left","");break;case"value":this._animateOff=!0,this._refreshValue(),this._change(null,0),this._animateOff=!1;break;case"values":for(this._animateOff=!0,this._refreshValue(),i=n-1;i>=0;i--)this._change(null,i);this._animateOff=!1;break;case"step":case"min":case"max":this._animateOff=!0,this._calculateNewMax(),this._refreshValue(),this._animateOff=!1;break;case"range":this._animateOff=!0,this._refresh(),this._animateOff=!1}},_setOptionDisabled:function(t){this._super(t),this._toggleClass(null,"ui-state-disabled",!!t)},_value:function(){var t=this.options.value;return t=this._trimAlignValue(t)},_values:function(t){var e,i,n;if(arguments.length)return e=this.options.values[t],e=this._trimAlignValue(e);if(this._hasMultipleValues()){for(i=this.options.values.slice(),n=0;n<i.length;n+=1)i[n]=this._trimAlignValue(i[n]);return i}return[]},_trimAlignValue:function(t){if(t<=this._valueMin())return this._valueMin();if(t>=this._valueMax())return this._valueMax();var e=this.options.step>0?this.options.step:1,i=(t-this._valueMin())%e,n=t-i;return 2*Math.abs(i)>=e&&(n+=i>0?e:-e),parseFloat(n.toFixed(5))},_calculateNewMax:function(){var t=this.options.max,e=this._valueMin(),i=this.options.step;(t=Math.round((t-e)/i)*i+e)>this.options.max&&(t-=i),this.max=parseFloat(t.toFixed(this._precision()))},_precision:function(){var t=this._precisionOf(this.options.step);return null!==this.options.min&&(t=Math.max(t,this._precisionOf(this.options.min))),t},_precisionOf:function(t){var e=t.toString(),i=e.indexOf(".");return-1===i?0:e.length-i-1},_valueMin:function(){return this.options.min},_valueMax:function(){return this.max},_refreshRange:function(t){"vertical"===t&&this.range.css({width:"",left:""}),"horizontal"===t&&this.range.css({height:"",bottom:""})},_refreshValue:function(){var e,i,n,s,o,r=this.options.range,a=this.options,l=this,h=!this._animateOff&&a.animate,u={};this._hasMultipleValues()?this.handles.each((function(n){i=(l.values(n)-l._valueMin())/(l._valueMax()-l._valueMin())*100,u["horizontal"===l.orientation?"left":"bottom"]=i+"%",t(this).stop(1,1)[h?"animate":"css"](u,a.animate),!0===l.options.range&&("horizontal"===l.orientation?(0===n&&l.range.stop(1,1)[h?"animate":"css"]({left:i+"%"},a.animate),1===n&&l.range[h?"animate":"css"]({width:i-e+"%"},{queue:!1,duration:a.animate})):(0===n&&l.range.stop(1,1)[h?"animate":"css"]({bottom:i+"%"},a.animate),1===n&&l.range[h?"animate":"css"]({height:i-e+"%"},{queue:!1,duration:a.animate}))),e=i})):(n=this.value(),s=this._valueMin(),o=this._valueMax(),i=o!==s?(n-s)/(o-s)*100:0,u["horizontal"===this.orientation?"left":"bottom"]=i+"%",this.handle.stop(1,1)[h?"animate":"css"](u,a.animate),"min"===r&&"horizontal"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({width:i+"%"},a.animate),"max"===r&&"horizontal"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({width:100-i+"%"},a.animate),"min"===r&&"vertical"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({height:i+"%"},a.animate),"max"===r&&"vertical"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({height:100-i+"%"},a.animate))},_handleEvents:{keydown:function(e){var i,n,s,o=t(e.target).data("ui-slider-handle-index");switch(e.keyCode){case t.ui.keyCode.HOME:case t.ui.keyCode.END:case t.ui.keyCode.PAGE_UP:case t.ui.keyCode.PAGE_DOWN:case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(e.preventDefault(),!this._keySliding&&(this._keySliding=!0,this._addClass(t(e.target),null,"ui-state-active"),!1===this._start(e,o)))return}switch(s=this.options.step,i=n=this._hasMultipleValues()?this.values(o):this.value(),e.keyCode){case t.ui.keyCode.HOME:n=this._valueMin();break;case t.ui.keyCode.END:n=this._valueMax();break;case t.ui.keyCode.PAGE_UP:n=this._trimAlignValue(i+(this._valueMax()-this._valueMin())/this.numPages);break;case t.ui.keyCode.PAGE_DOWN:n=this._trimAlignValue(i-(this._valueMax()-this._valueMin())/this.numPages);break;case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:if(i===this._valueMax())return;n=this._trimAlignValue(i+s);break;case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(i===this._valueMin())return;n=this._trimAlignValue(i-s)}this._slide(e,o,n)},keyup:function(e){var i=t(e.target).data("ui-slider-handle-index");this._keySliding&&(this._keySliding=!1,this._stop(e,i),this._change(e,i),this._removeClass(t(e.target),null,"ui-state-active"))}}}),t.widget("ui.sortable",t.ui.mouse,{version:"1.14.1",widgetEventPrefix:"sort",ready:!1,options:{appendTo:"parent",axis:!1,connectWith:!1,containment:!1,cursor:"auto",cursorAt:!1,dropOnEmpty:!0,forcePlaceholderSize:!1,forceHelperSize:!1,grid:!1,handle:!1,helper:"original",items:"> *",opacity:!1,placeholder:!1,revert:!1,scroll:!0,scrollSensitivity:20,scrollSpeed:20,scope:"default",tolerance:"intersect",zIndex:1e3,activate:null,beforeStop:null,change:null,deactivate:null,out:null,over:null,receive:null,remove:null,sort:null,start:null,stop:null,update:null},_isOverAxis:function(t,e,i){return t>=e&&t<e+i},_isFloating:function(t){return/left|right/.test(t.css("float"))||/inline|table-cell/.test(t.css("display"))},_create:function(){this.containerCache={},this._addClass("ui-sortable"),this.refresh(),this.offset=this.element.offset(),this._mouseInit(),this._setHandleClassName(),this.ready=!0},_setOption:function(t,e){this._super(t,e),"handle"===t&&this._setHandleClassName()},_setHandleClassName:function(){var e=this;this._removeClass(this.element.find(".ui-sortable-handle"),"ui-sortable-handle"),t.each(this.items,(function(){e._addClass(this.instance.options.handle?this.item.find(this.instance.options.handle):this.item,"ui-sortable-handle")}))},_destroy:function(){this._mouseDestroy();for(var t=this.items.length-1;t>=0;t--)this.items[t].item.removeData(this.widgetName+"-item");return this},_mouseCapture:function(e,i){var n=null,s=!1,o=this;return!this.reverting&&(!this.options.disabled&&"static"!==this.options.type&&(this._refreshItems(e),t(e.target).parents().each((function(){if(t.data(this,o.widgetName+"-item")===o)return n=t(this),!1})),t.data(e.target,o.widgetName+"-item")===o&&(n=t(e.target)),!!n&&(!(this.options.handle&&!i&&(t(this.options.handle,n).find("*").addBack().each((function(){this===e.target&&(s=!0)})),!s))&&(this.currentItem=n,this._removeCurrentsFromItems(),!0))))},_mouseStart:function(e,i,n){var s,o,r=this.options;if(this.currentContainer=this,this.refreshPositions(),this.appendTo=t("parent"!==r.appendTo?r.appendTo:this.currentItem.parent()),this.helper=this._createHelper(e),this._cacheHelperProportions(),this._cacheMargins(),this.offset=this.currentItem.offset(),this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left},t.extend(this.offset,{click:{left:e.pageX-this.offset.left,top:e.pageY-this.offset.top},relative:this._getRelativeOffset()}),this.helper.css("position","absolute"),this.cssPosition=this.helper.css("position"),r.cursorAt&&this._adjustOffsetFromHelper(r.cursorAt),this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]},this.helper[0]!==this.currentItem[0]&&this.currentItem.hide(),this._createPlaceholder(),this.scrollParent=this.placeholder.scrollParent(),t.extend(this.offset,{parent:this._getParentOffset()}),r.containment&&this._setContainment(),r.cursor&&"auto"!==r.cursor&&(o=this.document.find("body"),this._storedStylesheet=t("<style>*{ cursor: "+r.cursor+" !important; }</style>").appendTo(o)),r.zIndex&&(this.helper.css("zIndex")&&(this._storedZIndex=this.helper.css("zIndex")),this.helper.css("zIndex",r.zIndex)),r.opacity&&(this.helper.css("opacity")&&(this._storedOpacity=this.helper.css("opacity")),this.helper.css("opacity",r.opacity)),this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName&&(this.overflowOffset=this.scrollParent.offset()),this._trigger("start",e,this._uiHash()),this._preserveHelperProportions||this._cacheHelperProportions(),!n)for(s=this.containers.length-1;s>=0;s--)this.containers[s]._trigger("activate",e,this._uiHash(this));return t.ui.ddmanager&&(t.ui.ddmanager.current=this),t.ui.ddmanager&&!r.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e),this.dragging=!0,this._addClass(this.helper,"ui-sortable-helper"),this.helper.parent().is(this.appendTo)||(this.helper.detach().appendTo(this.appendTo),this.offset.parent=this._getParentOffset()),this.position=this.originalPosition=this._generatePosition(e),this.originalPageX=e.pageX,this.originalPageY=e.pageY,this.lastPositionAbs=this.positionAbs=this._convertPositionTo("absolute"),this._mouseDrag(e),!0},_scroll:function(t){var e=this.options,i=!1;return this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName?(this.overflowOffset.top+this.scrollParent[0].offsetHeight-t.pageY<e.scrollSensitivity?this.scrollParent[0].scrollTop=i=this.scrollParent[0].scrollTop+e.scrollSpeed:t.pageY-this.overflowOffset.top<e.scrollSensitivity&&(this.scrollParent[0].scrollTop=i=this.scrollParent[0].scrollTop-e.scrollSpeed),this.overflowOffset.left+this.scrollParent[0].offsetWidth-t.pageX<e.scrollSensitivity?this.scrollParent[0].scrollLeft=i=this.scrollParent[0].scrollLeft+e.scrollSpeed:t.pageX-this.overflowOffset.left<e.scrollSensitivity&&(this.scrollParent[0].scrollLeft=i=this.scrollParent[0].scrollLeft-e.scrollSpeed)):(t.pageY-this.document.scrollTop()<e.scrollSensitivity?i=this.document.scrollTop(this.document.scrollTop()-e.scrollSpeed):this.window.height()-(t.pageY-this.document.scrollTop())<e.scrollSensitivity&&(i=this.document.scrollTop(this.document.scrollTop()+e.scrollSpeed)),t.pageX-this.document.scrollLeft()<e.scrollSensitivity?i=this.document.scrollLeft(this.document.scrollLeft()-e.scrollSpeed):this.window.width()-(t.pageX-this.document.scrollLeft())<e.scrollSensitivity&&(i=this.document.scrollLeft(this.document.scrollLeft()+e.scrollSpeed))),i},_mouseDrag:function(e){var i,n,s,o,r=this.options;for(this.position=this._generatePosition(e),this.positionAbs=this._convertPositionTo("absolute"),this.options.axis&&"y"===this.options.axis||(this.helper[0].style.left=this.position.left+"px"),this.options.axis&&"x"===this.options.axis||(this.helper[0].style.top=this.position.top+"px"),r.scroll&&!1!==this._scroll(e)&&(this._refreshItemPositions(!0),t.ui.ddmanager&&!r.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e)),this.dragDirection={vertical:this._getDragVerticalDirection(),horizontal:this._getDragHorizontalDirection()},i=this.items.length-1;i>=0;i--)if(s=(n=this.items[i]).item[0],(o=this._intersectsWithPointer(n))&&n.instance===this.currentContainer&&!(s===this.currentItem[0]||this.placeholder[1===o?"next":"prev"]()[0]===s||t.contains(this.placeholder[0],s)||"semi-dynamic"===this.options.type&&t.contains(this.element[0],s))){if(this.direction=1===o?"down":"up","pointer"!==this.options.tolerance&&!this._intersectsWithSides(n))break;this._rearrange(e,n),this._trigger("change",e,this._uiHash());break}return this._contactContainers(e),t.ui.ddmanager&&t.ui.ddmanager.drag(this,e),this._trigger("sort",e,this._uiHash()),this.lastPositionAbs=this.positionAbs,!1},_mouseStop:function(e,i){if(e){if(t.ui.ddmanager&&!this.options.dropBehaviour&&t.ui.ddmanager.drop(this,e),this.options.revert){var n=this,s=this.placeholder.offset(),o=this.options.axis,r={};o&&"x"!==o||(r.left=s.left-this.offset.parent.left-this.margins.left+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollLeft)),o&&"y"!==o||(r.top=s.top-this.offset.parent.top-this.margins.top+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollTop)),this.reverting=!0,t(this.helper).animate(r,parseInt(this.options.revert,10)||500,(function(){n._clear(e)}))}else this._clear(e,i);return!1}},cancel:function(){if(this.dragging){this._mouseUp(new t.Event("mouseup",{target:null})),"original"===this.options.helper?(this.currentItem.css(this._storedCSS),this._removeClass(this.currentItem,"ui-sortable-helper")):this.currentItem.show();for(var e=this.containers.length-1;e>=0;e--)this.containers[e]._trigger("deactivate",null,this._uiHash(this)),this.containers[e].containerCache.over&&(this.containers[e]._trigger("out",null,this._uiHash(this)),this.containers[e].containerCache.over=0)}return this.placeholder&&(this.placeholder[0].parentNode&&this.placeholder[0].parentNode.removeChild(this.placeholder[0]),"original"!==this.options.helper&&this.helper&&this.helper[0].parentNode&&this.helper.remove(),t.extend(this,{helper:null,dragging:!1,reverting:!1,_noFinalSort:null}),this.domPosition.prev?t(this.domPosition.prev).after(this.currentItem):t(this.domPosition.parent).prepend(this.currentItem)),this},serialize:function(e){var i=this._getItemsAsjQuery(e&&e.connected),n=[];return e=e||{},t(i).each((function(){var i=(t(e.item||this).attr(e.attribute||"id")||"").match(e.expression||/(.+)[\-=_](.+)/);i&&n.push((e.key||i[1]+"[]")+"="+(e.key&&e.expression?i[1]:i[2]))})),!n.length&&e.key&&n.push(e.key+"="),n.join("&")},toArray:function(e){var i=this._getItemsAsjQuery(e&&e.connected),n=[];return e=e||{},i.each((function(){n.push(t(e.item||this).attr(e.attribute||"id")||"")})),n},_intersectsWith:function(t){var e=this.positionAbs.left,i=e+this.helperProportions.width,n=this.positionAbs.top,s=n+this.helperProportions.height,o=t.left,r=o+t.width,a=t.top,l=a+t.height,h=this.offset.click.top,u=this.offset.click.left,c="x"===this.options.axis||n+h>a&&n+h<l,d="y"===this.options.axis||e+u>o&&e+u<r,p=c&&d;return"pointer"===this.options.tolerance||this.options.forcePointerForContainers||"pointer"!==this.options.tolerance&&this.helperProportions[this.floating?"width":"height"]>t[this.floating?"width":"height"]?p:o<e+this.helperProportions.width/2&&i-this.helperProportions.width/2<r&&a<n+this.helperProportions.height/2&&s-this.helperProportions.height/2<l},_intersectsWithPointer:function(t){var e,i,n="x"===this.options.axis||this._isOverAxis(this.positionAbs.top+this.offset.click.top,t.top,t.height),s="y"===this.options.axis||this._isOverAxis(this.positionAbs.left+this.offset.click.left,t.left,t.width);return!(!n||!s)&&(e=this.dragDirection.vertical,i=this.dragDirection.horizontal,this.floating?"right"===i||"down"===e?2:1:e&&("down"===e?2:1))},_intersectsWithSides:function(t){var e=this._isOverAxis(this.positionAbs.top+this.offset.click.top,t.top+t.height/2,t.height),i=this._isOverAxis(this.positionAbs.left+this.offset.click.left,t.left+t.width/2,t.width),n=this.dragDirection.vertical,s=this.dragDirection.horizontal;return this.floating&&s?"right"===s&&i||"left"===s&&!i:n&&("down"===n&&e||"up"===n&&!e)},_getDragVerticalDirection:function(){var t=this.positionAbs.top-this.lastPositionAbs.top;return 0!==t&&(t>0?"down":"up")},_getDragHorizontalDirection:function(){var t=this.positionAbs.left-this.lastPositionAbs.left;return 0!==t&&(t>0?"right":"left")},refresh:function(t){return this._refreshItems(t),this._setHandleClassName(),this.refreshPositions(),this},_connectWith:function(){var t=this.options;return t.connectWith.constructor===String?[t.connectWith]:t.connectWith},_getItemsAsjQuery:function(e){var i,n,s,o,r=[],a=[],l=this._connectWith();if(l&&e)for(i=l.length-1;i>=0;i--)for(n=(s=t(l[i],this.document[0])).length-1;n>=0;n--)(o=t.data(s[n],this.widgetFullName))&&o!==this&&!o.options.disabled&&a.push(["function"==typeof o.options.items?o.options.items.call(o.element):t(o.options.items,o.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),o]);function h(){r.push(this)}for(a.push(["function"==typeof this.options.items?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):t(this.options.items,this.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),this]),i=a.length-1;i>=0;i--)a[i][0].each(h);return t(r)},_removeCurrentsFromItems:function(){var e=this.currentItem.find(":data("+this.widgetName+"-item)");this.items=t.grep(this.items,(function(t){for(var i=0;i<e.length;i++)if(e[i]===t.item[0])return!1;return!0}))},_refreshItems:function(e){this.items=[],this.containers=[this];var i,n,s,o,r,a,l,h,u=this.items,c=[["function"==typeof this.options.items?this.options.items.call(this.element[0],e,{item:this.currentItem}):t(this.options.items,this.element),this]],d=this._connectWith();if(d&&this.ready)for(i=d.length-1;i>=0;i--)for(n=(s=t(d[i],this.document[0])).length-1;n>=0;n--)(o=t.data(s[n],this.widgetFullName))&&o!==this&&!o.options.disabled&&(c.push(["function"==typeof o.options.items?o.options.items.call(o.element[0],e,{item:this.currentItem}):t(o.options.items,o.element),o]),this.containers.push(o));for(i=c.length-1;i>=0;i--)for(r=c[i][1],n=0,h=(a=c[i][0]).length;n<h;n++)(l=t(a[n])).data(this.widgetName+"-item",r),u.push({item:l,instance:r,width:0,height:0,left:0,top:0})},_refreshItemPositions:function(e){var i,n,s,o;for(i=this.items.length-1;i>=0;i--)n=this.items[i],this.currentContainer&&n.instance!==this.currentContainer&&n.item[0]!==this.currentItem[0]||(s=this.options.toleranceElement?t(this.options.toleranceElement,n.item):n.item,e||(n.width=s.outerWidth(),n.height=s.outerHeight()),o=s.offset(),n.left=o.left,n.top=o.top)},refreshPositions:function(t){var e,i;if(this.floating=!!this.items.length&&("x"===this.options.axis||this._isFloating(this.items[0].item)),this.offsetParent&&this.helper&&(this.offset.parent=this._getParentOffset()),this._refreshItemPositions(t),this.options.custom&&this.options.custom.refreshContainers)this.options.custom.refreshContainers.call(this);else for(e=this.containers.length-1;e>=0;e--)i=this.containers[e].element.offset(),this.containers[e].containerCache.left=i.left,this.containers[e].containerCache.top=i.top,this.containers[e].containerCache.width=this.containers[e].element.outerWidth(),this.containers[e].containerCache.height=this.containers[e].element.outerHeight();return this},_createPlaceholder:function(e){var i,n,s=(e=e||this).options;s.placeholder&&s.placeholder.constructor!==String||(i=s.placeholder,n=e.currentItem[0].nodeName.toLowerCase(),s.placeholder={element:function(){var s=t("<"+n+">",e.document[0]);return e._addClass(s,"ui-sortable-placeholder",i||e.currentItem[0].className)._removeClass(s,"ui-sortable-helper"),"tbody"===n?e._createTrPlaceholder(e.currentItem.find("tr").eq(0),t("<tr>",e.document[0]).appendTo(s)):"tr"===n?e._createTrPlaceholder(e.currentItem,s):"img"===n&&s.attr("src",e.currentItem.attr("src")),i||s.css("visibility","hidden"),s},update:function(t,o){i&&!s.forcePlaceholderSize||(o.height()&&(!s.forcePlaceholderSize||"tbody"!==n&&"tr"!==n)||o.height(e.currentItem.innerHeight()-parseInt(e.currentItem.css("paddingTop")||0,10)-parseInt(e.currentItem.css("paddingBottom")||0,10)),o.width()||o.width(e.currentItem.innerWidth()-parseInt(e.currentItem.css("paddingLeft")||0,10)-parseInt(e.currentItem.css("paddingRight")||0,10)))}}),e.placeholder=t(s.placeholder.element.call(e.element,e.currentItem)),e.currentItem.after(e.placeholder),s.placeholder.update(e,e.placeholder)},_createTrPlaceholder:function(e,i){var n=this;e.children().each((function(){t("<td>&#160;</td>",n.document[0]).attr("colspan",t(this).attr("colspan")||1).appendTo(i)}))},_contactContainers:function(e){var i,n,s,o,r,a,l,h,u,c,d=null,p=null;for(i=this.containers.length-1;i>=0;i--)if(!t.contains(this.currentItem[0],this.containers[i].element[0]))if(this._intersectsWith(this.containers[i].containerCache)){if(d&&t.contains(this.containers[i].element[0],d.element[0]))continue;d=this.containers[i],p=i}else this.containers[i].containerCache.over&&(this.containers[i]._trigger("out",e,this._uiHash(this)),this.containers[i].containerCache.over=0);if(d)if(1===this.containers.length)this.containers[p].containerCache.over||(this.containers[p]._trigger("over",e,this._uiHash(this)),this.containers[p].containerCache.over=1);else{for(s=1e4,o=null,r=(u=d.floating||this._isFloating(this.currentItem))?"left":"top",a=u?"width":"height",c=u?"pageX":"pageY",n=this.items.length-1;n>=0;n--)t.contains(this.containers[p].element[0],this.items[n].item[0])&&this.items[n].item[0]!==this.currentItem[0]&&(l=this.items[n].item.offset()[r],h=!1,e[c]-l>this.items[n][a]/2&&(h=!0),Math.abs(e[c]-l)<s&&(s=Math.abs(e[c]-l),o=this.items[n],this.direction=h?"up":"down"));if(!o&&!this.options.dropOnEmpty)return;if(this.currentContainer===this.containers[p])return void(this.currentContainer.containerCache.over||(this.containers[p]._trigger("over",e,this._uiHash()),this.currentContainer.containerCache.over=1));o?this._rearrange(e,o,null,!0):this._rearrange(e,null,this.containers[p].element,!0),this._trigger("change",e,this._uiHash()),this.containers[p]._trigger("change",e,this._uiHash(this)),this.currentContainer=this.containers[p],this.options.placeholder.update(this.currentContainer,this.placeholder),this.scrollParent=this.placeholder.scrollParent(),this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName&&(this.overflowOffset=this.scrollParent.offset()),this.containers[p]._trigger("over",e,this._uiHash(this)),this.containers[p].containerCache.over=1}},_createHelper:function(e){var i=this.options,n="function"==typeof i.helper?t(i.helper.apply(this.element[0],[e,this.currentItem])):"clone"===i.helper?this.currentItem.clone():this.currentItem;return n.parents("body").length||this.appendTo[0].appendChild(n[0]),n[0]===this.currentItem[0]&&(this._storedCSS={width:this.currentItem[0].style.width,height:this.currentItem[0].style.height,position:this.currentItem.css("position"),top:this.currentItem.css("top"),left:this.currentItem.css("left")}),n[0].style.width&&!i.forceHelperSize||n.width(this.currentItem.width()),n[0].style.height&&!i.forceHelperSize||n.height(this.currentItem.height()),n},_adjustOffsetFromHelper:function(t){"string"==typeof t&&(t=t.split(" ")),Array.isArray(t)&&(t={left:+t[0],top:+t[1]||0}),"left"in t&&(this.offset.click.left=t.left+this.margins.left),"right"in t&&(this.offset.click.left=this.helperProportions.width-t.right+this.margins.left),"top"in t&&(this.offset.click.top=t.top+this.margins.top),"bottom"in t&&(this.offset.click.top=this.helperProportions.height-t.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var e=this.offsetParent.offset();return"absolute"===this.cssPosition&&this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])&&(e.left+=this.scrollParent.scrollLeft(),e.top+=this.scrollParent.scrollTop()),this.offsetParent[0]===this.document[0].body&&(e={top:0,left:0}),{top:e.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:e.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"===this.cssPosition){var t=this.currentItem.position();return{top:t.top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:t.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}}return{top:0,left:0}},_cacheMargins:function(){this.margins={left:parseInt(this.currentItem.css("marginLeft"),10)||0,top:parseInt(this.currentItem.css("marginTop"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var e,i,n,s=this.options;"parent"===s.containment&&(s.containment=this.helper[0].parentNode),"document"!==s.containment&&"window"!==s.containment||(this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-this.offset.parent.top,"document"===s.containment?this.document.width():this.window.width()-this.helperProportions.width-this.margins.left,("document"===s.containment?this.document.height()||document.body.parentNode.scrollHeight:this.window.height()||this.document[0].body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]),/^(document|window|parent)$/.test(s.containment)||(e=t(s.containment)[0],i=t(s.containment).offset(),n="hidden"!==t(e).css("overflow"),this.containment=[i.left+(parseInt(t(e).css("borderLeftWidth"),10)||0)+(parseInt(t(e).css("paddingLeft"),10)||0)-this.margins.left,i.top+(parseInt(t(e).css("borderTopWidth"),10)||0)+(parseInt(t(e).css("paddingTop"),10)||0)-this.margins.top,i.left+(n?Math.max(e.scrollWidth,e.offsetWidth):e.offsetWidth)-(parseInt(t(e).css("borderLeftWidth"),10)||0)-(parseInt(t(e).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left,i.top+(n?Math.max(e.scrollHeight,e.offsetHeight):e.offsetHeight)-(parseInt(t(e).css("borderTopWidth"),10)||0)-(parseInt(t(e).css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top])},_convertPositionTo:function(e,i){i||(i=this.position);var n="absolute"===e?1:-1,s="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,o=/(html|body)/i.test(s[0].tagName);return{top:i.top+this.offset.relative.top*n+this.offset.parent.top*n-("fixed"===this.cssPosition?-this.scrollParent.scrollTop():o?0:s.scrollTop())*n,left:i.left+this.offset.relative.left*n+this.offset.parent.left*n-("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():o?0:s.scrollLeft())*n}},_generatePosition:function(e){var i,n,s=this.options,o=e.pageX,r=e.pageY,a="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,l=/(html|body)/i.test(a[0].tagName);return"relative"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&this.scrollParent[0]!==this.offsetParent[0]||(this.offset.relative=this._getRelativeOffset()),this.originalPosition&&(this.containment&&(e.pageX-this.offset.click.left<this.containment[0]&&(o=this.containment[0]+this.offset.click.left),e.pageY-this.offset.click.top<this.containment[1]&&(r=this.containment[1]+this.offset.click.top),e.pageX-this.offset.click.left>this.containment[2]&&(o=this.containment[2]+this.offset.click.left),e.pageY-this.offset.click.top>this.containment[3]&&(r=this.containment[3]+this.offset.click.top)),s.grid&&(i=this.originalPageY+Math.round((r-this.originalPageY)/s.grid[1])*s.grid[1],r=this.containment?i-this.offset.click.top>=this.containment[1]&&i-this.offset.click.top<=this.containment[3]?i:i-this.offset.click.top>=this.containment[1]?i-s.grid[1]:i+s.grid[1]:i,n=this.originalPageX+Math.round((o-this.originalPageX)/s.grid[0])*s.grid[0],o=this.containment?n-this.offset.click.left>=this.containment[0]&&n-this.offset.click.left<=this.containment[2]?n:n-this.offset.click.left>=this.containment[0]?n-s.grid[0]:n+s.grid[0]:n)),{top:r-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.scrollParent.scrollTop():l?0:a.scrollTop()),left:o-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():l?0:a.scrollLeft())}},_rearrange:function(t,e,i,n){i?i[0].appendChild(this.placeholder[0]):e.item[0].parentNode.insertBefore(this.placeholder[0],"down"===this.direction?e.item[0]:e.item[0].nextSibling),this.counter=this.counter?++this.counter:1;var s=this.counter;this._delay((function(){s===this.counter&&this.refreshPositions(!n)}))},_clear:function(t,e){this.reverting=!1;var i,n=[];if(!this._noFinalSort&&this.currentItem.parent().length&&this.placeholder.before(this.currentItem),this._noFinalSort=null,this.helper[0]===this.currentItem[0]){for(i in this._storedCSS)"auto"!==this._storedCSS[i]&&"static"!==this._storedCSS[i]||(this._storedCSS[i]="");this.currentItem.css(this._storedCSS),this._removeClass(this.currentItem,"ui-sortable-helper")}else this.currentItem.show();function s(t,e,i){return function(n){i._trigger(t,n,e._uiHash(e))}}for(this.fromOutside&&!e&&n.push((function(t){this._trigger("receive",t,this._uiHash(this.fromOutside))})),!this.fromOutside&&this.domPosition.prev===this.currentItem.prev().not(".ui-sortable-helper")[0]&&this.domPosition.parent===this.currentItem.parent()[0]||e||n.push((function(t){this._trigger("update",t,this._uiHash())})),this!==this.currentContainer&&(e||(n.push((function(t){this._trigger("remove",t,this._uiHash())})),n.push(function(t){return function(e){t._trigger("receive",e,this._uiHash(this))}}.call(this,this.currentContainer)),n.push(function(t){return function(e){t._trigger("update",e,this._uiHash(this))}}.call(this,this.currentContainer)))),i=this.containers.length-1;i>=0;i--)e||n.push(s("deactivate",this,this.containers[i])),this.containers[i].containerCache.over&&(n.push(s("out",this,this.containers[i])),this.containers[i].containerCache.over=0);if(this._storedStylesheet&&(this._storedStylesheet.remove(),this._storedStylesheet=null),this._storedOpacity&&this.helper.css("opacity",this._storedOpacity),this._storedZIndex&&this.helper.css("zIndex","auto"===this._storedZIndex?"":this._storedZIndex),this.dragging=!1,e||this._trigger("beforeStop",t,this._uiHash()),this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.cancelHelperRemoval||(this.helper[0]!==this.currentItem[0]&&this.helper.remove(),this.helper=null),!e){for(i=0;i<n.length;i++)n[i].call(this,t);this._trigger("stop",t,this._uiHash())}return this.fromOutside=!1,!this.cancelHelperRemoval},_trigger:function(){!1===t.Widget.prototype._trigger.apply(this,arguments)&&this.cancel()},_uiHash:function(e){var i=e||this;return{helper:i.helper,placeholder:i.placeholder||t([]),position:i.position,originalPosition:i.originalPosition,offset:i.positionAbs,item:i.currentItem,sender:e?e.element:null}}});function H(t){return function(){var e=this.element.val();t.apply(this,arguments),this._refresh(),e!==this.element.val()&&this._trigger("change")}}t.widget("ui.spinner",{version:"1.14.1",defaultElement:"<input>",widgetEventPrefix:"spin",options:{classes:{"ui-spinner":"ui-corner-all","ui-spinner-down":"ui-corner-br","ui-spinner-up":"ui-corner-tr"},culture:null,icons:{down:"ui-icon-triangle-1-s",up:"ui-icon-triangle-1-n"},incremental:!0,max:null,min:null,numberFormat:null,page:10,step:1,change:null,spin:null,start:null,stop:null},_create:function(){this._setOption("max",this.options.max),this._setOption("min",this.options.min),this._setOption("step",this.options.step),""!==this.value()&&this._value(this.element.val(),!0),this._draw(),this._on(this._events),this._refresh(),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_getCreateOptions:function(){var e=this._super(),i=this.element;return t.each(["min","max","step"],(function(t,n){var s=i.attr(n);null!=s&&s.length&&(e[n]=s)})),e},_events:{keydown:function(t){this._start(t)&&this._keydown(t)&&t.preventDefault()},keyup:"_stop",focus:function(){this.previous=this.element.val()},blur:function(t){this._stop(),this._refresh(),this.previous!==this.element.val()&&this._trigger("change",t)},mousewheel:function(t,e){var i=this.document[0].activeElement;if(this.element[0]===i&&e){if(!this.spinning&&!this._start(t))return!1;this._spin((e>0?1:-1)*this.options.step,t),clearTimeout(this.mousewheelTimer),this.mousewheelTimer=this._delay((function(){this.spinning&&this._stop(t)}),100),t.preventDefault()}},"mousedown .ui-spinner-button":function(e){var i;function n(){this.element[0]===this.document[0].activeElement||(this.element.trigger("focus"),this.previous=i)}i=this.element[0]===this.document[0].activeElement?this.previous:this.element.val(),e.preventDefault(),n.call(this),!1!==this._start(e)&&this._repeat(null,t(e.currentTarget).hasClass("ui-spinner-up")?1:-1,e)},"mouseup .ui-spinner-button":"_stop","mouseenter .ui-spinner-button":function(e){if(t(e.currentTarget).hasClass("ui-state-active"))return!1!==this._start(e)&&void this._repeat(null,t(e.currentTarget).hasClass("ui-spinner-up")?1:-1,e)},"mouseleave .ui-spinner-button":"_stop"},_enhance:function(){this.uiSpinner=this.element.attr("autocomplete","off").wrap("<span>").parent().append("<a></a><a></a>")},_draw:function(){this._enhance(),this._addClass(this.uiSpinner,"ui-spinner","ui-widget ui-widget-content"),this._addClass("ui-spinner-input"),this.element.attr("role","spinbutton"),this.buttons=this.uiSpinner.children("a").attr("tabIndex",-1).attr("aria-hidden",!0).button({classes:{"ui-button":""}}),this._removeClass(this.buttons,"ui-corner-all"),this._addClass(this.buttons.first(),"ui-spinner-button ui-spinner-up"),this._addClass(this.buttons.last(),"ui-spinner-button ui-spinner-down"),this.buttons.first().button({icon:this.options.icons.up,showLabel:!1}),this.buttons.last().button({icon:this.options.icons.down,showLabel:!1}),this.buttons.height()>Math.ceil(.5*this.uiSpinner.height())&&this.uiSpinner.height()>0&&this.uiSpinner.height(this.uiSpinner.height())},_keydown:function(e){var i=this.options,n=t.ui.keyCode;switch(e.keyCode){case n.UP:return this._repeat(null,1,e),!0;case n.DOWN:return this._repeat(null,-1,e),!0;case n.PAGE_UP:return this._repeat(null,i.page,e),!0;case n.PAGE_DOWN:return this._repeat(null,-i.page,e),!0}return!1},_start:function(t){return!(!this.spinning&&!1===this._trigger("start",t))&&(this.counter||(this.counter=1),this.spinning=!0,!0)},_repeat:function(t,e,i){t=t||500,clearTimeout(this.timer),this.timer=this._delay((function(){this._repeat(40,e,i)}),t),this._spin(e*this.options.step,i)},_spin:function(t,e){var i=this.value()||0;this.counter||(this.counter=1),i=this._adjustValue(i+t*this._increment(this.counter)),this.spinning&&!1===this._trigger("spin",e,{value:i})||(this._value(i),this.counter++)},_increment:function(t){var e=this.options.incremental;return e?"function"==typeof e?e(t):Math.floor(t*t*t/5e4-t*t/500+17*t/200+1):1},_precision:function(){var t=this._precisionOf(this.options.step);return null!==this.options.min&&(t=Math.max(t,this._precisionOf(this.options.min))),t},_precisionOf:function(t){var e=t.toString(),i=e.indexOf(".");return-1===i?0:e.length-i-1},_adjustValue:function(t){var e,i,n=this.options;return i=t-(e=null!==n.min?n.min:0),t=e+(i=Math.round(i/n.step)*n.step),t=parseFloat(t.toFixed(this._precision())),null!==n.max&&t>n.max?n.max:null!==n.min&&t<n.min?n.min:t},_stop:function(t){this.spinning&&(clearTimeout(this.timer),clearTimeout(this.mousewheelTimer),this.counter=0,this.spinning=!1,this._trigger("stop",t))},_setOption:function(t,e){var i,n,s;if("culture"===t||"numberFormat"===t)return i=this._parse(this.element.val()),this.options[t]=e,void this.element.val(this._format(i));"max"!==t&&"min"!==t&&"step"!==t||"string"==typeof e&&(e=this._parse(e)),"icons"===t&&(n=this.buttons.first().find(".ui-icon"),this._removeClass(n,null,this.options.icons.up),this._addClass(n,null,e.up),s=this.buttons.last().find(".ui-icon"),this._removeClass(s,null,this.options.icons.down),this._addClass(s,null,e.down)),this._super(t,e)},_setOptionDisabled:function(t){this._super(t),this._toggleClass(this.uiSpinner,null,"ui-state-disabled",!!t),this.element.prop("disabled",!!t),this.buttons.button(t?"disable":"enable")},_setOptions:H((function(t){this._super(t)})),_parse:function(t){return"string"==typeof t&&""!==t&&(t=window.Globalize&&this.options.numberFormat?Globalize.parseFloat(t,10,this.options.culture):+t),""===t||isNaN(t)?null:t},_format:function(t){return""===t?"":window.Globalize&&this.options.numberFormat?Globalize.format(t,this.options.numberFormat,this.options.culture):t},_refresh:function(){this.element.attr({"aria-valuemin":this.options.min,"aria-valuemax":this.options.max,"aria-valuenow":this._parse(this.element.val())})},isValid:function(){var t=this.value();return null!==t&&t===this._adjustValue(t)},_value:function(t,e){var i;""!==t&&null!==(i=this._parse(t))&&(e||(i=this._adjustValue(i)),t=this._format(i)),this.element.val(t),this._refresh()},_destroy:function(){this.element.prop("disabled",!1).removeAttr("autocomplete role aria-valuemin aria-valuemax aria-valuenow"),this.uiSpinner.replaceWith(this.element)},stepUp:H((function(t){this._stepUp(t)})),_stepUp:function(t){this._start()&&(this._spin((t||1)*this.options.step),this._stop())},stepDown:H((function(t){this._stepDown(t)})),_stepDown:function(t){this._start()&&(this._spin((t||1)*-this.options.step),this._stop())},pageUp:H((function(t){this._stepUp((t||1)*this.options.page)})),pageDown:H((function(t){this._stepDown((t||1)*this.options.page)})),value:function(t){if(!arguments.length)return this._parse(this.element.val());H(this._value).call(this,t)},widget:function(){return this.uiSpinner}}),!0===t.uiBackCompat&&t.widget("ui.spinner",t.ui.spinner,{_enhance:function(){this.uiSpinner=this.element.attr("autocomplete","off").wrap(this._uiSpinnerHtml()).parent().append(this._buttonHtml())},_uiSpinnerHtml:function(){return"<span>"},_buttonHtml:function(){return"<a></a><a></a>"}});t.ui.spinner;t.widget("ui.tabs",{version:"1.14.1",delay:300,options:{active:null,classes:{"ui-tabs":"ui-corner-all","ui-tabs-nav":"ui-corner-all","ui-tabs-panel":"ui-corner-bottom","ui-tabs-tab":"ui-corner-top"},collapsible:!1,event:"click",heightStyle:"content",hide:null,show:null,activate:null,beforeActivate:null,beforeLoad:null,load:null},_isLocal:function(){var t=/#.*$/;return function(e){var i,n;i=e.href.replace(t,""),n=location.href.replace(t,"");try{i=decodeURIComponent(i)}catch(t){}try{n=decodeURIComponent(n)}catch(t){}return e.hash.length>1&&i===n}}(),_create:function(){var e=this,i=this.options;this.running=!1,this._addClass("ui-tabs","ui-widget ui-widget-content"),this._toggleClass("ui-tabs-collapsible",null,i.collapsible),this._processTabs(),i.active=this._initialActive(),Array.isArray(i.disabled)&&(i.disabled=t.uniqueSort(i.disabled.concat(t.map(this.tabs.filter(".ui-state-disabled"),(function(t){return e.tabs.index(t)})))).sort()),!1!==this.options.active&&this.anchors.length?this.active=this._findActive(i.active):this.active=t(),this._refresh(),this.active.length&&this.load(i.active)},_initialActive:function(){var e=this.options.active,i=this.options.collapsible,n=decodeURIComponent(location.hash.substring(1));return null===e&&(n&&this.tabs.each((function(i,s){if(t(s).attr("aria-controls")===n)return e=i,!1})),null===e&&(e=this.tabs.index(this.tabs.filter(".ui-tabs-active"))),null!==e&&-1!==e||(e=!!this.tabs.length&&0)),!1!==e&&-1===(e=this.tabs.index(this.tabs.eq(e)))&&(e=!i&&0),!i&&!1===e&&this.anchors.length&&(e=0),e},_getCreateEventData:function(){return{tab:this.active,panel:this.active.length?this._getPanelForTab(this.active):t()}},_tabKeydown:function(e){var i=t(this.document[0].activeElement).closest("li"),n=this.tabs.index(i),s=!0;if(!this._handlePageNav(e)){switch(e.keyCode){case t.ui.keyCode.RIGHT:case t.ui.keyCode.DOWN:n++;break;case t.ui.keyCode.UP:case t.ui.keyCode.LEFT:s=!1,n--;break;case t.ui.keyCode.END:n=this.anchors.length-1;break;case t.ui.keyCode.HOME:n=0;break;case t.ui.keyCode.SPACE:return e.preventDefault(),clearTimeout(this.activating),void this._activate(n);case t.ui.keyCode.ENTER:return e.preventDefault(),clearTimeout(this.activating),void this._activate(n!==this.options.active&&n);default:return}e.preventDefault(),clearTimeout(this.activating),n=this._focusNextTab(n,s),e.ctrlKey||e.metaKey||(i.attr("aria-selected","false"),this.tabs.eq(n).attr("aria-selected","true"),this.activating=this._delay((function(){this.option("active",n)}),this.delay))}},_panelKeydown:function(e){this._handlePageNav(e)||e.ctrlKey&&e.keyCode===t.ui.keyCode.UP&&(e.preventDefault(),this.active.trigger("focus"))},_handlePageNav:function(e){return e.altKey&&e.keyCode===t.ui.keyCode.PAGE_UP?(this._activate(this._focusNextTab(this.options.active-1,!1)),!0):e.altKey&&e.keyCode===t.ui.keyCode.PAGE_DOWN?(this._activate(this._focusNextTab(this.options.active+1,!0)),!0):void 0},_findNextTab:function(e,i){var n=this.tabs.length-1;function s(){return e>n&&(e=0),e<0&&(e=n),e}for(;-1!==t.inArray(s(),this.options.disabled);)e=i?e+1:e-1;return e},_focusNextTab:function(t,e){return t=this._findNextTab(t,e),this.tabs.eq(t).trigger("focus"),t},_setOption:function(t,e){"active"!==t?(this._super(t,e),"collapsible"===t&&(this._toggleClass("ui-tabs-collapsible",null,e),e||!1!==this.options.active||this._activate(0)),"event"===t&&this._setupEvents(e),"heightStyle"===t&&this._setupHeightStyle(e)):this._activate(e)},refresh:function(){var e=this.options,i=this.tablist.children(":has(a[href])");e.disabled=t.map(i.filter(".ui-state-disabled"),(function(t){return i.index(t)})),this._processTabs(),!1!==e.active&&this.anchors.length?this.active.length&&!t.contains(this.tablist[0],this.active[0])?this.tabs.length===e.disabled.length?(e.active=!1,this.active=t()):this._activate(this._findNextTab(Math.max(0,e.active-1),!1)):e.active=this.tabs.index(this.active):(e.active=!1,this.active=t()),this._refresh()},_refresh:function(){this._setOptionDisabled(this.options.disabled),this._setupEvents(this.options.event),this._setupHeightStyle(this.options.heightStyle),this.tabs.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1}),this.panels.not(this._getPanelForTab(this.active)).hide().attr({"aria-hidden":"true"}),this.active.length?(this.active.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}),this._addClass(this.active,"ui-tabs-active","ui-state-active"),this._getPanelForTab(this.active).show().attr({"aria-hidden":"false"})):this.tabs.eq(0).attr("tabIndex",0)},_processTabs:function(){var e=this,i=this.tabs,n=this.anchors,s=this.panels;this.tablist=this._getList().attr("role","tablist"),this._addClass(this.tablist,"ui-tabs-nav","ui-helper-reset ui-helper-clearfix ui-widget-header"),this.tablist.on("mousedown"+this.eventNamespace,"> li",(function(e){t(this).is(".ui-state-disabled")&&e.preventDefault()})),this.tabs=this.tablist.find("> li:has(a[href])").attr({role:"tab",tabIndex:-1}),this._addClass(this.tabs,"ui-tabs-tab","ui-state-default"),this.anchors=this.tabs.map((function(){return t("a",this)[0]})).attr({tabIndex:-1}),this._addClass(this.anchors,"ui-tabs-anchor"),this.panels=t(),this.anchors.each((function(i,n){var s,o,r,a=t(n).uniqueId().attr("id"),l=t(n).closest("li"),h=l.attr("aria-controls");e._isLocal(n)?(r=(s=decodeURIComponent(n.hash)).substring(1),o=e.element.find("#"+CSS.escape(r))):(s="#"+(r=l.attr("aria-controls")||t({}).uniqueId()[0].id),(o=e.element.find(s)).length||(o=e._createPanel(r)).insertAfter(e.panels[i-1]||e.tablist),o.attr("aria-live","polite")),o.length&&(e.panels=e.panels.add(o)),h&&l.data("ui-tabs-aria-controls",h),l.attr({"aria-controls":r,"aria-labelledby":a}),o.attr("aria-labelledby",a)})),this.panels.attr("role","tabpanel"),this._addClass(this.panels,"ui-tabs-panel","ui-widget-content"),i&&(this._off(i.not(this.tabs)),this._off(n.not(this.anchors)),this._off(s.not(this.panels)))},_getList:function(){return this.tablist||this.element.find("ol, ul").eq(0)},_createPanel:function(e){return t("<div>").attr("id",e).data("ui-tabs-destroy",!0)},_setOptionDisabled:function(e){var i,n,s;for(Array.isArray(e)&&(e.length?e.length===this.anchors.length&&(e=!0):e=!1),s=0;n=this.tabs[s];s++)i=t(n),!0===e||-1!==t.inArray(s,e)?(i.attr("aria-disabled","true"),this._addClass(i,null,"ui-state-disabled")):(i.removeAttr("aria-disabled"),this._removeClass(i,null,"ui-state-disabled"));this.options.disabled=e,this._toggleClass(this.widget(),this.widgetFullName+"-disabled",null,!0===e)},_setupEvents:function(e){var i={};e&&t.each(e.split(" "),(function(t,e){i[e]="_eventHandler"})),this._off(this.anchors.add(this.tabs).add(this.panels)),this._on(!0,this.anchors,{click:function(t){t.preventDefault()}}),this._on(this.anchors,i),this._on(this.tabs,{keydown:"_tabKeydown"}),this._on(this.panels,{keydown:"_panelKeydown"}),this._focusable(this.tabs),this._hoverable(this.tabs)},_setupHeightStyle:function(e){var i,n=this.element.parent();"fill"===e?(i=n.height(),i-=this.element.outerHeight()-this.element.height(),this.element.siblings(":visible").each((function(){var e=t(this),n=e.css("position");"absolute"!==n&&"fixed"!==n&&(i-=e.outerHeight(!0))})),this.element.children().not(this.panels).each((function(){i-=t(this).outerHeight(!0)})),this.panels.each((function(){t(this).height(Math.max(0,i-t(this).innerHeight()+t(this).height()))})).css("overflow","auto")):"auto"===e&&(i=0,this.panels.each((function(){i=Math.max(i,t(this).height("").height())})).height(i))},_eventHandler:function(e){var i=this.options,n=this.active,s=t(e.currentTarget).closest("li"),o=s[0]===n[0],r=o&&i.collapsible,a=r?t():this._getPanelForTab(s),l=n.length?this._getPanelForTab(n):t(),h={oldTab:n,oldPanel:l,newTab:r?t():s,newPanel:a};e.preventDefault(),s.hasClass("ui-state-disabled")||s.hasClass("ui-tabs-loading")||this.running||o&&!i.collapsible||!1===this._trigger("beforeActivate",e,h)||(i.active=!r&&this.tabs.index(s),this.active=o?t():s,this.xhr&&this.xhr.abort(),l.length||a.length||t.error("jQuery UI Tabs: Mismatching fragment identifier."),a.length&&this.load(this.tabs.index(s),e),this._toggle(e,h))},_toggle:function(e,i){var n=this,s=i.newPanel,o=i.oldPanel;function r(){n.running=!1,n._trigger("activate",e,i)}function a(){n._addClass(i.newTab.closest("li"),"ui-tabs-active","ui-state-active"),s.length&&n.options.show?n._show(s,n.options.show,r):(s.show(),r())}this.running=!0,o.length&&this.options.hide?this._hide(o,this.options.hide,(function(){n._removeClass(i.oldTab.closest("li"),"ui-tabs-active","ui-state-active"),a()})):(this._removeClass(i.oldTab.closest("li"),"ui-tabs-active","ui-state-active"),o.hide(),a()),o.attr("aria-hidden","true"),i.oldTab.attr({"aria-selected":"false","aria-expanded":"false"}),s.length&&o.length?i.oldTab.attr("tabIndex",-1):s.length&&this.tabs.filter((function(){return 0===t(this).attr("tabIndex")})).attr("tabIndex",-1),s.attr("aria-hidden","false"),i.newTab.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0})},_activate:function(e){var i,n=this._findActive(e);n[0]!==this.active[0]&&(n.length||(n=this.active),i=n.find(".ui-tabs-anchor")[0],this._eventHandler({target:i,currentTarget:i,preventDefault:t.noop}))},_findActive:function(e){return!1===e?t():this.tabs.eq(e)},_getIndex:function(t){return"string"==typeof t&&(t=this.anchors.index(this.anchors.filter("[href$='"+CSS.escape(t)+"']"))),t},_destroy:function(){this.xhr&&this.xhr.abort(),this.tablist.removeAttr("role").off(this.eventNamespace),this.anchors.removeAttr("role tabIndex").removeUniqueId(),this.tabs.add(this.panels).each((function(){t.data(this,"ui-tabs-destroy")?t(this).remove():t(this).removeAttr("role tabIndex aria-live aria-busy aria-selected aria-labelledby aria-hidden aria-expanded")})),this.tabs.each((function(){var e=t(this),i=e.data("ui-tabs-aria-controls");i?e.attr("aria-controls",i).removeData("ui-tabs-aria-controls"):e.removeAttr("aria-controls")})),this.panels.show(),"content"!==this.options.heightStyle&&this.panels.css("height","")},enable:function(e){var i=this.options.disabled;!1!==i&&(void 0===e?i=!1:(e=this._getIndex(e),i=Array.isArray(i)?t.map(i,(function(t){return t!==e?t:null})):t.map(this.tabs,(function(t,i){return i!==e?i:null}))),this._setOptionDisabled(i))},disable:function(e){var i=this.options.disabled;if(!0!==i){if(void 0===e)i=!0;else{if(e=this._getIndex(e),-1!==t.inArray(e,i))return;i=Array.isArray(i)?t.merge([e],i).sort():[e]}this._setOptionDisabled(i)}},load:function(e,i){e=this._getIndex(e);var n=this,s=this.tabs.eq(e),o=s.find(".ui-tabs-anchor"),r=this._getPanelForTab(s),a={tab:s,panel:r},l=function(t,e){"abort"===e&&n.panels.stop(!1,!0),n._removeClass(s,"ui-tabs-loading"),r.removeAttr("aria-busy"),t===n.xhr&&delete n.xhr};this._isLocal(o[0])||(this.xhr=t.ajax(this._ajaxSettings(o,i,a)),"canceled"!==this.xhr.statusText&&(this._addClass(s,"ui-tabs-loading"),r.attr("aria-busy","true"),this.xhr.done((function(t,e,s){r.html(t),n._trigger("load",i,a),l(s,e)})).fail((function(t,e){l(t,e)}))))},_ajaxSettings:function(e,i,n){var s=this;return{url:e.attr("href"),beforeSend:function(e,o){return s._trigger("beforeLoad",i,t.extend({jqXHR:e,ajaxSettings:o},n))}}},_getPanelForTab:function(e){var i=t(e).attr("aria-controls");return this.element.find("#"+CSS.escape(i))}}),!0===t.uiBackCompat&&t.widget("ui.tabs",t.ui.tabs,{_processTabs:function(){this._superApply(arguments),this._addClass(this.tabs,"ui-tab")}});t.ui.tabs;t.widget("ui.tooltip",{version:"1.14.1",options:{classes:{"ui-tooltip":"ui-corner-all ui-widget-shadow"},content:function(){var e=t(this).attr("title");return t("<a>").text(e).html()},hide:!0,items:"[title]:not([disabled])",position:{my:"left top+15",at:"left bottom",collision:"flipfit flip"},show:!0,track:!1,close:null,open:null},_addDescribedBy:function(t,e){var i=(t.attr("aria-describedby")||"").split(/\s+/);i.push(e),t.data("ui-tooltip-id",e).attr("aria-describedby",String.prototype.trim.call(i.join(" ")))},_removeDescribedBy:function(e){var i=e.data("ui-tooltip-id"),n=(e.attr("aria-describedby")||"").split(/\s+/),s=t.inArray(i,n);-1!==s&&n.splice(s,1),e.removeData("ui-tooltip-id"),(n=String.prototype.trim.call(n.join(" ")))?e.attr("aria-describedby",n):e.removeAttr("aria-describedby")},_create:function(){this._on({mouseover:"open",focusin:"open"}),this.tooltips={},this.parents={},this.liveRegion=t("<div>").attr({role:"log","aria-live":"assertive","aria-relevant":"additions"}).appendTo(this.document[0].body),this._addClass(this.liveRegion,null,"ui-helper-hidden-accessible"),this.disabledTitles=t([])},_setOption:function(e,i){var n=this;this._super(e,i),"content"===e&&t.each(this.tooltips,(function(t,e){n._updateContent(e.element)}))},_setOptionDisabled:function(t){this[t?"_disable":"_enable"]()},_disable:function(){var e=this;t.each(this.tooltips,(function(i,n){var s=t.Event("blur");s.target=s.currentTarget=n.element[0],e.close(s,!0)})),this.disabledTitles=this.disabledTitles.add(this.element.find(this.options.items).addBack().filter((function(){var e=t(this);if(e.is("[title]"))return e.data("ui-tooltip-title",e.attr("title")).removeAttr("title")})))},_enable:function(){this.disabledTitles.each((function(){var e=t(this);e.data("ui-tooltip-title")&&e.attr("title",e.data("ui-tooltip-title"))})),this.disabledTitles=t([])},open:function(e){var i=this,n=t(e?e.target:this.element).closest(this.options.items);n.length&&!n.data("ui-tooltip-id")&&(n.attr("title")&&n.data("ui-tooltip-title",n.attr("title")),n.data("ui-tooltip-open",!0),e&&"mouseover"===e.type&&n.parents().each((function(){var e,n=t(this);n.data("ui-tooltip-open")&&((e=t.Event("blur")).target=e.currentTarget=this,i.close(e,!0)),n.attr("title")&&(n.uniqueId(),i.parents[this.id]={element:this,title:n.attr("title")},n.attr("title",""))})),this._registerCloseHandlers(e,n),this._updateContent(n,e))},_updateContent:function(t,e){var i,n=this.options.content,s=this,o=e?e.type:null;if("string"==typeof n||n.nodeType||n.jquery)return this._open(e,t,n);(i=n.call(t[0],(function(i){t.data("ui-tooltip-open")&&(e&&(e.type=o),s._open(e,t,i))})))&&this._open(e,t,i)},_open:function(e,i,n){var s,o,r,a,l=t.extend({},this.options.position);function h(t){l.of=t,o.is(":hidden")||o.position(l)}n&&((s=this._find(i))?s.tooltip.find(".ui-tooltip-content").html(n):(i.is("[title]")&&(e&&"mouseover"===e.type?i.attr("title",""):i.removeAttr("title")),s=this._tooltip(i),o=s.tooltip,this._addDescribedBy(i,o.attr("id")),o.find(".ui-tooltip-content").html(n),this.liveRegion.children().hide(),(a=t("<div>").html(o.find(".ui-tooltip-content").html())).removeAttr("name").find("[name]").removeAttr("name"),a.removeAttr("id").find("[id]").removeAttr("id"),a.appendTo(this.liveRegion),this.options.track&&e&&/^mouse/.test(e.type)?(this._on(this.document,{mousemove:h}),h(e)):o.position(t.extend({of:i},this.options.position)),o.hide(),this._show(o,this.options.show),this.options.track&&this.options.show&&this.options.show.delay&&(r=this.delayedShow=setInterval((function(){o.is(":visible")&&(h(l.of),clearInterval(r))}),13)),this._trigger("open",e,{tooltip:o})))},_registerCloseHandlers:function(e,i){var n={keyup:function(e){if(e.keyCode===t.ui.keyCode.ESCAPE){var n=t.Event(e);n.currentTarget=i[0],this.close(n,!0)}}};i[0]!==this.element[0]&&(n.remove=function(){var t=this._find(i);t&&this._removeTooltip(t.tooltip)}),e&&"mouseover"!==e.type||(n.mouseleave="close"),e&&"focusin"!==e.type||(n.focusout="close"),this._on(!0,i,n)},close:function(e){var i,n=this,s=t(e?e.currentTarget:this.element),o=this._find(s);o?(i=o.tooltip,o.closing||(clearInterval(this.delayedShow),s.data("ui-tooltip-title")&&!s.attr("title")&&s.attr("title",s.data("ui-tooltip-title")),this._removeDescribedBy(s),o.hiding=!0,i.stop(!0),this._hide(i,this.options.hide,(function(){n._removeTooltip(t(this))})),s.removeData("ui-tooltip-open"),this._off(s,"mouseleave focusout keyup"),s[0]!==this.element[0]&&this._off(s,"remove"),this._off(this.document,"mousemove"),e&&"mouseleave"===e.type&&t.each(this.parents,(function(e,i){t(i.element).attr("title",i.title),delete n.parents[e]})),o.closing=!0,this._trigger("close",e,{tooltip:i}),o.hiding||(o.closing=!1))):s.removeData("ui-tooltip-open")},_tooltip:function(e){var i=t("<div>").attr("role","tooltip"),n=t("<div>").appendTo(i),s=i.uniqueId().attr("id");return this._addClass(n,"ui-tooltip-content"),this._addClass(i,"ui-tooltip","ui-widget ui-widget-content"),i.appendTo(this._appendTo(e)),this.tooltips[s]={element:e,tooltip:i}},_find:function(t){var e=t.data("ui-tooltip-id");return e?this.tooltips[e]:null},_removeTooltip:function(t){clearInterval(this.delayedShow),t.remove(),delete this.tooltips[t.attr("id")]},_appendTo:function(t){var e=t.closest(".ui-front, dialog");return e.length||(e=this.document[0].body),e},_destroy:function(){var e=this;t.each(this.tooltips,(function(i,n){var s=t.Event("blur"),o=n.element;s.target=s.currentTarget=o[0],e.close(s,!0),t("#"+i).remove(),o.data("ui-tooltip-title")&&(o.attr("title")||o.attr("title",o.data("ui-tooltip-title")),o.removeData("ui-tooltip-title"))})),this.liveRegion.remove()}}),!0===t.uiBackCompat&&t.widget("ui.tooltip",t.ui.tooltip,{options:{tooltipClass:null},_tooltip:function(){var t=this._superApply(arguments);return this.options.tooltipClass&&t.tooltip.addClass(this.options.tooltipClass),t}});t.ui.tooltip},void 0===(o="function"==typeof n?n.apply(e,s):n)||(t.exports=o)}()}},e={};function i(n){var s=e[n];if(void 0!==s)return s.exports;var o=e[n]={exports:{}};return t[n].call(o.exports,o,o.exports,i),o.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i(692),i(910),i(409),i(328),i(590),i(428);i(735)})();