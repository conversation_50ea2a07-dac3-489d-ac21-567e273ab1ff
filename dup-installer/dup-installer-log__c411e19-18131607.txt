********************************************************************************
* DUPLICATOR-PRO: Install-Log
* STEP-0 START @ 01:21:44
* NOTICE: Do NOT post to public sites or forums!!
********************************************************************************
PACKAGE INFO________ ORIGINAL SERVER                        |CURRENT SERVER
OS__________________: Linux                                 |Darwin
PHP VERSION_________: 8.2.29                                |8.3.25
********************************************************************************
CURRENT SERVER INFO
PHP_________________: 8.3.25 | SAPI: fpm-fcgi
PHP MEMORY__________: 4294967296 | SUHOSIN: disabled
ARCHITECTURE________: 64-bit
SERVER______________: nginx/1.25.4
DOC ROOT____________: "/Users/<USER>/Sites/brandfast"
REQUEST URL_________: "https://brandfast.test"
********************************************************************************
OVERWRITE PARAMS
 *** FROM PACKAGE
PARAM SET KEY[cpnl-dbaction]
********************************************************************************
.htaccess file was found in dup-installer folder and it was renamed to avoid interference with installer.
CAN'T REMOVE MAINTENANCE MODE, ROOT FOLDER NOT WRITABLE
INSTALLER INFO

INSTALL MODES_______: CLASSIC
TEMPLATE____________: "base"
VALIDATE ON START___: "normal"
PATH_NEW____________: "/Users/<USER>/Sites/brandfast"
URL_NEW_____________: "https://brandfast.test"
********************************************************************************
ARCHIVE INFO

ARCHIVE NAME________: "/Users/<USER>/Sites/brandfast/20250918_brandfast_[HASH]_20250918131607_archive.zip"
ARCHIVE SIZE________: 150.72MB
CREATED_____________: 2025-09-18 13:16:07
WP VERSION__________: 6.8.1
DUP VERSION_________: ********
LICENSE_____________: Elite
PACKAGE COMPONENTS__: Database, Core, Plugins, Themes, Media, Other
DB VERSION__________: 8.4.6
DB FILE SIZE________: 229.95MB
DB TABLES___________: 106
DB ROWS_____________: 764066
URL HOME____________: https://unlocked.brandfast.nu
URL CORE____________: https://unlocked.brandfast.nu/wordpress
URL CONTENT_________: https://unlocked.brandfast.nu
URL UPLOAD__________: https://unlocked.brandfast.nu/uploads
URL PLUGINS_________: https://unlocked.brandfast.nu/plugins
URL MU PLUGINS______: https://unlocked.brandfast.nu/mu-plugins
URL THEMES__________: https://unlocked.brandfast.nu/themes
PATH HOME___________: /sites/unlocked.brandfast.nu/files/public
PATH ABS____________: /sites/unlocked.brandfast.nu/files/public/wordpress
PATH WPCONFIG_______: /sites/unlocked.brandfast.nu/files/public
PATH WPCONTENT______: /sites/unlocked.brandfast.nu/files/public
PATH UPLOADS________: /sites/unlocked.brandfast.nu/files/public/uploads
PATH PLUGINS________: /sites/unlocked.brandfast.nu/files/public/plugins
PATH MUPLUGINS______: /sites/unlocked.brandfast.nu/files/public/mu-plugins
PATH THEMES_________: /sites/unlocked.brandfast.nu/files/public/themes

SUBSITES
SUBSITE [ID:   1] "unlocked.brandfast.nu/"

PLUGINS
PLUGIN [SLUG:additional-products/additional-products.php       ][ON:true ]  Additional products
PLUGIN [SLUG:advanced-custom-fields-pro/acf.php                ][ON:true ]  Advanced Custom Fields PRO
PLUGIN [SLUG:algolia-search/algolia-search.php                 ][ON:true ]  Algolia search
PLUGIN [SLUG:webp-converter-for-media/webp-converter-for-media.php][ON:true ]  Converter for Media
PLUGIN [SLUG:cookiebot/cookiebot.php                           ][ON:true ]  Cookie Banner & Privacy Compliance for GDPR/CCPA/Google Consent Mode – Usercentrics Cookiebot
PLUGIN [SLUG:webappick-product-feed-for-woocommerce/woo-feed.php][ON:false]  CTX Feed
PLUGIN [SLUG:product-feed-for-woocommerce-pro/webappick-product-feed-for-woocommerce-pro.php][ON:true ]  CTX Feed Pro
PLUGIN [SLUG:disable-comments/disable-comments.php             ][ON:true ]  Disable Comments
PLUGIN [SLUG:disable-emojis/disable-emojis.php                 ][ON:true ]  Disable Emojis (GDPR friendly)
PLUGIN [SLUG:disable-wp-rest-api/disable-wp-rest-api.php       ][ON:false]  Disable WP REST API
PLUGIN [SLUG:duplicator-pro/duplicator-pro.php                 ][ON:true ]  Duplicator Pro
PLUGIN [SLUG:easy-wp-smtp/easy-wp-smtp.php                     ][ON:true ]  Easy WP SMTP
PLUGIN [SLUG:facebook-for-woocommerce/facebook-for-woocommerce.php][ON:true ]  Facebook for WooCommerce
PLUGIN [SLUG:favicon-by-realfavicongenerator/favicon-by-realfavicongenerator.php][ON:true ]  Favicon by RealFaviconGenerator
PLUGIN [SLUG:feeds-for-youtube/youtube-feed.php                ][ON:true ]  Feeds for YouTube
PLUGIN [SLUG:woocommerce-fortnox-integration/plugin.php        ][ON:true ]  Fortnox integration for WooCommerce
PLUGIN [SLUG:woocommerce-google-analytics-integration/woocommerce-google-analytics-integration.php][ON:false]  Google Analytics for WooCommerce
PLUGIN [SLUG:gravity-forms/gravityforms.php                    ][ON:true ]  Gravity Forms
PLUGIN [SLUG:gravity-forms-google-analytics-event-tracking/gravity-forms-event-tracking.php][ON:true ]  Gravity Forms Event Tracking
PLUGIN [SLUG:gravity-forms-recaptcha-add-on/recaptcha.php      ][ON:true ]  Gravity Forms reCAPTCHA Add-On
PLUGIN [SLUG:duracelltomi-google-tag-manager/duracelltomi-google-tag-manager-for-wordpress.php][ON:true ]  GTM4WP - A Google Tag Manager (GTM) plugin for WordPress
PLUGIN [SLUG:gutenberg/gutenberg.php                           ][ON:true ]  Gutenberg
PLUGIN [SLUG:hotjar/hotjar.php                                 ][ON:true ]  Hotjar
PLUGIN [SLUG:limit-login-attempts-reloaded/limit-login-attempts-reloaded.php][ON:true ]  Limit Login Attempts Reloaded
PLUGIN [SLUG:mailchimp-for-woocommerce/mailchimp-woocommerce.php][ON:true ]  Mailchimp for WooCommerce
PLUGIN [SLUG:mailchimp-for-wp/mailchimp-for-wp.php             ][ON:true ]  MC4WP: Mailchimp for WordPress
PLUGIN [SLUG:media-cleaner/media-cleaner.php                   ][ON:true ]  Media Cleaner
PLUGIN [SLUG:woocommerce-product-sku-generator/woocommerce-product-sku-generator.php][ON:false]  Product SKU Generator for WooCommerce
PLUGIN [SLUG:seo-by-rank-math/rank-math.php                    ][ON:true ]  Rank Math SEO
PLUGIN [SLUG:seo-by-rank-math-pro/rank-math-pro.php            ][ON:true ]  Rank Math SEO PRO
PLUGIN [SLUG:reco-for-woocommerce/reco-woo-plugin.php          ][ON:true ]  Reco For Woocommerce
PLUGIN [SLUG:redirection/redirection.php                       ][ON:true ]  Redirection
PLUGIN [SLUG:safe-svg/safe-svg.php                             ][ON:true ]  Safe SVG
PLUGIN [SLUG:google-site-kit/google-site-kit.php               ][ON:true ]  Site Kit by Google
PLUGIN [SLUG:instagram-feed/instagram-feed.php                 ][ON:true ]  Smash Balloon Instagram Feed
PLUGIN [SLUG:spinupwp/spinupwp.php                             ][ON:true ]  SpinupWP
PLUGIN [SLUG:svea-checkout-for-woocommerce/svea-checkout-for-woocommerce.php][ON:true ]  Svea Checkout for WooCommerce
PLUGIN [SLUG:eduadmin-api-client/vistrom-eduadmin-api.php      ][ON:true ]  Viström EduAdmin API
PLUGIN [SLUG:vistrom-media-library-categories/vistrom-media-library-categories.php][ON:true ]  Viström Media Library Categories
PLUGIN [SLUG:vistrom-slider/vistrom-slider.php                 ][ON:true ]  Viström Slider
PLUGIN [SLUG:woocommerce/woocommerce.php                       ][ON:true ]  WooCommerce
PLUGIN [SLUG:brands/woocommerce-brands.php                     ][ON:false]  WooCommerce Brands
PLUGIN [SLUG:woocommerce-legacy-rest-api/woocommerce-legacy-rest-api.php][ON:true ]  WooCommerce Legacy REST API
PLUGIN [SLUG:product-bundles/woocommerce-product-bundles.php   ][ON:true ]  WooCommerce Product Bundles
PLUGIN [SLUG:woocommerce-side-cart-premium/xoo-wsc-main.php    ][ON:true ]  Woocommerce Side Cart Premium
PLUGIN [SLUG:table-rate-shipping/woocommerce-table-rate-shipping.php][ON:true ]  WooCommerce Table Rate Shipping
PLUGIN [SLUG:wp-2fa/wp-2fa.php                                 ][ON:true ]  WP 2FA - Two-factor authentication for WordPress
PLUGIN [SLUG:wp-rocket/wp-rocket.php                           ][ON:false]  WP Rocket
PLUGIN [SLUG:wps-hide-login/wps-hide-login.php                 ][ON:true ]  WPS Hide Login
PLUGIN [SLUG:duplicate-post/duplicate-post.php                 ][ON:false]  Yoast Duplicate Post
PLUGIN [SLUG:mu-plugins.php                                    ][ON:false]  Must Use Plugins
PLUGIN [SLUG:spinupwp-debug-log-path.php                       ][ON:false]  SpinupWP Debug Log Path
PLUGIN [SLUG:advanced-cache.php                                ][ON:false]  advanced-cache.php
PLUGIN [SLUG:object-cache.php                                  ][ON:false]  SpinupWP Redis Object Cache Drop-In

********************************************************************************
LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/ctrls/ctrl.base.php:191][DELTA:   0.04490]  MESSAGE:END RENDER PAGE
LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [sparam_s1] START
DATABASE CONNECTION EXCEPTION ERROR: No such file or directory

INSTALLER ERROR:
Unable to connect with the following parameters:
HOST: "localhost"
DBUSER: "root"
DATABASE: "brandfast"
MESSAGE: Error: No such file or directory


DATABASE CHECK CHARSET EXCEPTION:  Unable to connect with the following parameters:<br/>HOST: "localhost"
DBUSER: "root"
DATABASE: "brandfast"
MESSAGE: Error: No such file or directory[CODE:0]
	FILE:/Users/<USER>/Sites/brandfast/dup-installer/src/Utils/Log/Log.php[410]
	TRACE:
#0 /Users/<USER>/Sites/brandfast/dup-installer/classes/database/class.db.functions.php(117): Duplicator\Installer\Utils\Log\Log::error('Unable to conne...')
#1 /Users/<USER>/Sites/brandfast/dup-installer/classes/validation/class.validation.database.service.php(74): DUPX_DB_Functions->dbConnection(Array)
#2 /Users/<USER>/Sites/brandfast/dup-installer/classes/validation/class.validation.database.service.php(1033): DUPX_Validation_database_service->getDbConnection()
#3 /Users/<USER>/Sites/brandfast/dup-installer/ctrls/classes/class.ctrl.params.php(377): DUPX_Validation_database_service->caseSensitiveTablesValue()
#4 /Users/<USER>/Sites/brandfast/dup-installer/ctrls/classes/class.ctrl.params.php(138): DUPX_Ctrl_Params::setParamsDatabase()
#5 /Users/<USER>/Sites/brandfast/dup-installer/ctrls/classes/class.ctrl.ajax.php(196): DUPX_Ctrl_Params::setParamsStep1()
#6 /Users/<USER>/Sites/brandfast/dup-installer/ctrls/classes/class.ctrl.ajax.php(110): DUPX_Ctrl_ajax::actions('sparam_s1')
#7 /Users/<USER>/Sites/brandfast/dup-installer/main.installer.php(47): DUPX_Ctrl_ajax::controller()
#8 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#9 {main}

AJAX ACTION [sparam_s1] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME
STEP ACTION: "on-validate"
LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/ctrls/ctrl.base.php:191][DELTA:   0.01685]  MESSAGE:END RENDER PAGE
LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [validate] START
START TEST "Archive Check" [CLASS: DUPX_Validation_test_archive_check]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Archive Check" RESULT: passed

START TEST "Duplicator importer version" [CLASS: DUPX_Validation_test_importer_version]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Duplicator importer version" RESULT: skip

START TEST "Overwrite Install" [CLASS: DUPX_Validation_test_owrinstall]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Overwrite Install" RESULT: good

START TEST "Disaster Recovery" [CLASS: DUPX_Validation_test_recovery]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Disaster Recovery" RESULT: skip

START TEST "Partial Backup Check" [CLASS: DUPX_Validation_test_importable]
LOG-TIME[DELTA:   0.00048]  MESSAGE:TEST "Partial Backup Check" RESULT: hard warning

START TEST "REST API test" [CLASS: DUPX_Validation_test_rest_api]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "REST API test" RESULT: skip

START TEST "Manual extraction detected" [CLASS: DUPX_Validation_test_manual_extraction]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Manual extraction detected" RESULT: good

START TEST "Database Only" [CLASS: DUPX_Validation_test_dbonly_iswordpress]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Database Only" RESULT: skip

START TEST "Package Age" [CLASS: DUPX_Validation_test_package_age]
LOG-TIME[DELTA:   0.00002]  MESSAGE:TEST "Package Age" RESULT: good

START TEST "Replace PATHs in database" [CLASS: DUPX_Validation_test_replace_paths]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Replace PATHs in database" RESULT: skip

START TEST "Managed hosting supported" [CLASS: DUPX_Validation_test_managed_supported]
LOG-TIME[DELTA:   0.00004]  MESSAGE:TEST "Managed hosting supported" RESULT: skip

START TEST "Siteground" [CLASS: DUPX_Validation_test_siteground]
LOG-TIME[DELTA:   0.00019]  MESSAGE:TEST "Siteground" RESULT: skip

START TEST "Subomain multisite installation in subfolder" [CLASS: DUPX_Validation_test_multisite_subfolder]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Subomain multisite installation in subfolder" RESULT: skip

START TEST "Addon Sites" [CLASS: DUPX_Validation_test_addon_sites]
--------------------------------------
PATHS MAPPING : "/Users/<USER>/Sites/brandfast"
--------------------------------------
LOG-TIME[DELTA:   0.01338]  MESSAGE:TEST "Addon Sites" RESULT: good

START TEST "Wordfence" [CLASS: DUPX_Validation_test_wordfence]
LOG-TIME[DELTA:   0.00039]  MESSAGE:TEST "Wordfence" RESULT: good

START TEST "Table prefix of managed hosting" [CLASS: DUPX_Validation_test_managed_tprefix]
LOG-TIME[DELTA:   0.00008]  MESSAGE:TEST "Table prefix of managed hosting" RESULT: skip

START TEST "Wordpress Configuration" [CLASS: DUPX_Validation_test_wp_config]
LOG-TIME[DELTA:   0.00157]  MESSAGE:TEST "Wordpress Configuration" RESULT: passed

START TEST "PHP Version Mismatch" [CLASS: DUPX_Validation_test_php_version]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Version Mismatch" RESULT: good

START TEST "PHP Open Base" [CLASS: DUPX_Validation_test_open_basedir]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Open Base" RESULT: good

START TEST "PHP Memory Limit" [CLASS: DUPX_Validation_test_memory_limit]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Memory Limit" RESULT: good

START TEST "PHP Extensions" [CLASS: DUPX_Validation_test_extensions]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Extensions" RESULT: good

START TEST "PHP Mysqli" [CLASS: DUPX_Validation_test_mysql_connect]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Mysqli" RESULT: passed

START TEST "PHP Functions and Classes" [CLASS: DUPX_Validation_test_php_functionalities]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Functions and Classes" RESULT: passed

START TEST "PHP Timeout" [CLASS: DUPX_Validation_test_timeout]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Timeout" RESULT: good

START TEST "Disk Space" [CLASS: DUPX_Validation_test_disk_space]
LOG-TIME[DELTA:   0.00004]  MESSAGE:TEST "Disk Space" RESULT: good

START TEST "Permissions: General" [CLASS: DUPX_Validation_test_iswritable]
LOG-TIME[DELTA:   0.02536]  MESSAGE:TEST "Permissions: General" RESULT: passed

START TEST "Permissions: Configs Files " [CLASS: DUPX_Validation_test_iswritable_configs]
LOG-TIME[DELTA:   0.00007]  MESSAGE:TEST "Permissions: Configs Files " RESULT: passed

START TEST "Extract only files" [CLASS: DUPX_Validation_test_db_excluded]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Extract only files" RESULT: passed

START TEST "Cpanel connection" [CLASS: DUPX_Validation_test_cpnl_connection]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Cpanel connection" RESULT: skip

START TEST "Create Database User" [CLASS: DUPX_Validation_test_cpnl_new_user]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Create Database User" RESULT: skip

START TEST "Host Name" [CLASS: DUPX_Validation_test_db_host_name]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Host Name" RESULT: passed

START TEST "Host Connection" [CLASS: DUPX_Validation_test_db_connection]
DATABASE CONNECTION EXCEPTION ERROR: No such file or directory

INSTALLER ERROR:
Unable to connect with the following parameters:
HOST: "localhost"
DBUSER: "root"
DATABASE: "brandfast"
MESSAGE: Error: No such file or directory


      TEST "Host Connection" EXCEPTION: Unable to connect with the following parameters:<br/>HOST: "localhost"
DBUSER: "root"
DATABASE: "brandfast"
MESSAGE: Error: No such file or directory[CODE:0]
	FILE:/Users/<USER>/Sites/brandfast/dup-installer/src/Utils/Log/Log.php[410]
	TRACE:
#0 /Users/<USER>/Sites/brandfast/dup-installer/classes/database/class.db.functions.php(117): Duplicator\Installer\Utils\Log\Log::error('Unable to conne...')
#1 /Users/<USER>/Sites/brandfast/dup-installer/classes/validation/class.validation.database.service.php(74): DUPX_DB_Functions->dbConnection(Array)
#2 /Users/<USER>/Sites/brandfast/dup-installer/classes/validation/database-tests/class.validation.test.db.connection.php(26): DUPX_Validation_database_service->getDbConnection()
#3 /Users/<USER>/Sites/brandfast/dup-installer/classes/validation/class.validation.abstract.item.php(56): DUPX_Validation_test_db_connection->runTest()
#4 /Users/<USER>/Sites/brandfast/dup-installer/classes/validation/class.validation.manager.php(246): DUPX_Validation_abstract_item->test(true)
#5 /Users/<USER>/Sites/brandfast/dup-installer/classes/validation/class.validation.manager.php(214): DUPX_Validation_manager->runTests()
#6 /Users/<USER>/Sites/brandfast/dup-installer/ctrls/classes/class.ctrl.ajax.php(186): DUPX_Validation_manager->getValidateData()
#7 /Users/<USER>/Sites/brandfast/dup-installer/ctrls/classes/class.ctrl.ajax.php(110): DUPX_Ctrl_ajax::actions('validate')
#8 /Users/<USER>/Sites/brandfast/dup-installer/main.installer.php(47): DUPX_Ctrl_ajax::controller()
#9 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#10 {main}

LOG-TIME[DELTA:   0.00013]  MESSAGE:TEST "Host Connection" RESULT: failed

START TEST "Database Version" [CLASS: DUPX_Validation_test_db_version]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Database Version" RESULT: skip

START TEST "Create New Database" [CLASS: DUPX_Validation_test_db_create]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Create New Database" RESULT: skip

START TEST "Database Engine Support" [CLASS: DUPX_Validation_test_db_supported_engine]
LOG-TIME[DELTA:   0.00000]  MESSAGE:TEST "Database Engine Support" RESULT: skip

START TEST "Database GTID Mode" [CLASS: DUPX_Validation_test_db_gtid_mode]
LOG-TIME[DELTA:   0.00000]  MESSAGE:TEST "Database GTID Mode" RESULT: skip

START TEST "Privileges: User Visibility" [CLASS: DUPX_Validation_test_db_visibility]
LOG-TIME[DELTA:   0.00000]  MESSAGE:TEST "Privileges: User Visibility" RESULT: skip

START TEST "Manual Table Check" [CLASS: DUPX_Validation_test_db_manual_tables_count]
LOG-TIME[DELTA:   0.00000]  MESSAGE:TEST "Manual Table Check" RESULT: skip

START TEST "Multiple WP Installs" [CLASS: DUPX_Validation_test_db_multiple_wp_installs]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Multiple WP Installs" RESULT: skip

START TEST "Privileges: User Resources" [CLASS: DUPX_Validation_test_db_user_resources]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Privileges: User Resources" RESULT: skip

START TEST "Privileges: User Table Access" [CLASS: DUPX_Validation_test_db_user_perms]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Privileges: User Table Access" RESULT: skip

START TEST "Privileges: 'Show Variables' Query" [CLASS: DUPX_Validation_test_db_custom_queries]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Privileges: 'Show Variables' Query" RESULT: skip

START TEST "Source Database Triggers" [CLASS: DUPX_Validation_test_db_triggers]
LOG-TIME[DELTA:   0.00000]  MESSAGE:TEST "Source Database Triggers" RESULT: skip

START TEST "Character Set and Collation Support" [CLASS: DUPX_Validation_test_db_supported_default_charset]
LOG-TIME[DELTA:   0.00000]  MESSAGE:TEST "Character Set and Collation Support" RESULT: skip

START TEST "Character Set and  Collation Capability" [CLASS: DUPX_Validation_test_db_supported_charset]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Character Set and  Collation Capability" RESULT: skip

START TEST "Tables Case Sensitivity" [CLASS: DUPX_Validation_test_db_case_sensitive_tables]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Tables Case Sensitivity" RESULT: skip

START TEST "Tables Flagged for Removal or Backup" [CLASS: DUPX_Validation_test_db_affected_tables]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Tables Flagged for Removal or Backup" RESULT: skip

START TEST "Prefix too long" [CLASS: DUPX_Validation_test_db_prefix_too_long]
LOG-TIME[DELTA:   0.00000]  MESSAGE:TEST "Prefix too long" RESULT: skip

START TEST "Database cleanup" [CLASS: DUPX_Validation_test_db_cleanup]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Database cleanup" RESULT: skip

START TEST "User created cleanup" [CLASS: DUPX_Validation_test_db_user_cleanup]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "User created cleanup" RESULT: skip

AJAX ACTION [validate] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [sparam_s1] START
AJAX ACTION [sparam_s1] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME
STEP ACTION: "on-validate"
LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/ctrls/ctrl.base.php:191][DELTA:   0.02018]  MESSAGE:END RENDER PAGE
LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [validate] START
START TEST "Archive Check" [CLASS: DUPX_Validation_test_archive_check]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Archive Check" RESULT: passed

START TEST "Duplicator importer version" [CLASS: DUPX_Validation_test_importer_version]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Duplicator importer version" RESULT: skip

START TEST "Overwrite Install" [CLASS: DUPX_Validation_test_owrinstall]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Overwrite Install" RESULT: good

START TEST "Disaster Recovery" [CLASS: DUPX_Validation_test_recovery]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Disaster Recovery" RESULT: skip

START TEST "Partial Backup Check" [CLASS: DUPX_Validation_test_importable]
LOG-TIME[DELTA:   0.00016]  MESSAGE:TEST "Partial Backup Check" RESULT: hard warning

START TEST "REST API test" [CLASS: DUPX_Validation_test_rest_api]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "REST API test" RESULT: skip

START TEST "Manual extraction detected" [CLASS: DUPX_Validation_test_manual_extraction]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Manual extraction detected" RESULT: good

START TEST "Database Only" [CLASS: DUPX_Validation_test_dbonly_iswordpress]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Database Only" RESULT: skip

START TEST "Package Age" [CLASS: DUPX_Validation_test_package_age]
LOG-TIME[DELTA:   0.00002]  MESSAGE:TEST "Package Age" RESULT: good

START TEST "Replace PATHs in database" [CLASS: DUPX_Validation_test_replace_paths]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Replace PATHs in database" RESULT: skip

START TEST "Managed hosting supported" [CLASS: DUPX_Validation_test_managed_supported]
LOG-TIME[DELTA:   0.00004]  MESSAGE:TEST "Managed hosting supported" RESULT: skip

START TEST "Siteground" [CLASS: DUPX_Validation_test_siteground]
LOG-TIME[DELTA:   0.00016]  MESSAGE:TEST "Siteground" RESULT: skip

START TEST "Subomain multisite installation in subfolder" [CLASS: DUPX_Validation_test_multisite_subfolder]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Subomain multisite installation in subfolder" RESULT: skip

START TEST "Addon Sites" [CLASS: DUPX_Validation_test_addon_sites]
--------------------------------------
PATHS MAPPING : "/Users/<USER>/Sites/brandfast"
--------------------------------------
LOG-TIME[DELTA:   0.00848]  MESSAGE:TEST "Addon Sites" RESULT: good

START TEST "Wordfence" [CLASS: DUPX_Validation_test_wordfence]
LOG-TIME[DELTA:   0.00007]  MESSAGE:TEST "Wordfence" RESULT: good

START TEST "Table prefix of managed hosting" [CLASS: DUPX_Validation_test_managed_tprefix]
LOG-TIME[DELTA:   0.00005]  MESSAGE:TEST "Table prefix of managed hosting" RESULT: skip

START TEST "Wordpress Configuration" [CLASS: DUPX_Validation_test_wp_config]
LOG-TIME[DELTA:   0.00106]  MESSAGE:TEST "Wordpress Configuration" RESULT: passed

START TEST "PHP Version Mismatch" [CLASS: DUPX_Validation_test_php_version]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Version Mismatch" RESULT: good

START TEST "PHP Open Base" [CLASS: DUPX_Validation_test_open_basedir]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Open Base" RESULT: good

START TEST "PHP Memory Limit" [CLASS: DUPX_Validation_test_memory_limit]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Memory Limit" RESULT: good

START TEST "PHP Extensions" [CLASS: DUPX_Validation_test_extensions]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Extensions" RESULT: good

START TEST "PHP Mysqli" [CLASS: DUPX_Validation_test_mysql_connect]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Mysqli" RESULT: passed

START TEST "PHP Functions and Classes" [CLASS: DUPX_Validation_test_php_functionalities]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Functions and Classes" RESULT: passed

START TEST "PHP Timeout" [CLASS: DUPX_Validation_test_timeout]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Timeout" RESULT: good

START TEST "Disk Space" [CLASS: DUPX_Validation_test_disk_space]
LOG-TIME[DELTA:   0.00002]  MESSAGE:TEST "Disk Space" RESULT: good

START TEST "Permissions: General" [CLASS: DUPX_Validation_test_iswritable]
LOG-TIME[DELTA:   0.02329]  MESSAGE:TEST "Permissions: General" RESULT: passed

START TEST "Permissions: Configs Files " [CLASS: DUPX_Validation_test_iswritable_configs]
LOG-TIME[DELTA:   0.00006]  MESSAGE:TEST "Permissions: Configs Files " RESULT: passed

START TEST "Extract only files" [CLASS: DUPX_Validation_test_db_excluded]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Extract only files" RESULT: passed

START TEST "Cpanel connection" [CLASS: DUPX_Validation_test_cpnl_connection]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Cpanel connection" RESULT: skip

START TEST "Create Database User" [CLASS: DUPX_Validation_test_cpnl_new_user]
LOG-TIME[DELTA:   0.00000]  MESSAGE:TEST "Create Database User" RESULT: skip

START TEST "Host Name" [CLASS: DUPX_Validation_test_db_host_name]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Host Name" RESULT: passed

START TEST "Host Connection" [CLASS: DUPX_Validation_test_db_connection]
LOG-TIME[DELTA:   0.00094]  MESSAGE:TEST "Host Connection" RESULT: passed

START TEST "Database Version" [CLASS: DUPX_Validation_test_db_version]
LOG-TIME[DELTA:   0.00566]  MESSAGE:TEST "Database Version" RESULT: passed

START TEST "Create New Database" [CLASS: DUPX_Validation_test_db_create]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Create New Database" RESULT: skip

START TEST "Database Engine Support" [CLASS: DUPX_Validation_test_db_supported_engine]
LOG-TIME[DELTA:   0.00108]  MESSAGE:TEST "Database Engine Support" RESULT: passed

START TEST "Database GTID Mode" [CLASS: DUPX_Validation_test_db_gtid_mode]
LOG-TIME[DELTA:   0.01589]  MESSAGE:TEST "Database GTID Mode" RESULT: passed

START TEST "Privileges: User Visibility" [CLASS: DUPX_Validation_test_db_visibility]
LOG-TIME[DELTA:   0.00020]  MESSAGE:TEST "Privileges: User Visibility" RESULT: passed

START TEST "Manual Table Check" [CLASS: DUPX_Validation_test_db_manual_tables_count]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Manual Table Check" RESULT: skip

START TEST "Multiple WP Installs" [CLASS: DUPX_Validation_test_db_multiple_wp_installs]
LOG-TIME[DELTA:   0.00117]  MESSAGE:TEST "Multiple WP Installs" RESULT: passed

START TEST "Privileges: User Resources" [CLASS: DUPX_Validation_test_db_user_resources]
LOG-TIME[DELTA:   0.00165]  MESSAGE:TEST "Privileges: User Resources" RESULT: passed

START TEST "Privileges: User Table Access" [CLASS: DUPX_Validation_test_db_user_perms]
LOG-TIME[DELTA:   0.02055]  MESSAGE:TEST "Privileges: User Table Access" RESULT: passed

START TEST "Privileges: 'Show Variables' Query" [CLASS: DUPX_Validation_test_db_custom_queries]
LOG-TIME[DELTA:   0.00097]  MESSAGE:TEST "Privileges: 'Show Variables' Query" RESULT: passed

START TEST "Source Database Triggers" [CLASS: DUPX_Validation_test_db_triggers]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Source Database Triggers" RESULT: passed

START TEST "Character Set and Collation Support" [CLASS: DUPX_Validation_test_db_supported_default_charset]
LOG-TIME[DELTA:   0.00106]  MESSAGE:TEST "Character Set and Collation Support" RESULT: passed

START TEST "Character Set and  Collation Capability" [CLASS: DUPX_Validation_test_db_supported_charset]
LOG-TIME[DELTA:   0.00003]  MESSAGE:TEST "Character Set and  Collation Capability" RESULT: passed

START TEST "Tables Case Sensitivity" [CLASS: DUPX_Validation_test_db_case_sensitive_tables]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Tables Case Sensitivity" RESULT: skip

START TEST "Tables Flagged for Removal or Backup" [CLASS: DUPX_Validation_test_db_affected_tables]
LOG-TIME[DELTA:   0.00033]  MESSAGE:TEST "Tables Flagged for Removal or Backup" RESULT: passed

START TEST "Prefix too long" [CLASS: DUPX_Validation_test_db_prefix_too_long]
LOG-TIME[DELTA:   0.00024]  MESSAGE:TEST "Prefix too long" RESULT: passed

START TEST "Database cleanup" [CLASS: DUPX_Validation_test_db_cleanup]
LOG-TIME[DELTA:   0.00000]  MESSAGE:TEST "Database cleanup" RESULT: skip

START TEST "User created cleanup" [CLASS: DUPX_Validation_test_db_user_cleanup]
LOG-TIME[DELTA:   0.00000]  MESSAGE:TEST "User created cleanup" RESULT: skip


CTRL PARAMS AFTER VALIDATION
AJAX ACTION [validate] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [proceed_confirm_dialog] START
AJAX ACTION [proceed_confirm_dialog] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [sparam_s1] START
AJAX ACTION [sparam_s1] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [extract] START
findDupInstallerFolder error; set no subfolder
INITIALIZE FILTERS
--------------------------------------
PATHS MAPPING : "/Users/<USER>/Sites/brandfast"
--------------------------------------
********************************************************************************
* DUPLICATOR-PRO: Install-Log
* STEP-1 START @ 01:22:38
* NOTICE: Do NOT post to public sites or forums!!
********************************************************************************
USER INPUTS
INSTALL TYPE________: single site
BLOG NAME___________: "Brandfast"
HOME URL NEW________: "https://brandfast.test"
SITE URL NEW________: "https://brandfast.test/wordpress"
CONTENT URL NEW_____: "https://brandfast.test"
UPLOAD URL NEW______: "https://brandfast.test/uploads"
PLUGINS URL NEW_____: "https://brandfast.test/plugins"
MUPLUGINS URL NEW___: "https://brandfast.test/mu-plugins"
HOME PATH NEW_______: "/Users/<USER>/Sites/brandfast"
SITE PATH NEW_______: "/Users/<USER>/Sites/brandfast/wordpress"
CONTENT PATH NEW____: "/Users/<USER>/Sites/brandfast"
UPLOAD PATH NEW_____: "/Users/<USER>/Sites/brandfast/uploads"
PLUGINS PATH NEW____: "/Users/<USER>/Sites/brandfast/plugins"
MUPLUGINS PATH NEW__: "/Users/<USER>/Sites/brandfast/mu-plugins"
ARCHIVE ACTION______: "donothing"
SKIP WP FILES_______: "none"
ARCHIVE ENGINE______: "ziparchivechunking"
SET DIR PERMS_______: true
DIR PERMS VALUE_____: 0755
SET FILE PERMS______: true
FILE PERMS VALUE____: 0644
SAFE MODE___________: 0
LOGGING_____________: 1
ZIP THROTTLING______: false
WP CONFIG___________: "modify"
HTACCESS CONFIG_____: "new"
OTHER CONFIG________: "new"
FILE TIME___________: "current"
REMOVE RENDUNDANT___: false
********************************************************************************

REMOVE FILTERS
	DIR : "/Users/<USER>/Sites/brandfast/dup-installer"
	FILE: "/Users/<USER>/Sites/brandfast/20250918_brandfast_[HASH]_20250918131607_installer-backup.php"
	FILE: "/Users/<USER>/Sites/brandfast/20250918_brandfast_[HASH]_20250918131607_archive.zip"
	FILE: "/Users/<USER>/Sites/brandfast/installer.php"
	FILE: "/Users/<USER>/Sites/brandfast/dup-installer-bootlog__c411e19-18131607.txt"
EXTRACTION FILTERS
	DIR : "dup-installer"
	FILE: "20250918_brandfast_[HASH]_20250918131607_installer-backup.php"
--------------------------------------


EXTRACTION: ZIP CHUNKING >>> START
SET RELATIVE ABSPATH: "wordpress"
BEFORE EXTRACION ACTIONS

*** RESET CONFIG FILES IN CURRENT HOSTING
*** RESET CONFIG FILES END
REMOVE DONOTHING FILES
LOG-TIME RESET TIME
REMOVE FILES
LOG-TIME[DELTA:   0.00021]  MESSAGE:FOLDERS REMOVED
MAINTENANCE MODE ENABLE

*** CREATE FOLDER AND PERMISSION PREPARE
FOLDER PREPARE DONE
ARCHIVE OFFSET 0
CHUNK COMPLETE - RUNTIME: 5.0081 sec. - Files processed: 17,100 of 30,326
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [extract] START
SET RELATIVE ABSPATH: "wordpress"
ARCHIVE OFFSET 17100
--------------------------------------
PATHS MAPPING : "/Users/<USER>/Sites/brandfast"
--------------------------------------
FILE EXTRACTION: done processing last file in list of 31268

EXTRACTION: ZIP CHUNKING >>> DONE

EXTRACTION COMPLETE @ 01:22:47 - RUNTIME: 11.4484 sec. - Files processed: 30,326 of 30,326
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
ADD PREFIX META MAP ID 0 wp_



********************************************************************************
* DUPLICATOR PRO INSTALL-LOG
* STEP-2 START @ 01:22:47
* NOTICE: Do NOT post to public sites or forums!!
********************************************************************************
USER INPUTS
DB ENGINE___________: "chunk"
VIEW MODE___________: "basic"
DB ACTION___________: "empty"
DB HOST_____________: "**OBSCURED**"
DB NAME_____________: "**OBSCURED**"
DB PASS_____________: "**OBSCURED**"
DB PORT_____________: "**OBSCURED**"
USER MODE___________: "overwrite"
TABLE PREFIX________: "wp_"
MYSQL MODE__________: "DEFAULT"
MYSQL MODE OPTS_____: ""
CHARSET_____________: "utf8mb4"
COLLATE_____________: "utf8mb4_unicode_ci"
CUNKING_____________: true
VIEW CREATION_______: true
STORED PROCEDURE____: true
FUNCTIONS___________: true
REMOVE DEFINER______: false
SPLIT CREATES_______: true
SQL FILES___________: 1 (97.09MB)
	1)20250918131607-dump.sql (97.09MB)
--------------------------------------
TABLES
--------------------------------------
TABLE "wp_actionscheduler_actions"______________________[ROWS:    8389] [EXTRACT|REPLACE] [INST NAME: wp_actionscheduler_actions]
TABLE "wp_actionscheduler_claims"_______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_actionscheduler_claims]
TABLE "wp_actionscheduler_groups"_______________________[ROWS:      14] [EXTRACT|REPLACE] [INST NAME: wp_actionscheduler_groups]
TABLE "wp_actionscheduler_logs"_________________________[ROWS:   25089] [EXTRACT|REPLACE] [INST NAME: wp_actionscheduler_logs]
TABLE "wp_commentmeta"__________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_commentmeta]
TABLE "wp_comments"_____________________________________[ROWS:    7229] [EXTRACT|REPLACE] [INST NAME: wp_comments]
TABLE "wp_duplicator_activity_logs"_____________________[ROWS:      18] [EXTRACT|REPLACE] [INST NAME: wp_duplicator_activity_logs]
TABLE "wp_duplicator_backups"___________________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_duplicator_backups]
TABLE "wp_duplicator_entities"__________________________[ROWS:       8] [EXTRACT|REPLACE] [INST NAME: wp_duplicator_entities]
TABLE "wp_easywpsmtp_debug_events"______________________[ROWS:       3] [EXTRACT|REPLACE] [INST NAME: wp_easywpsmtp_debug_events]
TABLE "wp_easywpsmtp_tasks_meta"________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_easywpsmtp_tasks_meta]
TABLE "wp_gf_addon_feed"________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_gf_addon_feed]
TABLE "wp_gf_draft_submissions"_________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_gf_draft_submissions]
TABLE "wp_gf_entry"_____________________________________[ROWS:    1375] [EXTRACT|REPLACE] [INST NAME: wp_gf_entry]
TABLE "wp_gf_entry_meta"________________________________[ROWS:   12461] [EXTRACT|REPLACE] [INST NAME: wp_gf_entry_meta]
TABLE "wp_gf_entry_notes"_______________________________[ROWS:    2334] [EXTRACT|REPLACE] [INST NAME: wp_gf_entry_notes]
TABLE "wp_gf_form"______________________________________[ROWS:      11] [EXTRACT|REPLACE] [INST NAME: wp_gf_form]
TABLE "wp_gf_form_meta"_________________________________[ROWS:      11] [EXTRACT|REPLACE] [INST NAME: wp_gf_form_meta]
TABLE "wp_gf_form_revisions"____________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_gf_form_revisions]
TABLE "wp_gf_form_view"_________________________________[ROWS:    4459] [EXTRACT|REPLACE] [INST NAME: wp_gf_form_view]
TABLE "wp_gf_rest_api_keys"_____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_gf_rest_api_keys]
TABLE "wp_links"________________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_links]
TABLE "wp_mailchimp_carts"______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_mailchimp_carts]
TABLE "wp_mailchimp_jobs"_______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_mailchimp_jobs]
TABLE "wp_mclean_refs"__________________________________[ROWS:    8026] [EXTRACT|REPLACE] [INST NAME: wp_mclean_refs]
TABLE "wp_mclean_scan"__________________________________[ROWS:    3501] [EXTRACT|REPLACE] [INST NAME: wp_mclean_scan]
TABLE "wp_options"______________________________________[ROWS:     962] [EXTRACT|REPLACE] [INST NAME: wp_options]
TABLE "wp_pmxi_files"___________________________________[ROWS:       6] [EXTRACT|REPLACE] [INST NAME: wp_pmxi_files]
TABLE "wp_pmxi_hash"____________________________________[ROWS:    1047] [EXTRACT|REPLACE] [INST NAME: wp_pmxi_hash]
TABLE "wp_pmxi_history"_________________________________[ROWS:       6] [EXTRACT|REPLACE] [INST NAME: wp_pmxi_history]
TABLE "wp_pmxi_images"__________________________________[ROWS:    1853] [EXTRACT|REPLACE] [INST NAME: wp_pmxi_images]
TABLE "wp_pmxi_imports"_________________________________[ROWS:       6] [EXTRACT|REPLACE] [INST NAME: wp_pmxi_imports]
TABLE "wp_pmxi_posts"___________________________________[ROWS:    1467] [EXTRACT|REPLACE] [INST NAME: wp_pmxi_posts]
TABLE "wp_pmxi_templates"_______________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_pmxi_templates]
TABLE "wp_postmeta"_____________________________________[ROWS:  218290] [EXTRACT|REPLACE] [INST NAME: wp_postmeta]
TABLE "wp_posts"________________________________________[ROWS:   11464] [EXTRACT|REPLACE] [INST NAME: wp_posts]
TABLE "wp_rank_math_404_logs"___________________________[ROWS:      76] [EXTRACT|REPLACE] [INST NAME: wp_rank_math_404_logs]
TABLE "wp_rank_math_analytics_ga"_______________________[ROWS:   10237] [EXTRACT|REPLACE] [INST NAME: wp_rank_math_analytics_ga]
TABLE "wp_rank_math_analytics_gsc"______________________[ROWS:  297327] [EXTRACT|REPLACE] [INST NAME: wp_rank_math_analytics_gsc]
TABLE "wp_rank_math_analytics_inspections"______________[ROWS:     881] [EXTRACT|REPLACE] [INST NAME: wp_rank_math_analytics_inspections]
TABLE "wp_rank_math_analytics_keyword_manager"__________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_rank_math_analytics_keyword_manager]
TABLE "wp_rank_math_analytics_objects"__________________[ROWS:     895] [EXTRACT|REPLACE] [INST NAME: wp_rank_math_analytics_objects]
TABLE "wp_rank_math_internal_links"_____________________[ROWS:    3161] [EXTRACT|REPLACE] [INST NAME: wp_rank_math_internal_links]
TABLE "wp_rank_math_internal_meta"______________________[ROWS:    4474] [EXTRACT|REPLACE] [INST NAME: wp_rank_math_internal_meta]
TABLE "wp_rank_math_redirections"_______________________[ROWS:       8] [EXTRACT|REPLACE] [INST NAME: wp_rank_math_redirections]
TABLE "wp_rank_math_redirections_cache"_________________[ROWS:       8] [EXTRACT|REPLACE] [INST NAME: wp_rank_math_redirections_cache]
TABLE "wp_redirection_404"______________________________[ROWS:    1649] [EXTRACT|REPLACE] [INST NAME: wp_redirection_404]
TABLE "wp_redirection_groups"___________________________[ROWS:       4] [EXTRACT|REPLACE] [INST NAME: wp_redirection_groups]
TABLE "wp_redirection_items"____________________________[ROWS:    3095] [EXTRACT|REPLACE] [INST NAME: wp_redirection_items]
TABLE "wp_redirection_logs"_____________________________[ROWS:    1341] [EXTRACT|REPLACE] [INST NAME: wp_redirection_logs]
TABLE "wp_sbi_feed_caches"______________________________[ROWS:      10] [EXTRACT|REPLACE] [INST NAME: wp_sbi_feed_caches]
TABLE "wp_sbi_feeds"____________________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_sbi_feeds]
TABLE "wp_sbi_instagram_feed_locator"___________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_sbi_instagram_feed_locator]
TABLE "wp_sbi_instagram_feeds_posts"____________________[ROWS:     203] [EXTRACT|REPLACE] [INST NAME: wp_sbi_instagram_feeds_posts]
TABLE "wp_sbi_instagram_posts"__________________________[ROWS:     203] [EXTRACT|REPLACE] [INST NAME: wp_sbi_instagram_posts]
TABLE "wp_sbi_sources"__________________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_sbi_sources]
TABLE "wp_sby_feed_caches"______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_sby_feed_caches]
TABLE "wp_sby_feed_locator"_____________________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_sby_feed_locator]
TABLE "wp_sby_feeds"____________________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_sby_feeds]
TABLE "wp_smush_dir_images"_____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_smush_dir_images]
TABLE "wp_term_relationships"___________________________[ROWS:   12279] [EXTRACT|REPLACE] [INST NAME: wp_term_relationships]
TABLE "wp_term_taxonomy"________________________________[ROWS:     350] [EXTRACT|REPLACE] [INST NAME: wp_term_taxonomy]
TABLE "wp_termmeta"_____________________________________[ROWS:    4149] [EXTRACT|REPLACE] [INST NAME: wp_termmeta]
TABLE "wp_terms"________________________________________[ROWS:     350] [EXTRACT|REPLACE] [INST NAME: wp_terms]
TABLE "wp_usermeta"_____________________________________[ROWS:    1903] [EXTRACT|REPLACE] [INST NAME: wp_usermeta]
TABLE "wp_users"________________________________________[ROWS:      29] [EXTRACT|REPLACE] [INST NAME: wp_users]
TABLE "wp_wc_admin_note_actions"________________________[ROWS:     201] [EXTRACT|REPLACE] [INST NAME: wp_wc_admin_note_actions]
TABLE "wp_wc_admin_notes"_______________________________[ROWS:     165] [EXTRACT|REPLACE] [INST NAME: wp_wc_admin_notes]
TABLE "wp_wc_category_lookup"___________________________[ROWS:     330] [EXTRACT|REPLACE] [INST NAME: wp_wc_category_lookup]
TABLE "wp_wc_customer_lookup"___________________________[ROWS:    1803] [EXTRACT|REPLACE] [INST NAME: wp_wc_customer_lookup]
TABLE "wp_wc_download_log"______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_download_log]
TABLE "wp_wc_order_addresses"___________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_order_addresses]
TABLE "wp_wc_order_bundle_lookup"_______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_order_bundle_lookup]
TABLE "wp_wc_order_coupon_lookup"_______________________[ROWS:      38] [EXTRACT|REPLACE] [INST NAME: wp_wc_order_coupon_lookup]
TABLE "wp_wc_order_operational_data"____________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_order_operational_data]
TABLE "wp_wc_order_product_lookup"______________________[ROWS:    2785] [EXTRACT|REPLACE] [INST NAME: wp_wc_order_product_lookup]
TABLE "wp_wc_order_stats"_______________________________[ROWS:    2015] [EXTRACT|REPLACE] [INST NAME: wp_wc_order_stats]
TABLE "wp_wc_order_tax_lookup"__________________________[ROWS:    2033] [EXTRACT|REPLACE] [INST NAME: wp_wc_order_tax_lookup]
TABLE "wp_wc_orders"____________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_orders]
TABLE "wp_wc_orders_meta"_______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_orders_meta]
TABLE "wp_wc_product_attributes_lookup"_________________[ROWS:    2210] [EXTRACT|REPLACE] [INST NAME: wp_wc_product_attributes_lookup]
TABLE "wp_wc_product_download_directories"______________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_wc_product_download_directories]
TABLE "wp_wc_product_meta_lookup"_______________________[ROWS:    4231] [EXTRACT|REPLACE] [INST NAME: wp_wc_product_meta_lookup]
TABLE "wp_wc_rate_limits"_______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_rate_limits]
TABLE "wp_wc_reserved_stock"____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_reserved_stock]
TABLE "wp_wc_tax_rate_classes"__________________________[ROWS:       8] [EXTRACT|REPLACE] [INST NAME: wp_wc_tax_rate_classes]
TABLE "wp_wc_webhooks"__________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_webhooks]
TABLE "wp_woocommerce_api_keys"_________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_api_keys]
TABLE "wp_woocommerce_attribute_taxonomies"_____________[ROWS:       9] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_attribute_taxonomies]
TABLE "wp_woocommerce_bundled_itemmeta"_________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_bundled_itemmeta]
TABLE "wp_woocommerce_bundled_items"____________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_bundled_items]
TABLE "wp_woocommerce_downloadable_product_permissions"_[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_downloadable_product_permissions]
TABLE "wp_woocommerce_log"______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_log]
TABLE "wp_woocommerce_order_itemmeta"___________________[ROWS:   50414] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_order_itemmeta]
TABLE "wp_woocommerce_order_items"______________________[ROWS:    6847] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_order_items]
TABLE "wp_woocommerce_payment_tokenmeta"________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_payment_tokenmeta]
TABLE "wp_woocommerce_payment_tokens"___________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_payment_tokens]
TABLE "wp_woocommerce_sessions"_________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_sessions]
TABLE "wp_woocommerce_shipping_table_rates"_____________[ROWS:       3] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_shipping_table_rates]
TABLE "wp_woocommerce_shipping_zone_locations"__________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_shipping_zone_locations]
TABLE "wp_woocommerce_shipping_zone_methods"____________[ROWS:       3] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_shipping_zone_methods]
TABLE "wp_woocommerce_shipping_zones"___________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_shipping_zones]
TABLE "wp_woocommerce_tax_rate_locations"_______________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_tax_rate_locations]
TABLE "wp_woocommerce_tax_rates"________________________[ROWS:       3] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_tax_rates]
TABLE "wp_wpr_rocket_cache"_____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wpr_rocket_cache]
TABLE "wp_wpr_rucss_used_css"___________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wpr_rucss_used_css]
********************************************************************************

--------------------------------------
DATABASE-ENVIRONMENT
--------------------------------------
MYSQL VERSION:	This Server: 8.0.33 -- Build Server: 8.4.6
TIMEOUT:	5000
MAXPACK:	67108864
SQLMODE-GLOBAL:	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
SQLMODE-SESSION:NO_AUTO_VALUE_ON_ZERO
DROP ALL TABLES
--------------------------------------
DATABASE RESULTS
--------------------------------------
QUERY FIXES
GLOBAL RULES ADDED: PROC AND VIEWS

QUERY FIXES GLOBAL RULES
	SEARCH  => /^(\s*(?:\/\*!\d+\s)?\s*(?:CREATE.+)?DEFINER\s*=)([^\*\s]+)(.*)$/m
	REPLACE => $1`root`@`127.0.0.1`$3

	SEARCH  => /^(\s*CREATE.+(?:PROCEDURE|FUNCTION)[\s\S]*)(BEGIN)([\s\S]*)$/
	REPLACE => $1SQL SECURITY INVOKER
$2$3

--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
PROCESSING SQL FILE 20250918131607-dump.sql (1 of 1)
OFFSET 0 OF 101806360
Auto Commit set to false successfully
NO TABLE TO SKIP
DATABASE CHUNK: CREATION TABLE MARKER FOUND
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
PROCESSING SQL FILE 20250918131607-dump.sql (1 of 1)
OFFSET 37157363 OF 101806360
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
PROCESSING SQL FILE 20250918131607-dump.sql (1 of 1)
OFFSET 80116749 OF 101806360
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
ALL SQL FILES PROCESSED
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
ERRORS FOUND:	0
DROPPED TABLES:	0
RENAMED TABLES:	0
QUERIES RAN:	933

TABLES ROWS IN DATABASE AFTER EXTRACTION

TABLE "wp_actionscheduler_actions"______________________[ROWS:  8389]
TABLE "wp_actionscheduler_claims"_______________________[ROWS:     0]
TABLE "wp_actionscheduler_groups"_______________________[ROWS:    14]
TABLE "wp_actionscheduler_logs"_________________________[ROWS: 25089]
TABLE "wp_commentmeta"__________________________________[ROWS:     0]
TABLE "wp_comments"_____________________________________[ROWS:  7229]
TABLE "wp_duplicator_activity_logs"_____________________[ROWS:    18]
TABLE "wp_duplicator_backups"___________________________[ROWS:     2]
TABLE "wp_duplicator_entities"__________________________[ROWS:     8]
TABLE "wp_easywpsmtp_debug_events"______________________[ROWS:     3]
TABLE "wp_easywpsmtp_tasks_meta"________________________[ROWS:     1]
TABLE "wp_gf_addon_feed"________________________________[ROWS:     0]
TABLE "wp_gf_draft_submissions"_________________________[ROWS:     0]
TABLE "wp_gf_entry"_____________________________________[ROWS:  1375]
TABLE "wp_gf_entry_meta"________________________________[ROWS: 12461]
TABLE "wp_gf_entry_notes"_______________________________[ROWS:  2334]
TABLE "wp_gf_form"______________________________________[ROWS:    11]
TABLE "wp_gf_form_meta"_________________________________[ROWS:    11]
TABLE "wp_gf_form_revisions"____________________________[ROWS:     1]
TABLE "wp_gf_form_view"_________________________________[ROWS:  4459]
TABLE "wp_gf_rest_api_keys"_____________________________[ROWS:     0]
TABLE "wp_links"________________________________________[ROWS:     0]
TABLE "wp_mailchimp_carts"______________________________[ROWS:     0]
TABLE "wp_mailchimp_jobs"_______________________________[ROWS:     0]
TABLE "wp_mclean_refs"__________________________________[ROWS:  8026]
TABLE "wp_mclean_scan"__________________________________[ROWS:  3501]
TABLE "wp_options"______________________________________[ROWS:   962]
TABLE "wp_pmxi_files"___________________________________[ROWS:     6]
TABLE "wp_pmxi_hash"____________________________________[ROWS:  1047]
TABLE "wp_pmxi_history"_________________________________[ROWS:     6]
TABLE "wp_pmxi_images"__________________________________[ROWS:  1853]
TABLE "wp_pmxi_imports"_________________________________[ROWS:     6]
TABLE "wp_pmxi_posts"___________________________________[ROWS:  1467]
TABLE "wp_pmxi_templates"_______________________________[ROWS:     1]
TABLE "wp_postmeta"_____________________________________[ROWS:218290]
TABLE "wp_posts"________________________________________[ROWS: 11464]
TABLE "wp_rank_math_404_logs"___________________________[ROWS:    76]
TABLE "wp_rank_math_analytics_ga"_______________________[ROWS: 10237]
TABLE "wp_rank_math_analytics_gsc"______________________[ROWS:297327]
TABLE "wp_rank_math_analytics_inspections"______________[ROWS:   881]
TABLE "wp_rank_math_analytics_keyword_manager"__________[ROWS:     0]
TABLE "wp_rank_math_analytics_objects"__________________[ROWS:   895]
TABLE "wp_rank_math_internal_links"_____________________[ROWS:  3161]
TABLE "wp_rank_math_internal_meta"______________________[ROWS:  4474]
TABLE "wp_rank_math_redirections"_______________________[ROWS:     8]
TABLE "wp_rank_math_redirections_cache"_________________[ROWS:     8]
TABLE "wp_redirection_404"______________________________[ROWS:  1649]
TABLE "wp_redirection_groups"___________________________[ROWS:     4]
TABLE "wp_redirection_items"____________________________[ROWS:  3095]
TABLE "wp_redirection_logs"_____________________________[ROWS:  1341]
TABLE "wp_sbi_feed_caches"______________________________[ROWS:    10]
TABLE "wp_sbi_feeds"____________________________________[ROWS:     1]
TABLE "wp_sbi_instagram_feed_locator"___________________[ROWS:     0]
TABLE "wp_sbi_instagram_feeds_posts"____________________[ROWS:   203]
TABLE "wp_sbi_instagram_posts"__________________________[ROWS:   203]
TABLE "wp_sbi_sources"__________________________________[ROWS:     1]
TABLE "wp_sby_feed_caches"______________________________[ROWS:     0]
TABLE "wp_sby_feed_locator"_____________________________[ROWS:     2]
TABLE "wp_sby_feeds"____________________________________[ROWS:     1]
TABLE "wp_smush_dir_images"_____________________________[ROWS:     0]
TABLE "wp_term_relationships"___________________________[ROWS: 12279]
TABLE "wp_term_taxonomy"________________________________[ROWS:   350]
TABLE "wp_termmeta"_____________________________________[ROWS:  4149]
TABLE "wp_terms"________________________________________[ROWS:   350]
TABLE "wp_usermeta"_____________________________________[ROWS:  1903]
TABLE "wp_users"________________________________________[ROWS:    29]
TABLE "wp_wc_admin_note_actions"________________________[ROWS:   201]
TABLE "wp_wc_admin_notes"_______________________________[ROWS:   165]
TABLE "wp_wc_category_lookup"___________________________[ROWS:   330]
TABLE "wp_wc_customer_lookup"___________________________[ROWS:  1803]
TABLE "wp_wc_download_log"______________________________[ROWS:     0]
TABLE "wp_wc_order_addresses"___________________________[ROWS:     0]
TABLE "wp_wc_order_bundle_lookup"_______________________[ROWS:     0]
TABLE "wp_wc_order_coupon_lookup"_______________________[ROWS:    38]
TABLE "wp_wc_order_operational_data"____________________[ROWS:     0]
TABLE "wp_wc_order_product_lookup"______________________[ROWS:  2785]
TABLE "wp_wc_order_stats"_______________________________[ROWS:  2015]
TABLE "wp_wc_order_tax_lookup"__________________________[ROWS:  2033]
TABLE "wp_wc_orders"____________________________________[ROWS:     0]
TABLE "wp_wc_orders_meta"_______________________________[ROWS:     0]
TABLE "wp_wc_product_attributes_lookup"_________________[ROWS:  2210]
TABLE "wp_wc_product_download_directories"______________[ROWS:     2]
TABLE "wp_wc_product_meta_lookup"_______________________[ROWS:  4231]
TABLE "wp_wc_rate_limits"_______________________________[ROWS:     0]
TABLE "wp_wc_reserved_stock"____________________________[ROWS:     0]
TABLE "wp_wc_tax_rate_classes"__________________________[ROWS:     8]
TABLE "wp_wc_webhooks"__________________________________[ROWS:     0]
TABLE "wp_woocommerce_api_keys"_________________________[ROWS:     0]
TABLE "wp_woocommerce_attribute_taxonomies"_____________[ROWS:     9]
TABLE "wp_woocommerce_bundled_itemmeta"_________________[ROWS:     0]
TABLE "wp_woocommerce_bundled_items"____________________[ROWS:     0]
TABLE "wp_woocommerce_downloadable_product_permissions"_[ROWS:     0]
TABLE "wp_woocommerce_log"______________________________[ROWS:     0]
TABLE "wp_woocommerce_order_itemmeta"___________________[ROWS: 50414]
TABLE "wp_woocommerce_order_items"______________________[ROWS:  6847]
TABLE "wp_woocommerce_payment_tokenmeta"________________[ROWS:     0]
TABLE "wp_woocommerce_payment_tokens"___________________[ROWS:     0]
TABLE "wp_woocommerce_sessions"_________________________[ROWS:     1]
TABLE "wp_woocommerce_shipping_table_rates"_____________[ROWS:     3]
TABLE "wp_woocommerce_shipping_zone_locations"__________[ROWS:     1]
TABLE "wp_woocommerce_shipping_zone_methods"____________[ROWS:     3]
TABLE "wp_woocommerce_shipping_zones"___________________[ROWS:     1]
TABLE "wp_woocommerce_tax_rate_locations"_______________[ROWS:     0]
TABLE "wp_woocommerce_tax_rates"________________________[ROWS:     3]
TABLE "wp_wpr_rocket_cache"_____________________________[ROWS:     0]
TABLE "wp_wpr_rucss_used_css"___________________________[ROWS:     0]

INSERT DATA RUNTIME: 12.8595 sec.
STEP-2 COMPLETE @ 01:23:00 - RUNTIME: 12.8595 sec.
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [webupdate] START

====================================
SET SEARCH AND REPLACE LIST INSTALL TYPE single site
====================================
SEARCH ITEM[T:path |P:12] SEARCH: /sites/unlocked.brandfast.nu/files/public REPLACE: /Users/<USER>/Sites/brandfast [SCOPE: ALL]
SEARCH ITEM[T:urlnd|P:12] SEARCH: https://unlocked.brandfast.nu REPLACE: https://brandfast.test [SCOPE: ALL]
CHUNK LOAD DATA: IS NULL 
CHUNK ACTION: CURRENT [start][][]


********************************************************************************
DUPLICATOR PRO INSTALL-LOG
STEP-3 START @ 01:23:00
NOTICE: Do NOT post to public sites or forums
********************************************************************************
CHARSET SERVER:	"utf8mb4"
CHARSET CLIENT:	"utf8mb4"
********************************************************************************
OPTIONS:
SKIP PATH REPLACE_____: false
DISALLOW_FILE_EDIT____: [value = ], [inWpConfig = 1]
DISALLOW_FILE_MODS____: [value = ], [inWpConfig = 1]
AUTOSAVE_INTERVAL_____: [value = 60], [inWpConfig = ]
WP_POST_REVISIONS_____: [value = 2], [inWpConfig = 1]
FORCE_SSL_ADMIN_______: [value = 1], [inWpConfig = ]
WP_AUTO_UPDATE_CORE___: [value = false], [inWpConfig = ]
WP_CACHE______________: [value = 1], [inWpConfig = 1]
WPCACHEHOME___________: [value = /], [inWpConfig = ]
WP_DEBUG______________: [value = 1], [inWpConfig = 1]
WP_DEBUG_LOG__________: [value = 1], [inWpConfig = 1]
WP_DEBUG_DISPLAY______: [value = ], [inWpConfig = 1]
WP_DISABLE_FATAL_ERROR_HANDLER: [value = ], [inWpConfig = ]
SCRIPT_DEBUG__________: [value = 1], [inWpConfig = 1]
CONCATENATE_SCRIPTS___: [value = ], [inWpConfig = ]
SAVEQUERIES___________: [value = ], [inWpConfig = ]
ALTERNATE_WP_CRON_____: [value = ], [inWpConfig = ]
DISABLE_WP_CRON_______: [value = 1], [inWpConfig = 1]
WP_CRON_LOCK_TIMEOUT__: [value = 60], [inWpConfig = ]
COOKIE_DOMAIN_________: [value = ], [inWpConfig = ]
WP_MEMORY_LIMIT_______: [value = 256M], [inWpConfig = 1]
WP_MAX_MEMORY_LIMIT___: [value = 512M], [inWpConfig = 1]
WP_TEMP_DIR___________: [value = ], [inWpConfig = ]
********************************************************************************

********************************************************************************
CHUNK PARAMS:
maxIteration__________: 0
timeOut_______________: 5000000
throttling____________: 2000
rowsPerPage___________: 1000
********************************************************************************

CHUNK ACTION: CURRENT [cleanup_trans][][]
CLEAN OPTIONS [wp_options]
	`option_name` = "duplicator_pro_plugin_data_stats"
	`option_name` LIKE "\_transient%"
	`option_name` LIKE "\_site\_transient%"
	`option_name` IN ("duplicator_pro_ui_view_state","duplicator_pro_package_active","duplicator_pro_settings")
DATABASE OPTIONS DELETED [ROWS:    24]
DATABASE ACTIVITY LOGS DELETED [ROWS:    18]
CHUNK ACTION: CURRENT [cleanup_extra][][]
CLEANUP EXTRA
	- SKIP DROP VIEWS
	- SKIP DROP PROCS
	- SKIP DROP FUNCS
CHUNK ACTION: CURRENT [cleanup_packages][][]
EMPTY PACKAGES TABLE
CLEAN PACKAGES
DATABASE PACKAGE DELETED [ROWS:     2]
CHUNK ACTION: CURRENT [init][][]

EVALUATE TABLE: "wp_actionscheduler_actions"______________________[ROWS:  8389][PG:   9][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][0]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][1]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][2]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][3]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][4]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][5]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][6]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][7]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][8]

EVALUATE TABLE: "wp_actionscheduler_claims"_______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_actionscheduler_groups"_______________________[ROWS:    14][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_groups][0]

EVALUATE TABLE: "wp_actionscheduler_logs"_________________________[ROWS: 25089][PG:  26][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][0]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][1]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][2]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][3]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][4]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][5]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][6]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][7]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][8]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][9]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][10]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][11]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][12]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][13]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][14]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][15]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][16]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][17]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][18]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][19]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][20]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][21]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][22]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][23]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][24]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][25]

EVALUATE TABLE: "wp_commentmeta"__________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_comments"_____________________________________[ROWS:  7229][PG:   8][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_comments][0]
	CHUNK ACTION: CURRENT [search_replace][wp_comments][1]
	CHUNK ACTION: CURRENT [search_replace][wp_comments][2]
	CHUNK ACTION: CURRENT [search_replace][wp_comments][3]
	CHUNK ACTION: CURRENT [search_replace][wp_comments][4]
	CHUNK ACTION: CURRENT [search_replace][wp_comments][5]
	CHUNK ACTION: CURRENT [search_replace][wp_comments][6]
	CHUNK ACTION: CURRENT [search_replace][wp_comments][7]

EVALUATE TABLE: "wp_duplicator_activity_logs"_____________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_duplicator_backups"___________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_duplicator_entities"__________________________[ROWS:     8][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_duplicator_entities][0]

EVALUATE TABLE: "wp_easywpsmtp_debug_events"______________________[ROWS:     3][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_easywpsmtp_debug_events][0]

EVALUATE TABLE: "wp_easywpsmtp_tasks_meta"________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_easywpsmtp_tasks_meta][0]

EVALUATE TABLE: "wp_gf_addon_feed"________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_gf_draft_submissions"_________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_gf_entry"_____________________________________[ROWS:  1375][PG:   2][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry][0]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry][1]

EVALUATE TABLE: "wp_gf_entry_meta"________________________________[ROWS: 12461][PG:  13][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][0]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][1]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][2]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][3]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][4]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][5]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][6]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][7]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][8]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][9]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][10]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][11]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_meta][12]

EVALUATE TABLE: "wp_gf_entry_notes"_______________________________[ROWS:  2334][PG:   3][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_notes][0]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_notes][1]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_entry_notes][2]

EVALUATE TABLE: "wp_gf_form"______________________________________[ROWS:    11][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_gf_form][0]

EVALUATE TABLE: "wp_gf_form_meta"_________________________________[ROWS:    11][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_gf_form_meta][0]

EVALUATE TABLE: "wp_gf_form_revisions"____________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_gf_form_revisions][0]

EVALUATE TABLE: "wp_gf_form_view"_________________________________[ROWS:  4459][PG:   5][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_gf_form_view][0]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_form_view][1]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_form_view][2]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_form_view][3]
	CHUNK ACTION: CURRENT [search_replace][wp_gf_form_view][4]

EVALUATE TABLE: "wp_gf_rest_api_keys"_____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_links"________________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_mailchimp_carts"______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_mailchimp_jobs"_______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_mclean_refs"__________________________________[ROWS:  8026][PG:   9][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_refs][0]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_refs][1]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_refs][2]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_refs][3]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_refs][4]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_refs][5]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_refs][6]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_refs][7]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_refs][8]

EVALUATE TABLE: "wp_mclean_scan"__________________________________[ROWS:  3501][PG:   4][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_scan][0]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_scan][1]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_scan][2]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_scan][3]

EVALUATE TABLE: "wp_options"______________________________________[ROWS:   938][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_options][0]

EVALUATE TABLE: "wp_pmxi_files"___________________________________[ROWS:     6][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_pmxi_files][0]

EVALUATE TABLE: "wp_pmxi_hash"____________________________________[ROWS:  1047][PG:   2][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_pmxi_hash][0]
	CHUNK ACTION: CURRENT [search_replace][wp_pmxi_hash][1]

EVALUATE TABLE: "wp_pmxi_history"_________________________________[ROWS:     6][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_pmxi_history][0]

EVALUATE TABLE: "wp_pmxi_images"__________________________________[ROWS:  1853][PG:   2][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_pmxi_images][0]
	CHUNK ACTION: CURRENT [search_replace][wp_pmxi_images][1]

EVALUATE TABLE: "wp_pmxi_imports"_________________________________[ROWS:     6][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_pmxi_imports][0]

EVALUATE TABLE: "wp_pmxi_posts"___________________________________[ROWS:  1467][PG:   2][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_pmxi_posts][0]
	CHUNK ACTION: CURRENT [search_replace][wp_pmxi_posts][1]

EVALUATE TABLE: "wp_pmxi_templates"_______________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_pmxi_templates][0]

EVALUATE TABLE: "wp_postmeta"_____________________________________[ROWS:218290][PG: 219][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][0]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][1]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][2]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][3]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][4]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][5]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][6]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][7]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][8]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][9]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][10]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][11]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][12]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][13]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][14]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][15]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][16]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][17]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][18]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][19]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][20]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][21]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][22]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][23]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][24]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][25]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][26]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][27]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][28]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][29]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][30]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][31]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][32]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][33]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][34]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][35]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][36]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][37]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][38]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][39]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][40]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][41]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][42]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][43]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][44]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][45]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][46]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][47]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][48]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][49]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][50]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][51]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][52]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][53]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][54]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][55]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][56]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][57]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][58]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][59]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][60]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][61]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][62]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][63]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][64]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][65]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][66]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][67]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][68]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][69]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][70]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][71]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][72]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][73]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][74]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][75]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][76]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][77]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][78]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][79]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][80]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][81]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][82]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][83]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][84]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][85]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][86]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][87]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][88]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][89]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][90]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][91]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][92]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][93]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][94]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][95]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][96]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][97]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][98]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][99]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][100]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][101]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][102]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][103]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][104]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][105]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][106]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][107]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][108]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][109]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][110]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][111]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][112]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][113]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][114]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][115]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][116]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][117]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][118]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][119]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][120]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][121]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][122]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][123]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][124]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][125]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][126]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][127]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][128]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][129]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][130]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][131]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][132]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][133]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][134]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][135]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][136]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][137]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][138]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][139]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][140]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][141]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][142]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][143]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][144]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][145]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][146]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][147]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][148]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][149]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][150]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][151]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][152]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][153]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][154]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][155]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][156]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][157]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][158]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][159]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][160]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][161]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][162]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][163]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][164]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][165]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][166]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][167]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][168]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][169]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][170]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][171]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][172]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][173]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][174]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][175]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][176]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][177]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][178]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][179]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][180]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][181]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][182]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][183]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][184]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][185]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][186]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][187]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][188]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][189]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][190]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][191]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][192]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][193]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][194]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][195]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][196]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][197]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][198]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][199]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][200]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][201]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][202]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][203]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][204]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][205]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][206]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][207]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][208]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][209]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][210]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][211]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][212]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][213]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][214]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][215]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][216]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][217]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][218]

EVALUATE TABLE: "wp_posts"________________________________________[ROWS: 11464][PG:  12][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_posts][0]
	CHUNK ACTION: CURRENT [search_replace][wp_posts][1]
	
STEP-3 CHUNK STOP @ 01:23:05 - RUNTIME: 5.1657 sec. 


	AJAX ACTION [webupdate] SUCCESS
	-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [webupdate] START
CHUNK ACTION: CURRENT [search_replace][wp_posts][2]
CHUNK ACTION: CURRENT [search_replace][wp_posts][3]
CHUNK ACTION: CURRENT [search_replace][wp_posts][4]
CHUNK ACTION: CURRENT [search_replace][wp_posts][5]
CHUNK ACTION: CURRENT [search_replace][wp_posts][6]
CHUNK ACTION: CURRENT [search_replace][wp_posts][7]
CHUNK ACTION: CURRENT [search_replace][wp_posts][8]
CHUNK ACTION: CURRENT [search_replace][wp_posts][9]
CHUNK ACTION: CURRENT [search_replace][wp_posts][10]
CHUNK ACTION: CURRENT [search_replace][wp_posts][11]

EVALUATE TABLE: "wp_rank_math_404_logs"___________________________[ROWS:    76][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_404_logs][0]

EVALUATE TABLE: "wp_rank_math_analytics_ga"_______________________[ROWS: 10237][PG:  11][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_ga][0]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_ga][1]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_ga][2]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_ga][3]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_ga][4]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_ga][5]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_ga][6]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_ga][7]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_ga][8]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_ga][9]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_ga][10]

EVALUATE TABLE: "wp_rank_math_analytics_gsc"______________________[ROWS:297327][PG: 298][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][0]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][1]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][2]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][3]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][4]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][5]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][6]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][7]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][8]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][9]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][10]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][11]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][12]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][13]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][14]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][15]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][16]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][17]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][18]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][19]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][20]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][21]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][22]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][23]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][24]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][25]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][26]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][27]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][28]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][29]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][30]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][31]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][32]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][33]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][34]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][35]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][36]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][37]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][38]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][39]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][40]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][41]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][42]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][43]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][44]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][45]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][46]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][47]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][48]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][49]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][50]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][51]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][52]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][53]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][54]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][55]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][56]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][57]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][58]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][59]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][60]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][61]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][62]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][63]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][64]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][65]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][66]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][67]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][68]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][69]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][70]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][71]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][72]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][73]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][74]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][75]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][76]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][77]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][78]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][79]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][80]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][81]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][82]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][83]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][84]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][85]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][86]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][87]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][88]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][89]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][90]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][91]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][92]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][93]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][94]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][95]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][96]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][97]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][98]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][99]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][100]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][101]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][102]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][103]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][104]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][105]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][106]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][107]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][108]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][109]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][110]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][111]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][112]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][113]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][114]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][115]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][116]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][117]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][118]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][119]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][120]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][121]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][122]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][123]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][124]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][125]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][126]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][127]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][128]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][129]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][130]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][131]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][132]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][133]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][134]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][135]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][136]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][137]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][138]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][139]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][140]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][141]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][142]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][143]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][144]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][145]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][146]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][147]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][148]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][149]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][150]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][151]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][152]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][153]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][154]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][155]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][156]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][157]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][158]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][159]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][160]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][161]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][162]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][163]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][164]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][165]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][166]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][167]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][168]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][169]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][170]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][171]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][172]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][173]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][174]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][175]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][176]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][177]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][178]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][179]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][180]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][181]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][182]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][183]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][184]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][185]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][186]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][187]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][188]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][189]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][190]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][191]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][192]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][193]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][194]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][195]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][196]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][197]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][198]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][199]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][200]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][201]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][202]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][203]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][204]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][205]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][206]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][207]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][208]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][209]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][210]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][211]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][212]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][213]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][214]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][215]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][216]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][217]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][218]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][219]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][220]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][221]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][222]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][223]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][224]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][225]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][226]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][227]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][228]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][229]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][230]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][231]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][232]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][233]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][234]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][235]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][236]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][237]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][238]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][239]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][240]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][241]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][242]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][243]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][244]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][245]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][246]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][247]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][248]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][249]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][250]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][251]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][252]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][253]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][254]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][255]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][256]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][257]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][258]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][259]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][260]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][261]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][262]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][263]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][264]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][265]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][266]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][267]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][268]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][269]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][270]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][271]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][272]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][273]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][274]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][275]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][276]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][277]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][278]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][279]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][280]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][281]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][282]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][283]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][284]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][285]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][286]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][287]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][288]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][289]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][290]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][291]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][292]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][293]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][294]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][295]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][296]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_gsc][297]

EVALUATE TABLE: "wp_rank_math_analytics_inspections"______________[ROWS:   881][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_inspections][0]
	
STEP-3 CHUNK STOP @ 01:23:10 - RUNTIME: 5.1111 sec. 


	AJAX ACTION [webupdate] SUCCESS
	-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [webupdate] START

EVALUATE TABLE: "wp_rank_math_analytics_keyword_manager"__________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_rank_math_analytics_objects"__________________[ROWS:   895][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_analytics_objects][0]

EVALUATE TABLE: "wp_rank_math_internal_links"_____________________[ROWS:  3161][PG:   4][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_internal_links][0]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_internal_links][1]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_internal_links][2]
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_internal_links][3]

EVALUATE TABLE: "wp_rank_math_internal_meta"______________________[ROWS:  4474][PG:   5][SCAN:no columns  ]

EVALUATE TABLE: "wp_rank_math_redirections"_______________________[ROWS:     8][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_redirections][0]

EVALUATE TABLE: "wp_rank_math_redirections_cache"_________________[ROWS:     8][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_rank_math_redirections_cache][0]

EVALUATE TABLE: "wp_redirection_404"______________________________[ROWS:  1649][PG:   2][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_redirection_404][0]
	CHUNK ACTION: CURRENT [search_replace][wp_redirection_404][1]

EVALUATE TABLE: "wp_redirection_groups"___________________________[ROWS:     4][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_redirection_groups][0]

EVALUATE TABLE: "wp_redirection_items"____________________________[ROWS:  3095][PG:   4][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_redirection_items][0]
	CHUNK ACTION: CURRENT [search_replace][wp_redirection_items][1]
	CHUNK ACTION: CURRENT [search_replace][wp_redirection_items][2]
	CHUNK ACTION: CURRENT [search_replace][wp_redirection_items][3]

EVALUATE TABLE: "wp_redirection_logs"_____________________________[ROWS:  1341][PG:   2][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_redirection_logs][0]
	CHUNK ACTION: CURRENT [search_replace][wp_redirection_logs][1]

EVALUATE TABLE: "wp_sbi_feed_caches"______________________________[ROWS:    10][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_sbi_feed_caches][0]

EVALUATE TABLE: "wp_sbi_feeds"____________________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_sbi_feeds][0]

EVALUATE TABLE: "wp_sbi_instagram_feed_locator"___________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_sbi_instagram_feeds_posts"____________________[ROWS:   203][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_sbi_instagram_feeds_posts][0]

EVALUATE TABLE: "wp_sbi_instagram_posts"__________________________[ROWS:   203][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_sbi_instagram_posts][0]

EVALUATE TABLE: "wp_sbi_sources"__________________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_sbi_sources][0]

EVALUATE TABLE: "wp_sby_feed_caches"______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_sby_feed_locator"_____________________________[ROWS:     2][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_sby_feed_locator][0]

EVALUATE TABLE: "wp_sby_feeds"____________________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_sby_feeds][0]

EVALUATE TABLE: "wp_smush_dir_images"_____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_term_relationships"___________________________[ROWS: 12279][PG:  13][SCAN:no columns  ]

EVALUATE TABLE: "wp_term_taxonomy"________________________________[ROWS:   350][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_term_taxonomy][0]

EVALUATE TABLE: "wp_termmeta"_____________________________________[ROWS:  4149][PG:   5][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_termmeta][0]
	CHUNK ACTION: CURRENT [search_replace][wp_termmeta][1]
	CHUNK ACTION: CURRENT [search_replace][wp_termmeta][2]
	CHUNK ACTION: CURRENT [search_replace][wp_termmeta][3]
	CHUNK ACTION: CURRENT [search_replace][wp_termmeta][4]

EVALUATE TABLE: "wp_terms"________________________________________[ROWS:   350][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_terms][0]

EVALUATE TABLE: "wp_usermeta"_____________________________________[ROWS:  1903][PG:   2][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][0]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][1]

EVALUATE TABLE: "wp_users"________________________________________[ROWS:    29][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_users][0]

EVALUATE TABLE: "wp_wc_admin_note_actions"________________________[ROWS:   201][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_admin_note_actions][0]

EVALUATE TABLE: "wp_wc_admin_notes"_______________________________[ROWS:   165][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_admin_notes][0]

EVALUATE TABLE: "wp_wc_category_lookup"___________________________[ROWS:   330][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_customer_lookup"___________________________[ROWS:  1803][PG:   2][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_customer_lookup][0]
	CHUNK ACTION: CURRENT [search_replace][wp_wc_customer_lookup][1]

EVALUATE TABLE: "wp_wc_download_log"______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_order_addresses"___________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_order_bundle_lookup"_______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_order_coupon_lookup"_______________________[ROWS:    38][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_order_operational_data"____________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_order_product_lookup"______________________[ROWS:  2785][PG:   3][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_order_stats"_______________________________[ROWS:  2015][PG:   3][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_order_stats][0]
	CHUNK ACTION: CURRENT [search_replace][wp_wc_order_stats][1]
	CHUNK ACTION: CURRENT [search_replace][wp_wc_order_stats][2]

EVALUATE TABLE: "wp_wc_order_tax_lookup"__________________________[ROWS:  2033][PG:   3][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_orders"____________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_orders_meta"_______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_product_attributes_lookup"_________________[ROWS:  2210][PG:   3][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_product_attributes_lookup][0]
	CHUNK ACTION: CURRENT [search_replace][wp_wc_product_attributes_lookup][1]
	CHUNK ACTION: CURRENT [search_replace][wp_wc_product_attributes_lookup][2]

EVALUATE TABLE: "wp_wc_product_download_directories"______________[ROWS:     2][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_product_download_directories][0]

EVALUATE TABLE: "wp_wc_product_meta_lookup"_______________________[ROWS:  4231][PG:   5][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_product_meta_lookup][0]
	CHUNK ACTION: CURRENT [search_replace][wp_wc_product_meta_lookup][1]
	CHUNK ACTION: CURRENT [search_replace][wp_wc_product_meta_lookup][2]
	CHUNK ACTION: CURRENT [search_replace][wp_wc_product_meta_lookup][3]
	CHUNK ACTION: CURRENT [search_replace][wp_wc_product_meta_lookup][4]

EVALUATE TABLE: "wp_wc_rate_limits"_______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_reserved_stock"____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_tax_rate_classes"__________________________[ROWS:     8][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_tax_rate_classes][0]

EVALUATE TABLE: "wp_wc_webhooks"__________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_api_keys"_________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_attribute_taxonomies"_____________[ROWS:     9][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_attribute_taxonomies][0]

EVALUATE TABLE: "wp_woocommerce_bundled_itemmeta"_________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_bundled_items"____________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_downloadable_product_permissions"_[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_log"______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_order_itemmeta"___________________[ROWS: 50414][PG:  51][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][0]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][1]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][2]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][3]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][4]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][5]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][6]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][7]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][8]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][9]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][10]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][11]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][12]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][13]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][14]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][15]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][16]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][17]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][18]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][19]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][20]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][21]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][22]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][23]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][24]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][25]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][26]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][27]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][28]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][29]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][30]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][31]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][32]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][33]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][34]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][35]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][36]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][37]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][38]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][39]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][40]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][41]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][42]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][43]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][44]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][45]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][46]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][47]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][48]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][49]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][50]

EVALUATE TABLE: "wp_woocommerce_order_items"______________________[ROWS:  6847][PG:   7][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_items][0]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_items][1]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_items][2]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_items][3]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_items][4]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_items][5]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_items][6]

EVALUATE TABLE: "wp_woocommerce_payment_tokenmeta"________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_payment_tokens"___________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_sessions"_________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_sessions][0]

EVALUATE TABLE: "wp_woocommerce_shipping_table_rates"_____________[ROWS:     3][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_shipping_table_rates][0]

EVALUATE TABLE: "wp_woocommerce_shipping_zone_locations"__________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_shipping_zone_locations][0]

EVALUATE TABLE: "wp_woocommerce_shipping_zone_methods"____________[ROWS:     3][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_shipping_zone_methods][0]

EVALUATE TABLE: "wp_woocommerce_shipping_zones"___________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_shipping_zones][0]

EVALUATE TABLE: "wp_woocommerce_tax_rate_locations"_______________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_tax_rates"________________________[ROWS:     3][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/sites/unlocked.brandfast.nu/files/public" =======> "/Users/<USER>/Sites/brandfast"
	SEARCH[urlnd]  2:"https://unlocked.brandfast.nu" ===================> "https://brandfast.test"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_tax_rates][0]

EVALUATE TABLE: "wp_wpr_rocket_cache"_____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wpr_rucss_used_css"___________________________[ROWS:     0][PG:   0][SCAN:no columns  ]
--------------------------------------
SCANNED:	Tables:106 	|	 Rows:701791 	|	 Cells:4682750 
UPDATED:	Tables:21 	|	 Rows:32313 	|	 Cells:35673 
ERRORS:		0 
RUNTIME:	12.733700 sec
CHUNK ACTION: CURRENT [rem_maintenance][][]

====================================
REMOVE MAINTENANCE MODE
====================================
MAINTENANCE MODE DISABLE
CHUNK ACTION: CURRENT [rem_licenze_key][][]

====================================
REMOVE LICENSE KEY
====================================
CHUNK ACTION: CURRENT [config_update][][]
SET CONFIG FILES
Retained original entry wpconfig target:/Users/<USER>/Sites/brandfast/wp-config.php
New .htaccess file created:/Users/<USER>/Sites/brandfast/.htaccess

====================================
CONFIGURATION FILE UPDATES
====================================
	UPDATE ABSPATH "__DIR__ . '/wordpress'"
	UPDATE WP_HOME "https://brandfast.test"
	UPDATE WP_SITEURL "https://brandfast.test/wordpress"
	UPDATE WP_CONTENT_DIR "/Users/<USER>/Sites/brandfast"
	UPDATE WP_CONTENT_URL "https://brandfast.test"
	UPDATE WP_PLUGIN_DIR "/Users/<USER>/Sites/brandfast/plugins"
	UPDATE WP_PLUGIN_URL "https://brandfast.test/plugins"
	UPDATE WPMU_PLUGIN_DIR "/Users/<USER>/Sites/brandfast/mu-plugins"
	UPDATE WPMU_PLUGIN_URL "https://brandfast.test/mu-plugins"
	UPDATE DB_NAME ""brandfast""
	UPDATE DB_USER "** OBSCURED **"
	UPDATE DB_PASSWORD "** OBSCURED **"
	UPDATE DB_HOST ""127.0.0.1""
	UPDATE DB_CHARSET "utf8mb4"
	UPDATE DB_COLLATE "utf8mb4_unicode_ci"
	WP CONFIG UPDATE DISALLOW_FILE_EDIT "false"
	WP CONFIG UPDATE DISALLOW_FILE_MODS "false"
	WP CONFIG UPDATE IMAGE_EDIT_OVERWRITE "true"
	WP CONFIG UPDATE WP_CACHE "true"
	WP CONFIG UPDATE WP_POST_REVISIONS "2"
	WP CONFIG UPDATE WP_DEBUG "true"
	WP CONFIG UPDATE WP_DEBUG_LOG "true"
	WP CONFIG REMOVE WP_DISABLE_FATAL_ERROR_HANDLER
	WP CONFIG UPDATE WP_DEBUG_DISPLAY "false"
	WP CONFIG UPDATE SCRIPT_DEBUG "true"
	WP CONFIG UPDATE DISABLE_WP_CRON "true"
	WP CONFIG UPDATE WP_MEMORY_LIMIT "256M"
	WP CONFIG UPDATE WP_MAX_MEMORY_LIMIT "512M"
	WP CONFIG UPDATE AUTOMATIC_UPDATER_DISABLED "true"
	
*** UPDATED WP CONFIG FILE ***

====================================
HTACCESS UPDATE
====================================

WEB SERVER CONFIGURATION FILE UPDATED:
- Preparing .htaccess file with basic setup.
HTACCESS FILE - Successfully updated the .htaccess file setting.

====================================
INDEX.PHP UPDATE
====================================
INDEX.PHP updated with new blog header "__DIR__ . '/wordpress/wp-blog-header.php'"

CHUNK ACTION: CURRENT [gen_update][][]

====================================
GENERAL UPDATES
====================================
UPDATE OPTION home ON TABLE wp_options
UPDATE OPTION siteurl ON TABLE wp_options
UPDATE OPTION duplicator_pro_exe_safe_mode ON TABLE wp_options

====================================
MANAGE PLUGINS
====================================
CHUNK ACTION: CURRENT [gen_clean][][]

====================================
GENERAL CLEANUP
====================================
 - REMOVED 0 storage items
 - REMOVED 0 schedule items
RESET ALL USERS SESSION TOKENS
MIGRATION INFO SET
CHUNK ACTION: CURRENT [create_admin][][]

====================================
RESET USERS PASSWORD
====================================
CHUNK ACTION: CURRENT [notice_test][][]

====================================
CHECK FOR INDEX.HTML
====================================
NO INDEX.HTML WAS FOUND

====================================
NOTICES TEST
====================================
No General Notices Found

CHUNK ACTION: CURRENT [cleanup_tmp_files][][]

====================================
CLEANUP TMP FILES
====================================
CHUNK ACTION: CURRENT [set_files_perms][][]

====================================
SET PARAMS PERMISSION
====================================

*** SET FOLDER PERMISSION AFTER EXTRACTION
--------------------------------------
PATHS MAPPING : "/Users/<USER>/Sites/brandfast"
--------------------------------------
SET FOLDER PERMISSION DONE
CHUNK ACTION: CURRENT [final_report][][]

====================================
FINAL REPORT NOTICES
====================================

STEP-3 COMPLETE @ 01:23:13 - RUNTIME: 2.5584 sec. 


AJAX ACTION [webupdate] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [finalpre] START
AJAX ACTION [finalpre] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

-------------------------
AJAX ACTION [finalafter] START
AJAX ACTION [finalafter] SUCCESS
-------------------------

LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME
STEP ACTION: "on-validate"

====================================
NEXT STEP NOTICES
====================================
-----------------------
[NOTICE] Plugin WPS Hide Login disabled by default
	SECTIONS: plugins
	LONG MSG: This plugin is deactivated by default automatically due to issues that one may encounter when migrating. You must reactivate from the WordPress admin panel after completing the installation or from the plugins tab. Your site's frontend will render properly after reactivating the plugin.

-----------------------
[FATAL ERROR] Fatal error on WordPress login tests!
	SECTIONS: general
	LONG MSG: SCRIPT FILE TEST: /Users/<USER>/Sites/brandfast/wordpress/wp-login.php
EXCEPTION Failed opening required '/Users/<USER>/Sites/brandfast/../vendor/autoload.php' (include_path='.:')
	FILE: /Users/<USER>/Sites/brandfast/wp-config.php[4]
	--- TRACE ---
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wordpress/wp-load.php[55]
	FUNCTION: require                       FILE: /Users/<USER>/Sites/brandfast/wordpress/wp-login.php[12]
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wp_test_script_c411e19-18131607.php[135]
	FUNCTION: require                       FILE: /Applications/Herd.app/Contents/Resources/valet/server.php[167]
-----
For solutions to these issues see the online FAQs 
https://duplicator.com/knowledge-base 


====================================

====================================
FINAL REPORT NOTICES LIST
====================================
-----------------------
[FATAL ERROR] Fatal error on WordPress login tests!
	SECTIONS: general
	LONG MSG: SCRIPT FILE TEST: /Users/<USER>/Sites/brandfast/wordpress/wp-login.php
EXCEPTION Failed opening required '/Users/<USER>/Sites/brandfast/../vendor/autoload.php' (include_path='.:')
	FILE: /Users/<USER>/Sites/brandfast/wp-config.php[4]
	--- TRACE ---
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wordpress/wp-load.php[55]
	FUNCTION: require                       FILE: /Users/<USER>/Sites/brandfast/wordpress/wp-login.php[12]
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wp_test_script_c411e19-18131607.php[135]
	FUNCTION: require                       FILE: /Applications/Herd.app/Contents/Resources/valet/server.php[167]
-----
For solutions to these issues see the online FAQs 
https://duplicator.com/knowledge-base 


-----------------------
[ERROR] Fatal error on WordPress front-end tests!
	SECTIONS: general
	LONG MSG: SCRIPT FILE TEST: /Users/<USER>/Sites/brandfast/index.php
EXCEPTION Failed opening required '/Users/<USER>/Sites/brandfast/../vendor/autoload.php' (include_path='.:')
	FILE: /Users/<USER>/Sites/brandfast/wp-config.php[4]
	--- TRACE ---
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wordpress/wp-load.php[55]
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wordpress/wp-blog-header.php[13]
	FUNCTION: require                       FILE: /Users/<USER>/Sites/brandfast/index.php[5]
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wp_test_script_c411e19-18131607.php[135]
	FUNCTION: require                       FILE: /Applications/Herd.app/Contents/Resources/valet/server.php[167]
-----
For solutions to these issues see the online FAQs 
https://duplicator.com/knowledge-base 


-----------------------
[NOTICE] WPS Hide Login has been deactivated
	SECTIONS: plugins

-----------------------
[NOTICE] Activate WPS Hide Login after you login.
	SECTIONS: plugins

-----------------------
[NOTICE] Warnings or notices on WordPress front-end tests!
	SECTIONS: general
	LONG MSG: SCRIPT FILE TEST: /Users/<USER>/Sites/brandfast/index.php
E_WARNING require(/Users/<USER>/Sites/brandfast/../vendor/autoload.php): Failed to open stream: No such file or directory
	FILE: /Users/<USER>/Sites/brandfast/wp-config.php[4]
	--- TRACE ---
	FUNCTION: require                       FILE: /Users/<USER>/Sites/brandfast/wp-config.php[4]
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wordpress/wp-load.php[55]
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wordpress/wp-blog-header.php[13]
	FUNCTION: require                       FILE: /Users/<USER>/Sites/brandfast/index.php[5]
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wp_test_script_c411e19-18131607.php[135]
	FUNCTION: require                       FILE: /Applications/Herd.app/Contents/Resources/valet/server.php[167]
-----
For solutions to these issues see the online FAQs 
https://duplicator.com/knowledge-base 


-----------------------
[NOTICE] Warnings or notices on WordPress backend tests!
	SECTIONS: general
	LONG MSG: SCRIPT FILE TEST: /Users/<USER>/Sites/brandfast/wordpress/wp-login.php
E_WARNING require(/Users/<USER>/Sites/brandfast/../vendor/autoload.php): Failed to open stream: No such file or directory
	FILE: /Users/<USER>/Sites/brandfast/wp-config.php[4]
	--- TRACE ---
	FUNCTION: require                       FILE: /Users/<USER>/Sites/brandfast/wp-config.php[4]
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wordpress/wp-load.php[55]
	FUNCTION: require                       FILE: /Users/<USER>/Sites/brandfast/wordpress/wp-login.php[12]
	FUNCTION: require_once                  FILE: /Users/<USER>/Sites/brandfast/wp_test_script_c411e19-18131607.php[135]
	FUNCTION: require                       FILE: /Applications/Herd.app/Contents/Resources/valet/server.php[167]
-----
For solutions to these issues see the online FAQs 
https://duplicator.com/knowledge-base 


====================================
LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/ctrls/ctrl.base.php:191][DELTA:   0.01879]  MESSAGE:END RENDER PAGE
LOG-TIME[/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php:76] RESET TIME

SECURITY CHECK:  Security Check Validation - No Token Found[CODE:0]
	FILE:/Users/<USER>/Sites/brandfast/dup-installer/src/Core/Security.php[264]
	TRACE:
#0 /Users/<USER>/Sites/brandfast/dup-installer/src/Core/Bootstrap.php(89): Duplicator\Installer\Core\Security->check()
#1 /Users/<USER>/Sites/brandfast/dup-installer/main.installer.php(42): Duplicator\Installer\Core\Bootstrap::init()
#2 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#3 {main}

