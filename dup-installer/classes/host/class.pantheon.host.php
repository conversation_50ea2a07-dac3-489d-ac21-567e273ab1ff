<?php

/**
 * godaddy custom hosting class
 *
 * Standard: PSR-2
 *
 * @package SC\DUPX\DB
 * @link    http://www.php-fig.org/psr/psr-2/
 */

defined('ABSPATH') || defined('DUPXABSPATH') || exit;

use Duplicator\Installer\Core\Params\PrmMng;

/**
 * class for GoDaddy managed hosting
 *
 * @todo not yet implemneted
 */
class DUPX_Pantheon_Host implements DUPX_Host_interface
{
    /**
     * return the current host itentifier
     *
     * @return string
     */
    public static function getIdentifier(): string
    {
        return DUPX_Custom_Host_Manager::HOST_PANTHEON;
    }

    /**
     * @return bool true if is current host
     * @throws Exception
     */
    public function isHosting(): bool
    {
        // check only mu plugin file exists

        $testFile = PrmMng::getInstance()->getValue(PrmMng::PARAM_PATH_MUPLUGINS_NEW) . '/pantheon.php';
        return file_exists($testFile);
    }

    /**
     * the init function.
     * is called only if isHosting is true
     *
     * @return void
     */
    public function init(): void
    {
    }

    /**
     *
     * @return string
     */
    public function getLabel(): string
    {
        return 'Pantheon';
    }

    /**
     * this function is called if current hosting is this
     *
     * @return void
     */
    public function setCustomParams(): void
    {
    }
}
