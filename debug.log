[18-Sep-2025 13:36:14 UTC] RedisException: Connection refused in /Users/<USER>/Sites/brandfast/object-cache.php:740
Stack trace:
#0 /Users/<USER>/Sites/brandfast/object-cache.php(740): Redis->connect('127.0.0.1', 6379, 5, '', 0, 5)
#1 /Users/<USER>/Sites/brandfast/object-cache.php(547): WP_Object_Cache->connect_using_phpredis(Array)
#2 /Users/<USER>/Sites/brandfast/object-cache.php(247): WP_Object_Cache->__construct(false)
#3 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/load.php(893): wp_cache_init()
#4 /Users/<USER>/Sites/brandfast/wordpress/wp-settings.php(147): wp_start_object_cache()
#5 /Users/<USER>/Sites/brandfast/wp-config.php(128): require_once('/Users/<USER>')
#6 /Users/<USER>/Sites/brandfast/wordpress/wp-load.php(55): require_once('/Users/<USER>')
#7 /Users/<USER>/Sites/brandfast/wordpress/wp-blog-header.php(13): require_once('/Users/<USER>')
#8 /Users/<USER>/Sites/brandfast/index.php(5): require('/Users/<USER>')
#9 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#10 {main}
[18-Sep-2025 13:36:14 UTC] PHP Fatal error:  Uncaught Error: Call to a member function get() on null in /Users/<USER>/Sites/brandfast/object-cache.php:194
Stack trace:
#0 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/class-wp-textdomain-registry.php(204): wp_cache_get('9ed72b14c7a348b...', 'translation_fil...')
#1 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/class-wp-textdomain-registry.php(319): WP_Textdomain_Registry->get_language_files_from_path('/Users/<USER>')
#2 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/class-wp-textdomain-registry.php(94): WP_Textdomain_Registry->get_path_from_lang_dir('redis-cache', 'en_US')
#3 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/l10n.php(1365): WP_Textdomain_Registry->get('redis-cache', 'en_US')
#4 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/l10n.php(1409): _load_textdomain_just_in_time('redis-cache')
#5 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/l10n.php(195): get_translations_for_domain('redis-cache')
#6 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/l10n.php(307): translate('Error establish...', 'redis-cache')
#7 /Users/<USER>/Sites/brandfast/object-cache.php(2932): __('Error establish...', 'redis-cache')
#8 /Users/<USER>/Sites/brandfast/object-cache.php(2904): WP_Object_Cache->show_error_and_die(Object(RedisException))
#9 /Users/<USER>/Sites/brandfast/object-cache.php(577): WP_Object_Cache->handle_exception(Object(RedisException))
#10 /Users/<USER>/Sites/brandfast/object-cache.php(247): WP_Object_Cache->__construct(false)
#11 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/load.php(893): wp_cache_init()
#12 /Users/<USER>/Sites/brandfast/wordpress/wp-settings.php(147): wp_start_object_cache()
#13 /Users/<USER>/Sites/brandfast/wp-config.php(128): require_once('/Users/<USER>')
#14 /Users/<USER>/Sites/brandfast/wordpress/wp-load.php(55): require_once('/Users/<USER>')
#15 /Users/<USER>/Sites/brandfast/wordpress/wp-blog-header.php(13): require_once('/Users/<USER>')
#16 /Users/<USER>/Sites/brandfast/index.php(5): require('/Users/<USER>')
#17 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#18 {main}
  thrown in /Users/<USER>/Sites/brandfast/object-cache.php on line 194
[18-Sep-2025 13:37:16 UTC] RedisException: Connection refused in /Users/<USER>/Sites/brandfast/object-cache.php:740
Stack trace:
#0 /Users/<USER>/Sites/brandfast/object-cache.php(740): Redis->connect('127.0.0.1', 6379, 5, '', 0, 5)
#1 /Users/<USER>/Sites/brandfast/object-cache.php(547): WP_Object_Cache->connect_using_phpredis(Array)
#2 /Users/<USER>/Sites/brandfast/object-cache.php(247): WP_Object_Cache->__construct(false)
#3 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/load.php(893): wp_cache_init()
#4 /Users/<USER>/Sites/brandfast/wordpress/wp-settings.php(147): wp_start_object_cache()
#5 /Users/<USER>/Sites/brandfast/wp-config.php(128): require_once('/Users/<USER>')
#6 /Users/<USER>/Sites/brandfast/wordpress/wp-load.php(55): require_once('/Users/<USER>')
#7 /Users/<USER>/Sites/brandfast/wordpress/wp-blog-header.php(13): require_once('/Users/<USER>')
#8 /Users/<USER>/Sites/brandfast/index.php(5): require('/Users/<USER>')
#9 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#10 {main}
[18-Sep-2025 13:37:16 UTC] PHP Fatal error:  Uncaught Error: Call to a member function get() on null in /Users/<USER>/Sites/brandfast/object-cache.php:194
Stack trace:
#0 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/class-wp-textdomain-registry.php(204): wp_cache_get('9ed72b14c7a348b...', 'translation_fil...')
#1 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/class-wp-textdomain-registry.php(319): WP_Textdomain_Registry->get_language_files_from_path('/Users/<USER>')
#2 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/class-wp-textdomain-registry.php(94): WP_Textdomain_Registry->get_path_from_lang_dir('redis-cache', 'en_US')
#3 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/l10n.php(1365): WP_Textdomain_Registry->get('redis-cache', 'en_US')
#4 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/l10n.php(1409): _load_textdomain_just_in_time('redis-cache')
#5 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/l10n.php(195): get_translations_for_domain('redis-cache')
#6 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/l10n.php(307): translate('Error establish...', 'redis-cache')
#7 /Users/<USER>/Sites/brandfast/object-cache.php(2932): __('Error establish...', 'redis-cache')
#8 /Users/<USER>/Sites/brandfast/object-cache.php(2904): WP_Object_Cache->show_error_and_die(Object(RedisException))
#9 /Users/<USER>/Sites/brandfast/object-cache.php(577): WP_Object_Cache->handle_exception(Object(RedisException))
#10 /Users/<USER>/Sites/brandfast/object-cache.php(247): WP_Object_Cache->__construct(false)
#11 /Users/<USER>/Sites/brandfast/wordpress/wp-includes/load.php(893): wp_cache_init()
#12 /Users/<USER>/Sites/brandfast/wordpress/wp-settings.php(147): wp_start_object_cache()
#13 /Users/<USER>/Sites/brandfast/wp-config.php(128): require_once('/Users/<USER>')
#14 /Users/<USER>/Sites/brandfast/wordpress/wp-load.php(55): require_once('/Users/<USER>')
#15 /Users/<USER>/Sites/brandfast/wordpress/wp-blog-header.php(13): require_once('/Users/<USER>')
#16 /Users/<USER>/Sites/brandfast/index.php(5): require('/Users/<USER>')
#17 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#18 {main}
  thrown in /Users/<USER>/Sites/brandfast/object-cache.php on line 194
